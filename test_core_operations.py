#!/usr/bin/env python3
"""
Test core Appium operations that are available in the simplified interface
"""

from emulator_manager import Emulator<PERSON>anager
from appium_driver_enhanced import AppiumDriverEnhanced

def test_core_operations():
    print("🎯 Testing Core Appium Operations")
    print("=" * 40)
    
    # Initialize components
    emulator_manager = EmulatorManager()
    appium_driver = AppiumDriverEnhanced()
    
    # Discover emulators
    print("1️⃣ Discovering emulators...")
    emulators = emulator_manager.discover_emulators()
    running_emulators = [e for e in emulators if e.running]
    
    if not running_emulators:
        print("❌ No running emulators found")
        return False
    
    target_emulator = running_emulators[0]  # Use first running emulator
    print(f"✅ Target emulator: {target_emulator.name} ({target_emulator.adb_id})")
    
    # Test core operations
    operations_tested = []
    
    # 1. Connect to Device
    print("\n2️⃣ Testing: Connect to Device")
    try:
        success, diagnostics = appium_driver.connect_to_device(target_emulator.adb_id)
        if success:
            print("✅ Connect to Device - SUCCESS")
            operations_tested.append("Connect to Device ✅")
        else:
            print(f"❌ Connect to Device - FAILED: {diagnostics.error_message}")
            operations_tested.append("Connect to Device ❌")
            return False
    except Exception as e:
        print(f"❌ Connect to Device - ERROR: {str(e)}")
        operations_tested.append("Connect to Device ❌")
        return False
    
    # 2. Get Device Info
    print("\n3️⃣ Testing: Get Device Info")
    try:
        if appium_driver.is_connected():
            device_info = {
                "Device Name": target_emulator.device_name,
                "API Level": target_emulator.api_level,
                "ADB ID": target_emulator.adb_id
            }
            print("✅ Get Device Info - SUCCESS")
            print(f"   Device: {device_info['Device Name']}")
            print(f"   API Level: {device_info['API Level']}")
            operations_tested.append("Get Device Info ✅")
        else:
            print("❌ Get Device Info - Device not connected")
            operations_tested.append("Get Device Info ❌")
    except Exception as e:
        print(f"❌ Get Device Info - ERROR: {str(e)}")
        operations_tested.append("Get Device Info ❌")
    
    # 3. Take Screenshot
    print("\n4️⃣ Testing: Take Screenshot")
    try:
        screenshot = appium_driver.take_screenshot()
        if screenshot:
            print(f"✅ Take Screenshot - SUCCESS ({len(screenshot)} chars)")
            operations_tested.append("Take Screenshot ✅")
        else:
            print("❌ Take Screenshot - FAILED")
            operations_tested.append("Take Screenshot ❌")
    except Exception as e:
        print(f"❌ Take Screenshot - ERROR: {str(e)}")
        operations_tested.append("Take Screenshot ❌")
    
    # 4. Press Home Button
    print("\n5️⃣ Testing: Press Home Button")
    try:
        appium_driver.press_home()
        print("✅ Press Home Button - SUCCESS")
        operations_tested.append("Press Home Button ✅")
    except Exception as e:
        print(f"❌ Press Home Button - ERROR: {str(e)}")
        operations_tested.append("Press Home Button ❌")
    
    # 5. Get Page Source
    print("\n6️⃣ Testing: Get Page Source")
    try:
        if appium_driver.driver:
            page_source = appium_driver.driver.page_source
            if page_source:
                lines = len(page_source.split('\n'))
                print(f"✅ Get Page Source - SUCCESS ({lines} lines)")
                operations_tested.append("Get Page Source ✅")
            else:
                print("❌ Get Page Source - Empty response")
                operations_tested.append("Get Page Source ❌")
        else:
            print("❌ Get Page Source - No driver")
            operations_tested.append("Get Page Source ❌")
    except Exception as e:
        print(f"❌ Get Page Source - ERROR: {str(e)}")
        operations_tested.append("Get Page Source ❌")
    
    # 6. Disconnect from Device
    print("\n7️⃣ Testing: Disconnect from Device")
    try:
        appium_driver.disconnect()
        print("✅ Disconnect from Device - SUCCESS")
        operations_tested.append("Disconnect from Device ✅")
    except Exception as e:
        print(f"❌ Disconnect from Device - ERROR: {str(e)}")
        operations_tested.append("Disconnect from Device ❌")
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 CORE OPERATIONS TEST SUMMARY")
    print("=" * 40)
    
    for operation in operations_tested:
        print(f"   {operation}")
    
    successful_ops = len([op for op in operations_tested if "✅" in op])
    total_ops = len(operations_tested)
    
    print(f"\n✅ Success Rate: {successful_ops}/{total_ops} ({successful_ops/total_ops*100:.1f}%)")
    
    if successful_ops >= 4:  # At least 4 core operations working
        print("\n🎉 CORE OPERATIONS WORKING!")
        print("Your simplified dropdown interface is fully functional!")
        return True
    else:
        print("\n⚠️ Some core operations need attention")
        return False

if __name__ == "__main__":
    success = test_core_operations()
    
    if success:
        print("\n🌟 SIMPLIFIED INTERFACE READY FOR USE!")
        print("🌐 Dashboard URL: http://localhost:8501")
        print("📱 Navigate to: Emulator Testing")
        print("🎯 Use dropdown menus for clean, simple Appium operations")
    else:
        print("\n💡 Some operations may need troubleshooting")
