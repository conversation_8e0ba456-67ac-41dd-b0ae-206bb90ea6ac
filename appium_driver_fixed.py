
def connect_to_device_fixed(self, device_id: str) -> <PERSON><PERSON>[bool, 'ConnectionDiagnostics']:
    """Fixed connection method with proper Appium 2.x support"""
    
    diagnostics = ConnectionDiagnostics()
    
    try:
        # Clean up any existing connection
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
        
        # Check Appium server
        if not self.check_appium_server():
            diagnostics.error_message = "Appium server not running"
            diagnostics.suggestions.append("Start Appium server: appium --port 4723")
            return False, diagnostics
        
        # Set up proper capabilities for Appium 2.x
        from appium import webdriver
        from appium.options.android import UiAutomator2Options
        
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = device_id
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.no_reset = True
        options.full_reset = False
        
        # Connect
        self.driver = webdriver.Remote("http://localhost:4723", options=options)
        self.device_id = device_id
        self.connected = True
        
        # Test connection
        window_size = self.driver.get_window_size()
        current_activity = self.driver.current_activity
        
        diagnostics.success = True
        diagnostics.connection_time = 2.0  # Approximate
        
        return True, diagnostics
        
    except Exception as e:
        diagnostics.error_message = str(e)
        diagnostics.suggestions.append("Check emulator is running and responsive")
        diagnostics.suggestions.append("Restart Appium server if needed")
        return False, diagnostics
