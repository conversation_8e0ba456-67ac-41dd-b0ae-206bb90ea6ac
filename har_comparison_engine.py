"""
Advanced HAR File Comparison Engine
Comprehensive comparison system for HAR files with filtering, analysis, and visualization
"""

import json
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import numpy as np
from urllib.parse import urlparse
import difflib
import hashlib

@dataclass
class ComparisonResult:
    """Result of HAR file comparison"""
    common_endpoints: int
    unique_file1: int
    unique_file2: int
    response_time_diff: float
    status_code_changes: int
    content_changes: int
    header_changes: int
    detailed_differences: List[Dict[str, Any]]
    summary_stats: Dict[str, Any]
    performance_analysis: Dict[str, Any]
    security_analysis: Dict[str, Any]

@dataclass
class EndpointComparison:
    """Comparison result for a specific endpoint"""
    url: str
    method: str
    file1_present: bool
    file2_present: bool
    response_time_diff: Optional[float]
    status_code_diff: Optional[Tuple[int, int]]
    content_diff: Optional[str]
    headers_diff: Optional[Dict[str, Any]]
    size_diff: Optional[int]

class HARComparisonEngine:
    """Advanced HAR file comparison engine"""
    
    def __init__(self):
        self.supported_formats = ['.har', '.json', '.chlsj']
        self.domain_filters = []
        self.resource_type_filters = []
        self.comparison_options = {
            'compare_headers': True,
            'compare_content': True,
            'compare_timing': True,
            'ignore_dynamic_params': True,
            'case_sensitive_urls': False
        }
    
    def parse_har_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse HAR file content into standardized format"""
        try:
            content_str = file_content.decode('utf-8')
            data = json.loads(content_str)
            
            # Detect format and extract entries
            entries = []
            
            if 'log' in data and 'entries' in data['log']:
                # Standard HAR format
                entries = data['log']['entries']
                format_type = 'HAR'
            elif 'entries' in data:
                # Charles session format
                entries = data['entries']
                format_type = 'Charles Session'
            elif isinstance(data, list):
                # Direct array of entries
                entries = data
                format_type = 'Direct Array'
            else:
                # Single entry
                entries = [data]
                format_type = 'Single Entry'
            
            # Normalize entries to standard format
            normalized_entries = []
            for entry in entries:
                normalized = self._normalize_entry(entry)
                if normalized:
                    normalized_entries.append(normalized)
            
            return {
                'entries': normalized_entries,
                'format': format_type,
                'filename': filename,
                'total_entries': len(normalized_entries),
                'parsed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise ValueError(f"Failed to parse {filename}: {str(e)}")
    
    def _normalize_entry(self, entry: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Normalize entry to standard format"""
        try:
            # Handle different entry formats
            if 'request' in entry and 'response' in entry:
                # Standard HAR format
                request = entry['request']
                response = entry['response']
                timing = entry.get('timings', {})
            elif 'method' in entry and 'url' in entry:
                # Charles format
                request = {
                    'method': entry.get('method', 'GET'),
                    'url': entry.get('url', ''),
                    'headers': entry.get('request_headers', []),
                    'queryString': [],
                    'postData': entry.get('request_body', {})
                }
                response = {
                    'status': entry.get('status', 0),
                    'statusText': entry.get('status_text', ''),
                    'headers': entry.get('response_headers', []),
                    'content': entry.get('response_body', {}),
                    'bodySize': entry.get('response_size', 0)
                }
                timing = {
                    'wait': entry.get('duration', 0),
                    'receive': 0
                }
            else:
                return None
            
            # Extract key information
            url = request.get('url', '') or ''
            parsed_url = urlparse(url) if url else None
            
            normalized = {
                'url': url,
                'domain': parsed_url.netloc if parsed_url else '',
                'path': parsed_url.path if parsed_url else '',
                'method': request.get('method', 'GET'),
                'status_code': response.get('status', 0),
                'response_time': timing.get('wait', 0) + timing.get('receive', 0),
                'response_size': response.get('bodySize', 0),
                'request_headers': self._normalize_headers(request.get('headers', [])),
                'response_headers': self._normalize_headers(response.get('headers', [])),
                'request_body': request.get('postData', {}),
                'response_body': response.get('content', {}),
                'timestamp': entry.get('startedDateTime', datetime.now().isoformat()),
                'resource_type': self._detect_resource_type(url, response.get('content', {}))
            }
            
            return normalized
            
        except Exception as e:
            print(f"Error normalizing entry: {e}")
            return None
    
    def _normalize_headers(self, headers: List[Dict[str, str]]) -> Dict[str, str]:
        """Normalize headers to dict format"""
        if isinstance(headers, dict):
            return headers
        elif isinstance(headers, list):
            return {h.get('name', ''): h.get('value', '') for h in headers if 'name' in h}
        else:
            return {}
    
    def _detect_resource_type(self, url: str, content: Dict[str, Any]) -> str:
        """Detect resource type from URL and content"""
        url_lower = (url or '').lower()
        content_type = (content.get('mimeType', '') or '').lower()
        
        if 'image' in content_type or any(ext in url_lower for ext in ['.jpg', '.png', '.gif', '.svg']):
            return 'image'
        elif 'javascript' in content_type or '.js' in url_lower:
            return 'script'
        elif 'css' in content_type or '.css' in url_lower:
            return 'stylesheet'
        elif 'json' in content_type or 'api' in url_lower:
            return 'xhr'
        elif 'html' in content_type:
            return 'document'
        else:
            return 'other'
    
    def apply_filters(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply domain and resource type filters"""
        filtered = entries
        
        # Apply domain filters
        if self.domain_filters:
            filtered = [e for e in filtered if any(domain in e['domain'] for domain in self.domain_filters)]
        
        # Apply resource type filters
        if self.resource_type_filters:
            filtered = [e for e in filtered if e['resource_type'] in self.resource_type_filters]
        
        return filtered
    
    def compare_files(self, file1_data: Dict[str, Any], file2_data: Dict[str, Any]) -> ComparisonResult:
        """Compare two HAR files comprehensively"""
        entries1 = self.apply_filters(file1_data['entries'])
        entries2 = self.apply_filters(file2_data['entries'])
        
        # Create endpoint maps for comparison
        endpoints1 = self._create_endpoint_map(entries1)
        endpoints2 = self._create_endpoint_map(entries2)
        
        # Find common and unique endpoints
        all_endpoints = set(endpoints1.keys()) | set(endpoints2.keys())
        common_endpoints = set(endpoints1.keys()) & set(endpoints2.keys())
        unique_file1 = set(endpoints1.keys()) - set(endpoints2.keys())
        unique_file2 = set(endpoints2.keys()) - set(endpoints1.keys())
        
        # Detailed comparison
        detailed_differences = []
        response_time_diffs = []
        status_changes = 0
        content_changes = 0
        header_changes = 0
        
        for endpoint in all_endpoints:
            entry1 = endpoints1.get(endpoint)
            entry2 = endpoints2.get(endpoint)
            
            comparison = EndpointComparison(
                url=endpoint.split('|')[1],
                method=endpoint.split('|')[0],
                file1_present=entry1 is not None,
                file2_present=entry2 is not None,
                response_time_diff=None,
                status_code_diff=None,
                content_diff=None,
                headers_diff=None,
                size_diff=None
            )
            
            if entry1 and entry2:
                # Compare response times
                time_diff = entry2['response_time'] - entry1['response_time']
                comparison.response_time_diff = time_diff
                response_time_diffs.append(time_diff)
                
                # Compare status codes
                if entry1['status_code'] != entry2['status_code']:
                    comparison.status_code_diff = (entry1['status_code'], entry2['status_code'])
                    status_changes += 1
                
                # Compare content
                if self.comparison_options['compare_content']:
                    content_diff = self._compare_content(entry1['response_body'], entry2['response_body'])
                    if content_diff:
                        comparison.content_diff = content_diff
                        content_changes += 1
                
                # Compare headers
                if self.comparison_options['compare_headers']:
                    headers_diff = self._compare_headers(entry1['response_headers'], entry2['response_headers'])
                    if headers_diff:
                        comparison.headers_diff = headers_diff
                        header_changes += 1
                
                # Compare sizes
                size_diff = entry2['response_size'] - entry1['response_size']
                comparison.size_diff = size_diff
            
            detailed_differences.append(comparison.__dict__)
        
        # Calculate summary statistics
        avg_response_time_diff = np.mean(response_time_diffs) if response_time_diffs else 0
        
        # Performance analysis
        performance_analysis = self._analyze_performance(entries1, entries2)
        
        # Security analysis
        security_analysis = self._analyze_security(entries1, entries2)
        
        # Summary stats
        summary_stats = {
            'file1_total_requests': len(entries1),
            'file2_total_requests': len(entries2),
            'common_endpoints_count': len(common_endpoints),
            'unique_file1_count': len(unique_file1),
            'unique_file2_count': len(unique_file2),
            'avg_response_time_diff': avg_response_time_diff,
            'total_changes': status_changes + content_changes + header_changes
        }
        
        return ComparisonResult(
            common_endpoints=len(common_endpoints),
            unique_file1=len(unique_file1),
            unique_file2=len(unique_file2),
            response_time_diff=avg_response_time_diff,
            status_code_changes=status_changes,
            content_changes=content_changes,
            header_changes=header_changes,
            detailed_differences=detailed_differences,
            summary_stats=summary_stats,
            performance_analysis=performance_analysis,
            security_analysis=security_analysis
        )
    
    def _create_endpoint_map(self, entries: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Create a map of endpoints for comparison"""
        endpoint_map = {}
        for entry in entries:
            # Create unique key for endpoint
            key = f"{entry['method']}|{entry['url']}"
            if self.comparison_options['ignore_dynamic_params']:
                # Remove query parameters for comparison
                key = f"{entry['method']}|{entry['url'].split('?')[0]}"
            
            endpoint_map[key] = entry
        
        return endpoint_map

    def _compare_content(self, content1: Dict[str, Any], content2: Dict[str, Any]) -> Optional[str]:
        """Compare response content"""
        try:
            text1 = content1.get('text', '')
            text2 = content2.get('text', '')

            if text1 == text2:
                return None

            # Generate diff
            diff = list(difflib.unified_diff(
                text1.splitlines(keepends=True),
                text2.splitlines(keepends=True),
                fromfile='File 1',
                tofile='File 2',
                n=3
            ))

            return ''.join(diff) if diff else None

        except Exception:
            return "Content comparison failed"

    def _compare_headers(self, headers1: Dict[str, str], headers2: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Compare response headers"""
        try:
            added = {}
            removed = {}
            changed = {}

            all_keys = set(headers1.keys()) | set(headers2.keys())

            for key in all_keys:
                if key in headers1 and key in headers2:
                    if headers1[key] != headers2[key]:
                        changed[key] = {'old': headers1[key], 'new': headers2[key]}
                elif key in headers1:
                    removed[key] = headers1[key]
                else:
                    added[key] = headers2[key]

            if added or removed or changed:
                return {'added': added, 'removed': removed, 'changed': changed}

            return None

        except Exception:
            return {'error': 'Header comparison failed'}

    def _analyze_performance(self, entries1: List[Dict[str, Any]], entries2: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance differences"""
        try:
            # Calculate performance metrics for both files
            times1 = [e['response_time'] for e in entries1 if e['response_time'] > 0]
            times2 = [e['response_time'] for e in entries2 if e['response_time'] > 0]

            analysis = {
                'file1_avg_response_time': np.mean(times1) if times1 else 0,
                'file2_avg_response_time': np.mean(times2) if times2 else 0,
                'file1_median_response_time': np.median(times1) if times1 else 0,
                'file2_median_response_time': np.median(times2) if times2 else 0,
                'file1_95th_percentile': np.percentile(times1, 95) if times1 else 0,
                'file2_95th_percentile': np.percentile(times2, 95) if times2 else 0,
                'performance_regression': False,
                'slow_endpoints': []
            }

            # Check for performance regression
            if analysis['file2_avg_response_time'] > analysis['file1_avg_response_time'] * 1.2:
                analysis['performance_regression'] = True

            # Find slow endpoints
            slow_threshold = 2000  # 2 seconds
            for entry in entries2:
                if entry['response_time'] > slow_threshold:
                    analysis['slow_endpoints'].append({
                        'url': entry['url'],
                        'method': entry['method'],
                        'response_time': entry['response_time']
                    })

            return analysis

        except Exception as e:
            return {'error': f'Performance analysis failed: {str(e)}'}

    def _analyze_security(self, entries1: List[Dict[str, Any]], entries2: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze security differences"""
        try:
            analysis = {
                'insecure_requests': [],
                'security_headers_missing': [],
                'status_code_security_issues': [],
                'potential_vulnerabilities': []
            }

            # Check for insecure HTTP requests
            for entry in entries1 + entries2:
                if entry['url'].startswith('http://'):
                    analysis['insecure_requests'].append(entry['url'])

            # Check for missing security headers
            security_headers = ['X-Frame-Options', 'X-Content-Type-Options', 'X-XSS-Protection', 'Strict-Transport-Security']
            for entry in entries1 + entries2:
                missing_headers = []
                for header in security_headers:
                    if header not in entry['response_headers']:
                        missing_headers.append(header)

                if missing_headers:
                    analysis['security_headers_missing'].append({
                        'url': entry['url'],
                        'missing_headers': missing_headers
                    })

            # Check for security-related status codes
            for entry in entries1 + entries2:
                if entry['status_code'] in [401, 403, 500, 502, 503]:
                    analysis['status_code_security_issues'].append({
                        'url': entry['url'],
                        'status_code': entry['status_code']
                    })

            return analysis

        except Exception as e:
            return {'error': f'Security analysis failed: {str(e)}'}

    def set_domain_filters(self, domains: List[str]):
        """Set domain filters for comparison"""
        self.domain_filters = domains

    def set_resource_type_filters(self, resource_types: List[str]):
        """Set resource type filters for comparison"""
        self.resource_type_filters = resource_types

    def set_comparison_options(self, options: Dict[str, bool]):
        """Set comparison options"""
        self.comparison_options.update(options)

    def generate_comparison_report(self, result: ComparisonResult, file1_name: str, file2_name: str) -> Dict[str, Any]:
        """Generate comprehensive comparison report"""
        report = {
            'metadata': {
                'file1_name': file1_name,
                'file2_name': file2_name,
                'comparison_timestamp': datetime.now().isoformat(),
                'filters_applied': {
                    'domains': self.domain_filters,
                    'resource_types': self.resource_type_filters
                }
            },
            'executive_summary': {
                'total_endpoints_compared': result.common_endpoints,
                'unique_to_file1': result.unique_file1,
                'unique_to_file2': result.unique_file2,
                'performance_impact': 'Improved' if result.response_time_diff < 0 else 'Degraded' if result.response_time_diff > 0 else 'No Change',
                'breaking_changes': result.status_code_changes,
                'content_modifications': result.content_changes
            },
            'detailed_analysis': {
                'performance': result.performance_analysis,
                'security': result.security_analysis,
                'summary_statistics': result.summary_stats
            },
            'recommendations': self._generate_recommendations(result),
            'detailed_differences': result.detailed_differences[:100]  # Limit for performance
        }

        return report

    def _generate_recommendations(self, result: ComparisonResult) -> List[str]:
        """Generate recommendations based on comparison results"""
        recommendations = []

        if result.response_time_diff > 500:  # 500ms degradation
            recommendations.append("⚠️ Significant performance degradation detected. Review slow endpoints and optimize.")

        if result.status_code_changes > 0:
            recommendations.append("🔍 Status code changes detected. Verify API compatibility and error handling.")

        if result.content_changes > result.common_endpoints * 0.5:
            recommendations.append("📝 High number of content changes. Review API response modifications.")

        if result.security_analysis.get('insecure_requests'):
            recommendations.append("🔒 Insecure HTTP requests found. Migrate to HTTPS for security.")

        if result.unique_file2 > result.unique_file1:
            recommendations.append("📈 New endpoints detected. Update documentation and tests.")

        if not recommendations:
            recommendations.append("✅ No major issues detected. Changes appear to be within acceptable limits.")

        return recommendations
