#!/usr/bin/env python3
"""
QA Integrated Dashboard Functions
Supporting functions for the main QA dashboard
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

def create_log_dataframe(log_data: Dict[str, Any]) -> pd.DataFrame:
    """Create a pandas DataFrame from Charles Proxy log data"""
    try:
        entries = log_data.get('entries', [])
        
        if not entries:
            st.warning("No entries found in the log file")
            return pd.DataFrame()
        
        # Extract relevant data from each entry
        processed_entries = []
        
        for entry in entries:
            try:
                request = entry.get('request', {})
                response = entry.get('response', {})
                timings = entry.get('timings', {})
                
                processed_entry = {
                    'url': request.get('url', ''),
                    'method': request.get('method', ''),
                    'status': response.get('status', 0),
                    'status_text': response.get('statusText', ''),
                    'time': timings.get('wait', 0) + timings.get('receive', 0),
                    'size': response.get('bodySize', 0),
                    'mime_type': response.get('content', {}).get('mimeType', ''),
                    'started_datetime': entry.get('startedDateTime', ''),
                    'host': extract_host_from_url(request.get('url', '')),
                    'path': extract_path_from_url(request.get('url', '')),
                    'query_params': len(request.get('queryString', [])),
                    'request_headers': len(request.get('headers', [])),
                    'response_headers': len(response.get('headers', [])),
                    'cache_status': response.get('cache', {}).get('beforeRequest', {}).get('hitCount', 0) > 0
                }
                
                processed_entries.append(processed_entry)
                
            except Exception as e:
                st.warning(f"Error processing entry: {str(e)}")
                continue
        
        # Create DataFrame
        df = pd.DataFrame(processed_entries)
        
        if not df.empty:
            # Convert datetime column
            if 'started_datetime' in df.columns:
                try:
                    df['started_datetime'] = pd.to_datetime(df['started_datetime'])
                except:
                    pass
            
            # Store in session state
            st.session_state.log_df = df
            
        return df
        
    except Exception as e:
        st.error(f"Error creating DataFrame: {str(e)}")
        return pd.DataFrame()

def extract_host_from_url(url: str) -> str:
    """Extract host from URL"""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.netloc
    except:
        return ""

def extract_path_from_url(url: str) -> str:
    """Extract path from URL"""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.path
    except:
        return ""

def render_dashboard_tab():
    """Render the main dashboard tab"""
    st.markdown('<h2>Dashboard Overview</h2>', unsafe_allow_html=True)
    
    if st.session_state.log_df is not None and not st.session_state.log_df.empty:
        df = st.session_state.log_df
        
        # Key metrics
        st.markdown('<h3>Key Metrics</h3>', unsafe_allow_html=True)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_requests = len(df)
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{total_requests}</div>
                <div class="metric-label">Total Requests</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            success_count = len(df[df["status"].between(200, 299)])
            success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{success_rate:.1f}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            unique_hosts = df['host'].nunique()
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{unique_hosts}</div>
                <div class="metric-label">Unique Hosts</div>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            avg_response_time = df['time'].mean()
            st.markdown(f"""
            <div class="metric-card">
                <div class="metric-value">{avg_response_time:.0f}ms</div>
                <div class="metric-label">Avg Response Time</div>
            </div>
            """, unsafe_allow_html=True)
        
        # Charts
        st.markdown('<h3>Request Analysis</h3>', unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Status code distribution
            status_counts = df['status'].value_counts().head(10)
            fig_status = px.bar(
                x=status_counts.index.astype(str),
                y=status_counts.values,
                title="Status Code Distribution",
                labels={'x': 'Status Code', 'y': 'Count'}
            )
            fig_status.update_layout(showlegend=False)
            st.plotly_chart(fig_status, use_container_width=True)
        
        with col2:
            # Top hosts
            host_counts = df['host'].value_counts().head(10)
            fig_hosts = px.bar(
                x=host_counts.values,
                y=host_counts.index,
                orientation='h',
                title="Top Hosts by Request Count",
                labels={'x': 'Request Count', 'y': 'Host'}
            )
            fig_hosts.update_layout(showlegend=False)
            st.plotly_chart(fig_hosts, use_container_width=True)
        
        # Response time analysis
        st.markdown('<h3>Performance Analysis</h3>', unsafe_allow_html=True)
        
        # Response time histogram
        fig_time = px.histogram(
            df,
            x='time',
            nbins=50,
            title="Response Time Distribution",
            labels={'time': 'Response Time (ms)', 'count': 'Frequency'}
        )
        fig_time.update_layout(showlegend=False)
        st.plotly_chart(fig_time, use_container_width=True)
        
        # Slow requests
        slow_threshold = st.slider("Slow request threshold (ms)", 1000, 10000, 5000)
        slow_requests = df[df['time'] > slow_threshold]
        
        if not slow_requests.empty:
            st.markdown(f'<h4>Slow Requests (>{slow_threshold}ms)</h4>', unsafe_allow_html=True)
            st.dataframe(
                slow_requests[['method', 'url', 'status', 'time']].sort_values('time', ascending=False),
                use_container_width=True
            )
        
    else:
        st.info("No log data available. Please upload a Charles Proxy log file using the sidebar.")

def render_request_details_tab():
    """Render the request details tab"""
    st.markdown('<h2>Request Details</h2>', unsafe_allow_html=True)
    
    if st.session_state.log_df is not None and not st.session_state.log_df.empty:
        df = st.session_state.log_df
        
        # Filters
        st.markdown('<h3>Filters</h3>', unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # Status filter
            status_options = sorted(df['status'].unique())
            selected_status = st.multiselect(
                "Filter by Status Code",
                options=status_options,
                default=st.session_state.filter_settings.get('status_filter', [])
            )
            st.session_state.filter_settings['status_filter'] = selected_status
        
        with col2:
            # Host filter
            host_options = sorted(df['host'].unique())
            selected_hosts = st.multiselect(
                "Filter by Host",
                options=host_options,
                default=st.session_state.filter_settings.get('host_filter', [])
            )
            st.session_state.filter_settings['host_filter'] = selected_hosts
        
        with col3:
            # Method filter
            method_options = sorted(df['method'].unique())
            selected_methods = st.multiselect(
                "Filter by Method",
                options=method_options,
                default=st.session_state.filter_settings.get('method_filter', [])
            )
            st.session_state.filter_settings['method_filter'] = selected_methods
        
        # Search
        search_query = st.text_input(
            "Search in URLs",
            value=st.session_state.filter_settings.get('search_query', '')
        )
        st.session_state.filter_settings['search_query'] = search_query
        
        # Apply filters
        filtered_df = df.copy()
        
        if selected_status:
            filtered_df = filtered_df[filtered_df['status'].isin(selected_status)]
        
        if selected_hosts:
            filtered_df = filtered_df[filtered_df['host'].isin(selected_hosts)]
        
        if selected_methods:
            filtered_df = filtered_df[filtered_df['method'].isin(selected_methods)]
        
        if search_query:
            filtered_df = filtered_df[filtered_df['url'].str.contains(search_query, case=False, na=False)]
        
        # Display results
        st.markdown(f'<h3>Requests ({len(filtered_df)} of {len(df)})</h3>', unsafe_allow_html=True)
        
        if not filtered_df.empty:
            # Display table with enhanced formatting
            display_df = filtered_df[['method', 'url', 'status', 'status_text', 'time', 'size']].copy()
            display_df['time'] = display_df['time'].round(0).astype(int)
            display_df['size'] = display_df['size'].apply(lambda x: f"{x:,} bytes" if x > 0 else "N/A")
            
            st.dataframe(
                display_df,
                use_container_width=True,
                column_config={
                    "method": st.column_config.TextColumn("Method", width="small"),
                    "url": st.column_config.TextColumn("URL", width="large"),
                    "status": st.column_config.NumberColumn("Status", width="small"),
                    "status_text": st.column_config.TextColumn("Status Text", width="medium"),
                    "time": st.column_config.NumberColumn("Time (ms)", width="small"),
                    "size": st.column_config.TextColumn("Size", width="small")
                }
            )
            
            # Request details
            if st.checkbox("Show detailed request information"):
                selected_index = st.selectbox(
                    "Select request to view details",
                    options=range(len(filtered_df)),
                    format_func=lambda x: f"{filtered_df.iloc[x]['method']} {filtered_df.iloc[x]['url'][:50]}..."
                )
                
                if selected_index is not None:
                    selected_request = filtered_df.iloc[selected_index]
                    
                    st.markdown('<h4>Request Details</h4>', unsafe_allow_html=True)
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("**Request Information:**")
                        st.write(f"Method: {selected_request['method']}")
                        st.write(f"URL: {selected_request['url']}")
                        st.write(f"Host: {selected_request['host']}")
                        st.write(f"Path: {selected_request['path']}")
                    
                    with col2:
                        st.markdown("**Response Information:**")
                        st.write(f"Status: {selected_request['status']} {selected_request['status_text']}")
                        st.write(f"Response Time: {selected_request['time']:.0f}ms")
                        st.write(f"Size: {selected_request['size']:,} bytes")
                        st.write(f"MIME Type: {selected_request['mime_type']}")
        else:
            st.info("No requests match the current filters.")
    
    else:
        st.info("No log data available. Please upload a Charles Proxy log file using the sidebar.")
