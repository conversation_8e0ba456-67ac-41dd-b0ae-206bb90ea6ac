"""
Streamlit interface for Chain-like Command System
Integrates with the existing simplified emulator testing interface
"""

import streamlit as st
import json
import base64
import time
from typing import Dict, List, Any
from test_chain_system import TestChainExecutor, ChainStepType, ChainStepStatus, TestChain, ChainStep

def render_chain_testing_interface(appium_driver):
    """Render the chain testing interface"""
    
    st.markdown("### 🔗 Chain-like Command System")
    st.markdown("Create automated test sequences by chaining multiple Appium operations together")
    
    # Initialize chain executor
    if 'chain_executor' not in st.session_state:
        st.session_state['chain_executor'] = TestChainExecutor(appium_driver)
    
    executor = st.session_state['chain_executor']
    
    # Check if device is connected
    if not appium_driver.is_connected():
        st.warning("⚠️ Please connect to a device first before creating test chains")
        return
    
    # Main interface tabs
    chain_tabs = st.tabs([
        "🔨 Chain Builder",
        "▶️ Execute Chain", 
        "📊 Results",
        "💾 Saved Chains",
        "🔧 Generated Code"
    ])
    
    with chain_tabs[0]:
        render_chain_builder(executor)
    
    with chain_tabs[1]:
        render_chain_executor(executor)
    
    with chain_tabs[2]:
        render_chain_results(executor)
    
    with chain_tabs[3]:
        render_saved_chains(executor)
    
    with chain_tabs[4]:
        render_generated_code(executor)

def render_chain_builder(executor):
    """Render the chain builder interface"""
    
    st.markdown("#### 🔨 Build Test Chain")
    
    # Chain metadata
    col1, col2 = st.columns(2)
    
    with col1:
        chain_name = st.text_input("Chain Name", placeholder="Login Test Chain")
        app_package = st.text_input("App Package (Optional)", placeholder="com.example.app")
    
    with col2:
        chain_description = st.text_area("Description", placeholder="Test user login flow")
    
    # Create new chain button
    if st.button("🆕 Create New Chain", type="primary"):
        if chain_name:
            try:
                chain = executor.create_chain(chain_name, chain_description, app_package)
                st.session_state['current_chain'] = chain
                st.success(f"✅ Created chain: {chain_name}")
                st.rerun()
            except Exception as e:
                st.error(f"❌ Failed to create chain: {str(e)}")
        else:
            st.error("Chain name is required")
    
    # Show current chain
    if 'current_chain' in st.session_state:
        chain = st.session_state['current_chain']
        st.markdown(f"**Current Chain:** {chain.name}")
        st.markdown(f"**Description:** {chain.description}")
        
        if chain.app_package:
            st.markdown(f"**App Package:** {chain.app_package}")
        
        st.markdown("---")
        
        # Add step interface
        st.markdown("#### ➕ Add Step to Chain")
        
        # Step type selection
        step_type_options = {
            "Launch App": ChainStepType.LAUNCH_APP,
            "Take Screenshot": ChainStepType.TAKE_SCREENSHOT,
            "Verify Element Exists": ChainStepType.VERIFY_ELEMENT,
            "Verify Text on Screen": ChainStepType.VERIFY_TEXT,
            "Tap Element": ChainStepType.TAP_ELEMENT,
            "Tap Coordinates": ChainStepType.TAP_COORDINATES,
            "Type Text": ChainStepType.TYPE_TEXT,
            "Swipe Gesture": ChainStepType.SWIPE,
            "Press Device Button": ChainStepType.PRESS_BUTTON,
            "Wait": ChainStepType.WAIT,
            "Scroll": ChainStepType.SCROLL
        }
        
        selected_step_type = st.selectbox(
            "Step Type",
            list(step_type_options.keys()),
            key="step_type_selector"
        )
        
        step_type = step_type_options[selected_step_type]
        
        # Dynamic parameter inputs based on step type
        parameters = {}
        step_description = ""
        
        if step_type == ChainStepType.LAUNCH_APP:
            col1, col2 = st.columns(2)
            with col1:
                package_name = st.text_input("Package Name", placeholder="com.example.app")
            with col2:
                activity = st.text_input("Activity (Optional)", placeholder="MainActivity")
            
            parameters = {"package_name": package_name, "activity": activity}
            step_description = f"Launch app: {package_name}"
        
        elif step_type == ChainStepType.TAKE_SCREENSHOT:
            parameters = {}
            step_description = "Take screenshot"
        
        elif step_type == ChainStepType.VERIFY_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type", ["id", "xpath", "class", "text", "partial_text"])
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login")
            
            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Verify element exists: {locator_type}={locator_value}"
        
        elif step_type == ChainStepType.VERIFY_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                text_to_find = st.text_input("Text to Find", placeholder="Welcome")
            with col2:
                case_sensitive = st.checkbox("Case Sensitive")
            
            parameters = {"text": text_to_find, "case_sensitive": case_sensitive}
            step_description = f"Verify text: '{text_to_find}'"
        
        elif step_type == ChainStepType.TAP_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type", ["id", "xpath", "class", "text", "partial_text"], key="tap_locator_type")
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key="tap_locator_value")
            
            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Tap element: {locator_type}={locator_value}"
        
        elif step_type == ChainStepType.TAP_COORDINATES:
            col1, col2 = st.columns(2)
            with col1:
                x = st.number_input("X Coordinate", min_value=0, max_value=2000, value=500)
            with col2:
                y = st.number_input("Y Coordinate", min_value=0, max_value=2000, value=1000)
            
            parameters = {"x": x, "y": y}
            step_description = f"Tap coordinates: ({x}, {y})"
        
        elif step_type == ChainStepType.TYPE_TEXT:
            text_to_type = st.text_input("Text to Type", placeholder="<EMAIL>")
            parameters = {"text": text_to_type}
            step_description = f"Type text: '{text_to_type}'"
        
        elif step_type == ChainStepType.SWIPE:
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                start_x = st.number_input("Start X", min_value=0, max_value=2000, value=500)
            with col2:
                start_y = st.number_input("Start Y", min_value=0, max_value=2000, value=1500)
            with col3:
                end_x = st.number_input("End X", min_value=0, max_value=2000, value=500)
            with col4:
                end_y = st.number_input("End Y", min_value=0, max_value=2000, value=500)
            
            duration = st.number_input("Duration (ms)", min_value=100, max_value=5000, value=1000)
            
            parameters = {"start_x": start_x, "start_y": start_y, "end_x": end_x, "end_y": end_y, "duration": duration}
            step_description = f"Swipe from ({start_x}, {start_y}) to ({end_x}, {end_y})"
        
        elif step_type == ChainStepType.PRESS_BUTTON:
            button = st.selectbox("Button", ["home", "back", "menu"])
            parameters = {"button": button}
            step_description = f"Press {button} button"
        
        elif step_type == ChainStepType.WAIT:
            duration = st.number_input("Wait Duration (seconds)", min_value=0.1, max_value=30.0, value=2.0, step=0.1)
            parameters = {"duration": duration}
            step_description = f"Wait {duration} seconds"
        
        elif step_type == ChainStepType.SCROLL:
            direction = st.selectbox("Scroll Direction", ["up", "down"])
            parameters = {"direction": direction}
            step_description = f"Scroll {direction}"
        
        # Additional options
        col1, col2 = st.columns(2)
        with col1:
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=60, value=10)
        with col2:
            continue_on_failure = st.checkbox("Continue on Failure")
        
        # Custom description override
        custom_description = st.text_input("Custom Description (Optional)", placeholder=step_description)
        final_description = custom_description if custom_description else step_description
        
        # Add step button
        if st.button("➕ Add Step to Chain"):
            if step_type == ChainStepType.LAUNCH_APP and not parameters.get("package_name"):
                st.error("Package name is required for Launch App step")
            elif step_type in [ChainStepType.VERIFY_ELEMENT, ChainStepType.TAP_ELEMENT] and not parameters.get("locator_value"):
                st.error("Locator value is required for this step type")
            elif step_type == ChainStepType.VERIFY_TEXT and not parameters.get("text"):
                st.error("Text is required for Verify Text step")
            elif step_type == ChainStepType.TYPE_TEXT and not parameters.get("text"):
                st.error("Text is required for Type Text step")
            else:
                try:
                    step = executor.add_step(
                        step_type=step_type,
                        description=final_description,
                        parameters=parameters,
                        timeout=timeout,
                        continue_on_failure=continue_on_failure
                    )
                    st.success(f"✅ Added step: {final_description}")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Failed to add step: {str(e)}")
        
        # Show current steps
        if chain.steps:
            st.markdown("---")
            st.markdown("#### 📋 Current Steps")
            
            for i, step in enumerate(chain.steps):
                with st.expander(f"Step {i+1}: {step.description}", expanded=False):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write(f"**Type:** {step.step_type.value}")
                        st.write(f"**Timeout:** {step.timeout}s")
                        st.write(f"**Continue on Failure:** {step.continue_on_failure}")
                    
                    with col2:
                        st.write(f"**Status:** {step.status.value}")
                        if step.parameters:
                            st.write("**Parameters:**")
                            for key, value in step.parameters.items():
                                st.write(f"  • {key}: {value}")
                    
                    if st.button(f"🗑️ Remove Step {i+1}", key=f"remove_step_{i}"):
                        chain.steps.pop(i)
                        chain.total_steps = len(chain.steps)
                        st.success(f"Removed step {i+1}")
                        st.rerun()
        
        # Quick templates
        st.markdown("---")
        st.markdown("#### 🚀 Quick Templates")
        
        template_col1, template_col2 = st.columns(2)
        
        with template_col1:
            if st.button("📱 Basic App Launch Template"):
                add_app_launch_template(executor, chain)
                st.rerun()
        
        with template_col2:
            if st.button("🔐 Login Flow Template"):
                add_login_flow_template(executor, chain)
                st.rerun()

def add_app_launch_template(executor, chain):
    """Add basic app launch template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch target app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Take initial screenshot", {}),
        (ChainStepType.VERIFY_TEXT, "Verify app loaded", {"text": "Welcome", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def add_login_flow_template(executor, chain):
    """Add login flow template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Initial screen", {}),
        (ChainStepType.TAP_ELEMENT, "Tap username field", {"locator_type": "id", "locator_value": "username"}),
        (ChainStepType.TYPE_TEXT, "Enter username", {"text": "<EMAIL>"}),
        (ChainStepType.TAP_ELEMENT, "Tap password field", {"locator_type": "id", "locator_value": "password"}),
        (ChainStepType.TYPE_TEXT, "Enter password", {"text": "password123"}),
        (ChainStepType.TAP_ELEMENT, "Tap login button", {"locator_type": "id", "locator_value": "login_button"}),
        (ChainStepType.WAIT, "Wait for login", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After login", {}),
        (ChainStepType.VERIFY_TEXT, "Verify login success", {"text": "Dashboard", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)
