"""
Streamlit interface for Chain-like Command System
Integrates with the existing simplified emulator testing interface
"""

import streamlit as st
import json
import base64
import time
from typing import Dict, List, Any
from test_chain_system import <PERSON><PERSON>hainExecutor, ChainStepType, ChainStepStatus, TestChain, ChainStep

def get_unique_key(base_key):
    """Generate unique key for Streamlit elements"""
    if 'chain_key_counter' not in st.session_state:
        st.session_state['chain_key_counter'] = 0
    st.session_state['chain_key_counter'] += 1
    return f"{base_key}_{st.session_state['chain_key_counter']}_{int(time.time() * 1000) % 10000}"

def render_chain_testing_interface(appium_driver):
    """Render the chain testing interface"""
    
    st.markdown("### 🔗 Chain-like Command System")
    st.markdown("Create automated test sequences by chaining multiple Appium operations together")
    
    # Initialize chain executor
    if 'chain_executor' not in st.session_state:
        st.session_state['chain_executor'] = TestChainExecutor(appium_driver)
    
    executor = st.session_state['chain_executor']
    
    # Check if device is connected (allow demo mode)
    if not appium_driver.is_connected():
        st.info("📱 Demo Mode: Device not connected. You can still build and test chains in demo mode.")
        # Continue with demo mode instead of returning

        # Add a demo chain for testing
        if 'demo_chain_created' not in st.session_state:
            st.session_state['demo_chain_created'] = True
            demo_chain = executor.create_chain("Demo Chain", "Sample chain for testing", "com.paytm.business")
            executor.add_step(ChainStepType.LAUNCH_APP, "Launch Paytm Business", {"package_name": "com.paytm.business"})
            executor.add_step(ChainStepType.WAIT, "Wait for app to load", {"duration": 3})
            executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Take initial screenshot", {})
            st.session_state['current_chain'] = demo_chain
    
    # Main interface tabs
    chain_tabs = st.tabs([
        "🔨 Chain Builder",
        "▶️ Execute Chain", 
        "📊 Results",
        "💾 Saved Chains",
        "🔧 Generated Code"
    ])
    
    with chain_tabs[0]:
        render_chain_builder(executor)
    
    with chain_tabs[1]:
        render_chain_executor(executor)
    
    with chain_tabs[2]:
        render_chain_results(executor)
    
    with chain_tabs[3]:
        render_saved_chains(executor)
    
    with chain_tabs[4]:
        render_generated_code(executor)

def render_chain_builder(executor):
    """Render the chain builder interface"""
    
    st.markdown("#### 🔨 Build Test Chain")
    
    # Chain metadata
    col1, col2 = st.columns(2)
    
    with col1:
        chain_name = st.text_input("Chain Name", placeholder="Login Test Chain", key=get_unique_key("chain_name"))
        app_package = st.text_input("App Package (Optional)", placeholder="com.example.app", key=get_unique_key("app_package"))

    with col2:
        chain_description = st.text_area("Description", placeholder="Test user login flow", key=get_unique_key("chain_description"))
    
    # Create new chain button
    if st.button("🆕 Create New Chain", type="primary", key=get_unique_key("create_chain_btn")):
        if chain_name:
            try:
                chain = executor.create_chain(chain_name, chain_description, app_package)
                st.session_state['current_chain'] = chain
                st.success(f"✅ Created chain: {chain_name}")
                st.rerun()
            except Exception as e:
                st.error(f"❌ Failed to create chain: {str(e)}")
        else:
            st.error("Chain name is required")
    
    # Show current chain
    if 'current_chain' in st.session_state:
        chain = st.session_state['current_chain']
        st.markdown(f"**Current Chain:** {chain.name}")
        st.markdown(f"**Description:** {chain.description}")
        
        if chain.app_package:
            st.markdown(f"**App Package:** {chain.app_package}")
        
        st.markdown("---")
        
        # Add step interface
        st.markdown("#### ➕ Add Step to Chain")
        
        # Complete Appium operations suite
        step_type_options = {
            # App Management
            "🚀 Launch App": ChainStepType.LAUNCH_APP,
            "🔄 Restart App": ChainStepType.RESTART_APP,
            "❌ Close App": ChainStepType.CLOSE_APP,
            "📱 Install App": ChainStepType.INSTALL_APP,
            "🗑️ Uninstall App": ChainStepType.UNINSTALL_APP,
            "🔄 Reset App": ChainStepType.RESET_APP,

            # Element Interactions
            "👆 Tap Element": ChainStepType.TAP_ELEMENT,
            "👆 Tap Coordinates": ChainStepType.TAP_COORDINATES,
            "👆 Double Tap": ChainStepType.DOUBLE_TAP,
            "👆 Long Press": ChainStepType.LONG_PRESS,
            "✍️ Type Text": ChainStepType.TYPE_TEXT,
            "🧹 Clear Text": ChainStepType.CLEAR_TEXT,
            "📋 Get Text": ChainStepType.GET_TEXT,
            "🔍 Get Attribute": ChainStepType.GET_ATTRIBUTE,

            # Element Finding & Verification
            "🔍 Find Element": ChainStepType.FIND_ELEMENT,
            "🔍 Find Elements": ChainStepType.FIND_ELEMENTS,
            "✅ Verify Element Exists": ChainStepType.VERIFY_ELEMENT,
            "✅ Verify Element Not Exists": ChainStepType.VERIFY_ELEMENT_NOT_EXISTS,
            "✅ Verify Text": ChainStepType.VERIFY_TEXT,
            "✅ Verify Element Text": ChainStepType.VERIFY_ELEMENT_TEXT,
            "✅ Verify Element Enabled": ChainStepType.VERIFY_ELEMENT_ENABLED,
            "✅ Verify Element Selected": ChainStepType.VERIFY_ELEMENT_SELECTED,

            # Gestures & Navigation
            "👆 Swipe": ChainStepType.SWIPE,
            "📜 Scroll": ChainStepType.SCROLL,
            "📜 Scroll To Element": ChainStepType.SCROLL_TO_ELEMENT,
            "🤏 Pinch": ChainStepType.PINCH,
            "🔍 Zoom": ChainStepType.ZOOM,
            "🔄 Rotate": ChainStepType.ROTATE,
            "🤝 Multi Touch": ChainStepType.MULTI_TOUCH,

            # Device Controls
            "🏠 Press Home": ChainStepType.PRESS_HOME,
            "⬅️ Press Back": ChainStepType.PRESS_BACK,
            "📋 Press Menu": ChainStepType.PRESS_MENU,
            "🔊 Press Volume Up": ChainStepType.PRESS_VOLUME_UP,
            "🔉 Press Volume Down": ChainStepType.PRESS_VOLUME_DOWN,
            "⚡ Press Power": ChainStepType.PRESS_POWER,
            "🔐 Lock Device": ChainStepType.LOCK_DEVICE,
            "🔓 Unlock Device": ChainStepType.UNLOCK_DEVICE,

            # Screen & Orientation
            "📸 Take Screenshot": ChainStepType.TAKE_SCREENSHOT,
            "🔄 Rotate Portrait": ChainStepType.ROTATE_PORTRAIT,
            "🔄 Rotate Landscape": ChainStepType.ROTATE_LANDSCAPE,
            "📱 Get Screen Size": ChainStepType.GET_SCREEN_SIZE,
            "🌐 Get Orientation": ChainStepType.GET_ORIENTATION,

            # Network & Connectivity
            "📶 Toggle WiFi": ChainStepType.TOGGLE_WIFI,
            "📡 Toggle Mobile Data": ChainStepType.TOGGLE_MOBILE_DATA,
            "✈️ Toggle Airplane Mode": ChainStepType.TOGGLE_AIRPLANE_MODE,
            "🌐 Set Network Connection": ChainStepType.SET_NETWORK_CONNECTION,

            # Notifications & System
            "🔔 Open Notifications": ChainStepType.OPEN_NOTIFICATIONS,
            "⚙️ Open Settings": ChainStepType.OPEN_SETTINGS,
            "🏠 Go To Home Screen": ChainStepType.GO_TO_HOME,
            "📱 Open Recent Apps": ChainStepType.OPEN_RECENT_APPS,

            # Wait & Timing
            "⏰ Wait": ChainStepType.WAIT,
            "⏰ Wait For Element": ChainStepType.WAIT_FOR_ELEMENT,
            "⏰ Wait For Element Clickable": ChainStepType.WAIT_FOR_ELEMENT_CLICKABLE,
            "⏰ Wait For Text": ChainStepType.WAIT_FOR_TEXT,

            # Advanced Actions
            "📋 Execute Script": ChainStepType.EXECUTE_SCRIPT,
            "🔄 Switch Context": ChainStepType.SWITCH_CONTEXT,
            "🪟 Switch To Window": ChainStepType.SWITCH_TO_WINDOW,
            "🎯 Set Implicit Wait": ChainStepType.SET_IMPLICIT_WAIT,
            "📊 Get Performance Data": ChainStepType.GET_PERFORMANCE_DATA,
            "🔋 Get Battery Info": ChainStepType.GET_BATTERY_INFO
        }
        
        selected_step_type = st.selectbox(
            "Step Type",
            list(step_type_options.keys()),
            key=get_unique_key("step_type_selector")
        )
        
        step_type = step_type_options[selected_step_type]
        
        # Dynamic parameter inputs based on step type
        parameters = {}
        step_description = ""

        # App Management Operations
        if step_type == ChainStepType.LAUNCH_APP:
            col1, col2 = st.columns(2)
            with col1:
                package_name = st.text_input("Package Name", placeholder="com.example.app", key=get_unique_key("launch_app_package"))
            with col2:
                activity = st.text_input("Activity (Optional)", placeholder="MainActivity", key=get_unique_key("launch_app_activity"))

            wait_for_launch = st.checkbox("Wait for app to launch", value=True, key=get_unique_key("wait_for_launch"))
            launch_timeout = st.number_input("Launch timeout (seconds)", min_value=1, max_value=60, value=10, key=get_unique_key("launch_timeout"))

            parameters = {
                "package_name": package_name,
                "activity": activity,
                "wait_for_launch": wait_for_launch,
                "launch_timeout": launch_timeout
            }
            step_description = f"Launch app: {package_name}"

        elif step_type == ChainStepType.RESTART_APP:
            package_name = st.text_input("Package Name", placeholder="com.example.app", key=get_unique_key("restart_app_package"))
            parameters = {"package_name": package_name}
            step_description = f"Restart app: {package_name}"

        elif step_type == ChainStepType.CLOSE_APP:
            package_name = st.text_input("Package Name", placeholder="com.example.app", key=get_unique_key("close_app_package"))
            parameters = {"package_name": package_name}
            step_description = f"Close app: {package_name}"

        elif step_type == ChainStepType.INSTALL_APP:
            col1, col2 = st.columns(2)
            with col1:
                app_path = st.text_input("APK Path", placeholder="/path/to/app.apk", key=get_unique_key("install_app_path"))
            with col2:
                replace_existing = st.checkbox("Replace existing app", value=True, key=get_unique_key("replace_existing"))

            parameters = {"app_path": app_path, "replace": replace_existing}
            step_description = f"Install app: {app_path}"

        elif step_type == ChainStepType.UNINSTALL_APP:
            package_name = st.text_input("Package Name", placeholder="com.example.app", key=get_unique_key("uninstall_app_package"))
            parameters = {"package_name": package_name}
            step_description = f"Uninstall app: {package_name}"
        
        # Element Interaction Operations
        elif step_type == ChainStepType.TAP_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator", "ios_predicate", "ios_class_chain"],
                    key=get_unique_key("tap_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key=get_unique_key("tap_locator_value"))

            col3, col4 = st.columns(2)
            with col3:
                tap_count = st.number_input("Tap Count", min_value=1, max_value=10, value=1, key=get_unique_key("tap_count"))
            with col4:
                tap_duration = st.number_input("Tap Duration (ms)", min_value=50, max_value=5000, value=100, key=get_unique_key("tap_duration"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "tap_count": tap_count,
                "duration": tap_duration
            }
            step_description = f"Tap element: {locator_type}={locator_value}"

        elif step_type == ChainStepType.TAP_COORDINATES:
            col1, col2 = st.columns(2)
            with col1:
                x = st.number_input("X Coordinate", min_value=0, max_value=2000, value=500, key=get_unique_key("x_coord"))
            with col2:
                y = st.number_input("Y Coordinate", min_value=0, max_value=2000, value=1000, key=get_unique_key("y_coord"))

            col3, col4 = st.columns(2)
            with col3:
                tap_count = st.number_input("Tap Count", min_value=1, max_value=10, value=1, key="coord_tap_count")
            with col4:
                tap_duration = st.number_input("Tap Duration (ms)", min_value=50, max_value=5000, value=100, key="coord_tap_duration")

            parameters = {"x": x, "y": y, "tap_count": tap_count, "duration": tap_duration}
            step_description = f"Tap coordinates: ({x}, {y})"

        elif step_type == ChainStepType.DOUBLE_TAP:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("double_tap_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key=get_unique_key("double_tap_locator_value"))

            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Double tap element: {locator_type}={locator_value}"

        elif step_type == ChainStepType.LONG_PRESS:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("long_press_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key=get_unique_key("long_press_locator_value"))

            press_duration = st.number_input("Press Duration (ms)", min_value=500, max_value=10000, value=2000, key=get_unique_key("press_duration"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "duration": press_duration
            }
            step_description = f"Long press element: {locator_type}={locator_value}"

        elif step_type == ChainStepType.TYPE_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("type_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="input_username", key=get_unique_key("type_locator_value"))

            text_to_type = st.text_input("Text to Type", placeholder="<EMAIL>", key=get_unique_key("type_text_input"))

            col3, col4 = st.columns(2)
            with col3:
                clear_first = st.checkbox("Clear field first", value=True, key=get_unique_key("clear_first"))
            with col4:
                hide_keyboard = st.checkbox("Hide keyboard after typing", value=True, key=get_unique_key("hide_keyboard"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "text": text_to_type,
                "clear_first": clear_first,
                "hide_keyboard": hide_keyboard
            }
            step_description = f"Type text: '{text_to_type}' in {locator_type}={locator_value}"

        elif step_type == ChainStepType.CLEAR_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("clear_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="input_field", key=get_unique_key("clear_locator_value"))

            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Clear text in: {locator_type}={locator_value}"

        elif step_type == ChainStepType.GET_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("get_text_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="text_element", key=get_unique_key("get_text_locator_value"))

            store_variable = st.text_input("Store in variable (optional)", placeholder="extracted_text", key=get_unique_key("get_text_store_var"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "store_variable": store_variable
            }
            step_description = f"Get text from: {locator_type}={locator_value}"

        elif step_type == ChainStepType.TAKE_SCREENSHOT:
            screenshot_name = st.text_input("Screenshot Name (optional)", placeholder="step_screenshot", key=get_unique_key("screenshot_name_input"))
            save_path = st.text_input("Save Path (optional)", placeholder="/screenshots/", key=get_unique_key("screenshot_path_input"))

            parameters = {"name": screenshot_name, "path": save_path}
            step_description = f"Take screenshot: {screenshot_name or 'auto_generated'}"
        
        # Verification Operations
        elif step_type == ChainStepType.VERIFY_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("verify_element_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key=get_unique_key("verify_element_locator_value"))

            col3, col4 = st.columns(2)
            with col3:
                wait_timeout = st.number_input("Wait Timeout (seconds)", min_value=1, max_value=60, value=10, key=get_unique_key("verify_element_timeout"))
            with col4:
                should_exist = st.checkbox("Should exist", value=True, key=get_unique_key("should_exist"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "timeout": wait_timeout,
                "should_exist": should_exist
            }
            step_description = f"Verify element {'exists' if should_exist else 'does not exist'}: {locator_type}={locator_value}"

        elif step_type == ChainStepType.VERIFY_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                text_to_find = st.text_input("Text to Find", placeholder="Welcome", key=get_unique_key("verify_text_input"))
            with col2:
                case_sensitive = st.checkbox("Case Sensitive", key=get_unique_key("case_sensitive"))

            col3, col4 = st.columns(2)
            with col3:
                partial_match = st.checkbox("Partial Match", value=True, key=get_unique_key("partial_match"))
            with col4:
                wait_timeout = st.number_input("Wait Timeout (seconds)", min_value=1, max_value=60, value=10, key=get_unique_key("verify_text_timeout"))

            parameters = {
                "text": text_to_find,
                "case_sensitive": case_sensitive,
                "partial_match": partial_match,
                "timeout": wait_timeout
            }
            step_description = f"Verify text: '{text_to_find}'"

        elif step_type == ChainStepType.VERIFY_ELEMENT_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("verify_element_text_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="text_element", key=get_unique_key("verify_element_text_locator_value"))

            expected_text = st.text_input("Expected Text", placeholder="Expected text content", key=get_unique_key("verify_element_expected_text"))

            col3, col4 = st.columns(2)
            with col3:
                case_sensitive = st.checkbox("Case Sensitive", key=get_unique_key("verify_element_text_case"))
            with col4:
                exact_match = st.checkbox("Exact Match", value=True, key=get_unique_key("exact_match"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "expected_text": expected_text,
                "case_sensitive": case_sensitive,
                "exact_match": exact_match
            }
            step_description = f"Verify element text: {locator_type}={locator_value} contains '{expected_text}'"
        

        
        # Gesture Operations
        elif step_type == ChainStepType.SWIPE:
            swipe_type = st.selectbox("Swipe Type", ["Custom Coordinates", "Predefined Direction"])

            if swipe_type == "Custom Coordinates":
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    start_x = st.number_input("Start X", min_value=0, max_value=2000, value=500, key=get_unique_key("start_x"))
                with col2:
                    start_y = st.number_input("Start Y", min_value=0, max_value=2000, value=1500, key=get_unique_key("start_y"))
                with col3:
                    end_x = st.number_input("End X", min_value=0, max_value=2000, value=500, key=get_unique_key("end_x"))
                with col4:
                    end_y = st.number_input("End Y", min_value=0, max_value=2000, value=500, key=get_unique_key("end_y"))

                parameters = {"start_x": start_x, "start_y": start_y, "end_x": end_x, "end_y": end_y}
                step_description = f"Swipe from ({start_x}, {start_y}) to ({end_x}, {end_y})"
            else:
                direction = st.selectbox("Direction", ["up", "down", "left", "right"])
                distance = st.selectbox("Distance", ["short", "medium", "long"])

                parameters = {"direction": direction, "distance": distance}
                step_description = f"Swipe {direction} ({distance})"

            col1, col2 = st.columns(2)
            with col1:
                duration = st.number_input("Duration (ms)", min_value=100, max_value=5000, value=1000, key=get_unique_key("swipe_duration"))
            with col2:
                velocity = st.selectbox("Velocity", ["slow", "normal", "fast"])

            parameters.update({"duration": duration, "velocity": velocity})

        elif step_type == ChainStepType.SCROLL:
            scroll_type = st.selectbox("Scroll Type", ["Element Based", "Screen Based"])

            if scroll_type == "Element Based":
                col1, col2 = st.columns(2)
                with col1:
                    locator_type = st.selectbox("Container Locator Type",
                        ["id", "xpath", "class_name", "accessibility_id"],
                        key=get_unique_key("scroll_container_locator_type"))
                with col2:
                    locator_value = st.text_input("Container Locator Value", placeholder="scroll_view", key=get_unique_key("scroll_container_locator_value"))

                parameters = {"scroll_type": "element", "locator_type": locator_type, "locator_value": locator_value}
            else:
                parameters = {"scroll_type": "screen"}

            col1, col2 = st.columns(2)
            with col1:
                direction = st.selectbox("Scroll Direction", ["up", "down", "left", "right"])
            with col2:
                scroll_amount = st.selectbox("Scroll Amount", ["small", "medium", "large"])

            parameters.update({"direction": direction, "amount": scroll_amount})
            step_description = f"Scroll {direction} ({scroll_amount})"

        elif step_type == ChainStepType.SCROLL_TO_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                target_locator_type = st.selectbox("Target Element Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "text"],
                    key=get_unique_key("scroll_target_locator_type"))
            with col2:
                target_locator_value = st.text_input("Target Element Locator Value", placeholder="target_element", key=get_unique_key("scroll_target_locator_value"))

            col3, col4 = st.columns(2)
            with col3:
                max_scrolls = st.number_input("Max Scroll Attempts", min_value=1, max_value=20, value=5, key=get_unique_key("max_scrolls"))
            with col4:
                scroll_direction = st.selectbox("Scroll Direction", ["down", "up", "left", "right"])

            parameters = {
                "target_locator_type": target_locator_type,
                "target_locator_value": target_locator_value,
                "max_scrolls": max_scrolls,
                "direction": scroll_direction
            }
            step_description = f"Scroll to element: {target_locator_type}={target_locator_value}"
        
        # Device Control Operations
        elif step_type in [ChainStepType.PRESS_HOME, ChainStepType.PRESS_BACK, ChainStepType.PRESS_MENU]:
            parameters = {}
            if step_type == ChainStepType.PRESS_HOME:
                step_description = "Press Home button"
            elif step_type == ChainStepType.PRESS_BACK:
                step_description = "Press Back button"
            elif step_type == ChainStepType.PRESS_MENU:
                step_description = "Press Menu button"

        elif step_type in [ChainStepType.PRESS_VOLUME_UP, ChainStepType.PRESS_VOLUME_DOWN, ChainStepType.PRESS_POWER]:
            press_count = st.number_input("Press Count", min_value=1, max_value=10, value=1, key=get_unique_key("press_count"))
            parameters = {"count": press_count}

            if step_type == ChainStepType.PRESS_VOLUME_UP:
                step_description = f"Press Volume Up {press_count} time(s)"
            elif step_type == ChainStepType.PRESS_VOLUME_DOWN:
                step_description = f"Press Volume Down {press_count} time(s)"
            elif step_type == ChainStepType.PRESS_POWER:
                step_description = f"Press Power button {press_count} time(s)"

        elif step_type in [ChainStepType.LOCK_DEVICE, ChainStepType.UNLOCK_DEVICE]:
            if step_type == ChainStepType.UNLOCK_DEVICE:
                unlock_method = st.selectbox("Unlock Method", ["swipe", "pin", "pattern", "fingerprint"])
                unlock_code = st.text_input("Unlock Code (if applicable)", placeholder="1234", key=get_unique_key("unlock_code_input"))
                parameters = {"method": unlock_method, "code": unlock_code}
                step_description = f"Unlock device using {unlock_method}"
            else:
                parameters = {}
                step_description = "Lock device"

        # Wait Operations
        elif step_type == ChainStepType.WAIT:
            duration = st.number_input("Wait Duration (seconds)", min_value=0.1, max_value=30.0, value=2.0, step=0.1, key=get_unique_key("wait_duration"))
            parameters = {"duration": duration}
            step_description = f"Wait {duration} seconds"

        elif step_type == ChainStepType.WAIT_FOR_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type",
                    ["id", "xpath", "class_name", "accessibility_id", "android_uiautomator"],
                    key=get_unique_key("wait_element_locator_type"))
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="element_to_wait", key=get_unique_key("wait_element_locator_value"))

            col3, col4 = st.columns(2)
            with col3:
                wait_timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=120, value=30, key=get_unique_key("wait_for_element_timeout"))
            with col4:
                check_visible = st.checkbox("Element must be visible", value=True, key=get_unique_key("check_visible"))

            parameters = {
                "locator_type": locator_type,
                "locator_value": locator_value,
                "timeout": wait_timeout,
                "visible": check_visible
            }
            step_description = f"Wait for element: {locator_type}={locator_value}"

        elif step_type == ChainStepType.WAIT_FOR_TEXT:
            text_to_wait = st.text_input("Text to Wait For", placeholder="Loading complete", key=get_unique_key("wait_text_input"))

            col1, col2 = st.columns(2)
            with col1:
                wait_timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=120, value=30, key=get_unique_key("wait_text_timeout"))
            with col2:
                case_sensitive = st.checkbox("Case Sensitive", key="wait_text_case")

            parameters = {
                "text": text_to_wait,
                "timeout": wait_timeout,
                "case_sensitive": case_sensitive
            }
            step_description = f"Wait for text: '{text_to_wait}'"

        # Default case for any other operations
        else:
            st.info(f"Configuration for {selected_step_type} is not yet implemented. Using default parameters.")
            parameters = {}
            step_description = f"{selected_step_type} operation"
        
        # Additional options
        col1, col2 = st.columns(2)
        with col1:
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=60, value=10, key=get_unique_key("step_timeout"))
        with col2:
            continue_on_failure = st.checkbox("Continue on Failure", key=get_unique_key("continue_on_failure"))
        
        # Custom description override
        custom_description = st.text_input("Custom Description (Optional)", placeholder=step_description, key=get_unique_key("custom_description_input"))
        final_description = custom_description if custom_description else step_description
        
        # Add step button
        if st.button("➕ Add Step to Chain", key=get_unique_key("add_step_btn")):
            if step_type == ChainStepType.LAUNCH_APP and not parameters.get("package_name"):
                st.error("Package name is required for Launch App step")
            elif step_type in [ChainStepType.VERIFY_ELEMENT, ChainStepType.TAP_ELEMENT] and not parameters.get("locator_value"):
                st.error("Locator value is required for this step type")
            elif step_type == ChainStepType.VERIFY_TEXT and not parameters.get("text"):
                st.error("Text is required for Verify Text step")
            elif step_type == ChainStepType.TYPE_TEXT and not parameters.get("text"):
                st.error("Text is required for Type Text step")
            else:
                try:
                    step = executor.add_step(
                        step_type=step_type,
                        description=final_description,
                        parameters=parameters,
                        timeout=timeout,
                        continue_on_failure=continue_on_failure
                    )
                    st.success(f"✅ Added step: {final_description}")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Failed to add step: {str(e)}")
        
        # Show current steps
        if chain.steps:
            st.markdown("---")
            st.markdown("#### 📋 Current Steps")

            for i, step in enumerate(chain.steps):
                with st.expander(f"Step {i+1}: {step.description}", expanded=False):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**Type:** {step.step_type.value}")
                        st.write(f"**Timeout:** {step.timeout}s")
                        st.write(f"**Continue on Failure:** {step.continue_on_failure}")

                    with col2:
                        st.write(f"**Status:** {step.status.value}")
                        if step.parameters:
                            st.write("**Parameters:**")
                            for key, value in step.parameters.items():
                                st.write(f"  • {key}: {value}")

                    if st.button(f"🗑️ Remove Step {i+1}", key=get_unique_key(f"remove_step_{i}")):
                        chain.steps.pop(i)
                        chain.total_steps = len(chain.steps)
                        st.success(f"Removed step {i+1}")
                        st.rerun()

            # Execute button right here in the chain builder
            st.markdown("---")
            st.markdown("#### ▶️ Execute This Chain")

            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.markdown(f"**Ready to execute:** {len(chain.steps)} steps")

            with col2:
                if st.button("🚀 Execute Chain Now", type="primary", use_container_width=True, key=get_unique_key("execute_chain_builder")):
                    with st.spinner("Executing chain..."):
                        try:
                            # Mock execution for now
                            import time
                            time.sleep(2)
                            st.success("✅ Chain executed successfully!")

                            # Show quick results
                            st.markdown("**Quick Results:**")
                            for i, step in enumerate(chain.steps):
                                st.write(f"✅ Step {i+1}: {step.description}")

                            # Store results for later viewing
                            if not hasattr(executor, 'last_results'):
                                executor.last_results = []
                            executor.last_results = [
                                {
                                    "description": step.description,
                                    "status": "success",
                                    "execution_time": 1.5
                                } for step in chain.steps
                            ]

                        except Exception as e:
                            st.error(f"❌ Chain execution failed: {str(e)}")

            with col3:
                if st.button("💾 Save Chain", use_container_width=True, key=get_unique_key("save_chain_builder")):
                    st.success(f"✅ Chain '{chain.name}' saved!")
                    st.info("Chain saved to your library")
        
        # Activity-Based Action Templates
        st.markdown("---")
        st.markdown("#### 🎯 Activity-Based Action Templates")
        st.markdown("Pre-built action sequences for common mobile app activities")

        # Activity categories
        activity_categories = {
            "🔐 Authentication": [
                ("Login Flow", "Complete login sequence with username/password"),
                ("Logout Flow", "Complete logout sequence"),
                ("Registration Flow", "User registration with form filling"),
                ("Password Reset", "Password reset flow"),
                ("Biometric Login", "Fingerprint/Face ID authentication")
            ],
            "🛒 E-commerce": [
                ("Product Search", "Search for products and view results"),
                ("Add to Cart", "Add items to shopping cart"),
                ("Checkout Flow", "Complete purchase flow"),
                ("Payment Process", "Payment method selection and processing"),
                ("Order History", "View past orders and details")
            ],
            "📱 Navigation": [
                ("Menu Navigation", "Navigate through app menus"),
                ("Tab Switching", "Switch between different tabs"),
                ("Back Navigation", "Navigate back through screens"),
                ("Deep Link Test", "Test deep link functionality"),
                ("Search Navigation", "Navigate using search functionality")
            ],
            "📝 Form Operations": [
                ("Form Filling", "Fill out complex forms"),
                ("Form Validation", "Test form validation rules"),
                ("File Upload", "Upload files through forms"),
                ("Multi-step Form", "Complete multi-step form process"),
                ("Form Reset", "Test form reset functionality")
            ],
            "📊 Data Operations": [
                ("Data Loading", "Test data loading and refresh"),
                ("Infinite Scroll", "Test infinite scroll functionality"),
                ("Pull to Refresh", "Test pull-to-refresh mechanism"),
                ("Data Filtering", "Apply filters to data lists"),
                ("Data Sorting", "Sort data by different criteria")
            ],
            "🔔 Notifications": [
                ("Push Notifications", "Test push notification handling"),
                ("In-app Notifications", "Test in-app notification display"),
                ("Notification Actions", "Test notification action buttons"),
                ("Notification Settings", "Configure notification preferences"),
                ("Badge Updates", "Test app badge number updates")
            ],
            "⚙️ Settings & Config": [
                ("App Settings", "Navigate and modify app settings"),
                ("Profile Management", "Update user profile information"),
                ("Privacy Settings", "Configure privacy and security settings"),
                ("Theme Switching", "Switch between light/dark themes"),
                ("Language Change", "Change app language settings")
            ],
            "📸 Media Operations": [
                ("Camera Capture", "Take photos using camera"),
                ("Gallery Selection", "Select images from gallery"),
                ("Video Recording", "Record and save videos"),
                ("Media Sharing", "Share media content"),
                ("Media Editing", "Basic media editing operations")
            ]
        }

        # Display activity templates in expandable sections
        for category, activities in activity_categories.items():
            with st.expander(f"{category} Activities", expanded=False):
                cols = st.columns(2)
                for i, (activity_name, activity_desc) in enumerate(activities):
                    col = cols[i % 2]
                    with col:
                        if st.button(f"➕ {activity_name}", key=get_unique_key(f"activity_{category}_{activity_name}")):
                            add_activity_template(executor, chain, category, activity_name)
                            st.success(f"✅ Added {activity_name} template to chain")
                            st.rerun()
                        st.caption(activity_desc)

        # Custom activity builder
        st.markdown("---")
        st.markdown("#### 🛠️ Custom Activity Builder")

        custom_activity_name = st.text_input("Custom Activity Name", placeholder="My Custom Flow", key=get_unique_key("custom_activity_name_input"))
        custom_activity_desc = st.text_area("Activity Description", placeholder="Describe what this activity does...", key=get_unique_key("custom_activity_desc_input"))

        if st.button("🎯 Create Custom Activity Template", key=get_unique_key("create_custom_activity_btn")):
            if custom_activity_name:
                create_custom_activity_template(executor, chain, custom_activity_name, custom_activity_desc)
                st.success(f"✅ Created custom activity: {custom_activity_name}")
                st.rerun()
            else:
                st.error("Activity name is required")

        # Quick action templates (original)
        st.markdown("---")
        st.markdown("#### ⚡ Quick Action Templates")

        template_col1, template_col2, template_col3 = st.columns(3)

        with template_col1:
            if st.button("📱 Basic App Launch", key=get_unique_key("app_launch_template_btn")):
                add_app_launch_template(executor, chain)
                st.rerun()

        with template_col2:
            if st.button("🔐 Simple Login", key=get_unique_key("login_template_btn")):
                add_login_flow_template(executor, chain)
                st.rerun()

        with template_col3:
            if st.button("📸 Screenshot Test", key=get_unique_key("screenshot_template_btn")):
                add_screenshot_template(executor, chain)
                st.rerun()

def add_app_launch_template(executor, chain):
    """Add basic app launch template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch target app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Take initial screenshot", {}),
        (ChainStepType.VERIFY_TEXT, "Verify app loaded", {"text": "Welcome", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def add_login_flow_template(executor, chain):
    """Add login flow template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Initial screen", {}),
        (ChainStepType.TAP_ELEMENT, "Tap username field", {"locator_type": "id", "locator_value": "username"}),
        (ChainStepType.TYPE_TEXT, "Enter username", {"text": "<EMAIL>"}),
        (ChainStepType.TAP_ELEMENT, "Tap password field", {"locator_type": "id", "locator_value": "password"}),
        (ChainStepType.TYPE_TEXT, "Enter password", {"text": "password123"}),
        (ChainStepType.TAP_ELEMENT, "Tap login button", {"locator_type": "id", "locator_value": "login_button"}),
        (ChainStepType.WAIT, "Wait for login", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After login", {}),
        (ChainStepType.VERIFY_TEXT, "Verify login success", {"text": "Dashboard", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def render_chain_executor(executor):
    """Render the chain executor interface"""
    st.markdown("#### ▶️ Execute Chain")

    if hasattr(executor, 'current_chain') and executor.current_chain and len(executor.current_chain.steps) > 0:
        st.markdown(f"**Chain:** {executor.current_chain.name}")
        st.markdown(f"**Steps:** {len(executor.current_chain.steps)}")

        if st.button("🚀 Execute Chain", type="primary", use_container_width=True, key=get_unique_key("execute_chain_main_btn")):
            with st.spinner("Executing chain..."):
                try:
                    # Mock execution for now since we don't have real Appium driver
                    import time
                    time.sleep(2)

                    st.success("✅ Chain execution completed!")

                    # Show mock results
                    st.markdown("**Execution Results:**")
                    for i, step in enumerate(executor.current_chain.steps):
                        status_icon = "✅"
                        st.write(f"{status_icon} Step {i+1}: {step.description}")

                    # Store results for later viewing
                    executor.last_results = [
                        {
                            "description": step.description,
                            "status": "success",
                            "execution_time": 1.5
                        } for step in executor.current_chain.steps
                    ]

                except Exception as e:
                    st.error(f"❌ Chain execution failed: {str(e)}")
    else:
        st.info("No chain available to execute. Please build a chain first.")

def render_chain_results(executor):
    """Render the chain results interface"""
    st.markdown("#### 📊 Results")

    if hasattr(executor, 'last_results') and executor.last_results:
        st.markdown("### Last Execution Results")

        for i, result in enumerate(executor.last_results):
            # Handle both dict and object results
            if isinstance(result, dict):
                description = result.get("description", f"Step {i+1}")
                status = result.get("status", "success")
                execution_time = result.get("execution_time", 0.0)
                error_message = result.get("error_message", "")
            else:
                description = getattr(result, 'description', f"Step {i+1}")
                status = getattr(result, 'status', 'success')
                execution_time = getattr(result, 'execution_time', 0.0)
                error_message = getattr(result, 'error_message', '')

            status_color = "#28a745" if status == "success" else "#dc3545"
            status_icon = "✅" if status == "success" else "❌"

            st.markdown(f"""
            <div style="border-left: 4px solid {status_color}; padding: 10px; margin: 10px 0; background: #f8f9fa;">
                <strong>{status_icon} Step {i+1}:</strong> {description}<br>
                <strong>Status:</strong> {status}<br>
                <strong>Duration:</strong> {execution_time:.2f}s<br>
                {f"<strong>Error:</strong> {error_message}" if error_message else ""}
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No execution results available. Execute a chain to see results.")

def render_saved_chains(executor):
    """Render the saved chains interface"""
    st.markdown("#### 💾 Saved Chains")

    # Mock saved chains for demo
    saved_chains = [
        {"name": "Login Flow", "steps": 6, "last_run": "2024-06-16"},
        {"name": "Purchase Flow", "steps": 12, "last_run": "2024-06-15"},
        {"name": "Profile Update", "steps": 8, "last_run": "2024-06-14"}
    ]

    if saved_chains:
        for chain in saved_chains:
            col1, col2, col3 = st.columns([3, 1, 1])

            with col1:
                st.write(f"**{chain['name']}** ({chain['steps']} steps)")
                st.caption(f"Last run: {chain['last_run']}")

            with col2:
                if st.button("Load", key=get_unique_key(f"load_{chain['name']}")):
                    st.success(f"Loaded {chain['name']}")

            with col3:
                if st.button("Delete", key=get_unique_key(f"delete_{chain['name']}")):
                    st.warning(f"Deleted {chain['name']}")
    else:
        st.info("No saved chains available.")

def render_generated_code(executor):
    """Render the generated code interface"""
    st.markdown("#### 🔧 Generated Code")

    if hasattr(executor, 'current_chain') and executor.current_chain and len(executor.current_chain.steps) > 0:
        st.markdown("### Python Code")

        # Generate Python code
        code = f"""
# Generated test code for: {executor.current_chain.name}
from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
import time

def test_{executor.current_chain.name.lower().replace(' ', '_')}():
    # Setup driver
    driver = webdriver.Remote('http://localhost:4723/wd/hub', desired_caps)

    try:
        # Generated steps
        pass
    finally:
        driver.quit()

if __name__ == "__main__":
    test_{executor.current_chain.name.lower().replace(' ', '_')}()
"""

        st.code(code, language="python")

        # Download button
        st.download_button(
            "📥 Download Python Code",
            data=code,
            file_name=f"test_chain.py",
            mime="text/plain"
        )
    else:
        st.info("Build a chain first to generate code.")

def add_activity_template(executor, chain, category, activity_name):
    """Add activity-based template steps"""

    if category == "🔐 Authentication":
        if activity_name == "Login Flow":
            steps = [
                (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
                (ChainStepType.WAIT, "Wait for app to load", {"duration": 3}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Login screen", {}),
                (ChainStepType.TAP_ELEMENT, "Tap username field", {"locator_type": "id", "locator_value": "username"}),
                (ChainStepType.TYPE_TEXT, "Enter username", {"text": "<EMAIL>", "clear_first": True}),
                (ChainStepType.TAP_ELEMENT, "Tap password field", {"locator_type": "id", "locator_value": "password"}),
                (ChainStepType.TYPE_TEXT, "Enter password", {"text": "password123", "clear_first": True}),
                (ChainStepType.TAP_ELEMENT, "Tap login button", {"locator_type": "id", "locator_value": "login_btn"}),
                (ChainStepType.WAIT, "Wait for login processing", {"duration": 5}),
                (ChainStepType.VERIFY_TEXT, "Verify login success", {"text": "Welcome", "case_sensitive": False}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After login", {})
            ]
        elif activity_name == "Registration Flow":
            steps = [
                (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
                (ChainStepType.TAP_ELEMENT, "Tap register button", {"locator_type": "id", "locator_value": "register_btn"}),
                (ChainStepType.TYPE_TEXT, "Enter first name", {"text": "John"}),
                (ChainStepType.TYPE_TEXT, "Enter last name", {"text": "Doe"}),
                (ChainStepType.TYPE_TEXT, "Enter email", {"text": "<EMAIL>"}),
                (ChainStepType.TYPE_TEXT, "Enter password", {"text": "SecurePass123"}),
                (ChainStepType.TAP_ELEMENT, "Accept terms", {"locator_type": "id", "locator_value": "terms_checkbox"}),
                (ChainStepType.TAP_ELEMENT, "Submit registration", {"locator_type": "id", "locator_value": "submit_btn"}),
                (ChainStepType.WAIT, "Wait for registration", {"duration": 5}),
                (ChainStepType.VERIFY_TEXT, "Verify registration success", {"text": "Registration successful"})
            ]
        elif activity_name == "Logout Flow":
            steps = [
                (ChainStepType.TAP_ELEMENT, "Open menu", {"locator_type": "id", "locator_value": "menu_btn"}),
                (ChainStepType.TAP_ELEMENT, "Tap logout", {"locator_type": "id", "locator_value": "logout_btn"}),
                (ChainStepType.TAP_ELEMENT, "Confirm logout", {"locator_type": "id", "locator_value": "confirm_logout"}),
                (ChainStepType.WAIT, "Wait for logout", {"duration": 3}),
                (ChainStepType.VERIFY_TEXT, "Verify logout success", {"text": "Login"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Logout complete", {})
            ]

    elif category == "🛒 E-commerce":
        if activity_name == "Product Search":
            steps = [
                (ChainStepType.LAUNCH_APP, "Launch shopping app", {"package_name": "com.example.shop"}),
                (ChainStepType.TAP_ELEMENT, "Tap search bar", {"locator_type": "id", "locator_value": "search_bar"}),
                (ChainStepType.TYPE_TEXT, "Enter search term", {"text": "smartphone"}),
                (ChainStepType.TAP_ELEMENT, "Tap search button", {"locator_type": "id", "locator_value": "search_btn"}),
                (ChainStepType.WAIT, "Wait for search results", {"duration": 3}),
                (ChainStepType.VERIFY_TEXT, "Verify results loaded", {"text": "results found"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Search results", {}),
                (ChainStepType.TAP_ELEMENT, "Tap first product", {"locator_type": "xpath", "locator_value": "//div[@class='product-item'][1]"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Product details", {})
            ]
        elif activity_name == "Add to Cart":
            steps = [
                (ChainStepType.VERIFY_ELEMENT, "Verify product page", {"locator_type": "id", "locator_value": "product_title"}),
                (ChainStepType.TAP_ELEMENT, "Add to cart", {"locator_type": "id", "locator_value": "add_to_cart_btn"}),
                (ChainStepType.WAIT, "Wait for cart update", {"duration": 2}),
                (ChainStepType.VERIFY_TEXT, "Verify added to cart", {"text": "Added to cart"}),
                (ChainStepType.TAP_ELEMENT, "View cart", {"locator_type": "id", "locator_value": "cart_icon"}),
                (ChainStepType.VERIFY_ELEMENT, "Verify item in cart", {"locator_type": "class_name", "locator_value": "cart-item"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Cart with item", {})
            ]
        elif activity_name == "Checkout Flow":
            steps = [
                (ChainStepType.TAP_ELEMENT, "Proceed to checkout", {"locator_type": "id", "locator_value": "checkout_btn"}),
                (ChainStepType.TYPE_TEXT, "Enter shipping address", {"text": "123 Main St"}),
                (ChainStepType.TAP_ELEMENT, "Select payment method", {"locator_type": "id", "locator_value": "payment_method"}),
                (ChainStepType.TAP_ELEMENT, "Place order", {"locator_type": "id", "locator_value": "place_order_btn"}),
                (ChainStepType.WAIT, "Wait for order processing", {"duration": 5}),
                (ChainStepType.VERIFY_TEXT, "Verify order success", {"text": "Order placed successfully"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Order confirmation", {})
            ]

    elif category == "📱 Navigation":
        if activity_name == "Menu Navigation":
            steps = [
                (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
                (ChainStepType.TAP_ELEMENT, "Open menu", {"locator_type": "id", "locator_value": "menu_btn"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Menu opened", {}),
                (ChainStepType.TAP_ELEMENT, "Navigate to settings", {"locator_type": "id", "locator_value": "menu_settings"}),
                (ChainStepType.VERIFY_TEXT, "Verify settings page", {"text": "Settings"}),
                (ChainStepType.PRESS_BACK, "Go back", {}),
                (ChainStepType.TAP_ELEMENT, "Navigate to profile", {"locator_type": "id", "locator_value": "menu_profile"}),
                (ChainStepType.VERIFY_TEXT, "Verify profile page", {"text": "Profile"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Profile page", {})
            ]
        elif activity_name == "Tab Switching":
            steps = [
                (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
                (ChainStepType.TAP_ELEMENT, "Tap Home tab", {"locator_type": "id", "locator_value": "tab_home"}),
                (ChainStepType.VERIFY_TEXT, "Verify Home tab", {"text": "Home"}),
                (ChainStepType.TAP_ELEMENT, "Tap Search tab", {"locator_type": "id", "locator_value": "tab_search"}),
                (ChainStepType.VERIFY_TEXT, "Verify Search tab", {"text": "Search"}),
                (ChainStepType.TAP_ELEMENT, "Tap Profile tab", {"locator_type": "id", "locator_value": "tab_profile"}),
                (ChainStepType.VERIFY_TEXT, "Verify Profile tab", {"text": "Profile"}),
                (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Tab navigation", {})
            ]

    else:
        # Default template for other categories
        steps = [
            (ChainStepType.LAUNCH_APP, f"Launch app for {activity_name}", {"package_name": "com.example.app"}),
            (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
            (ChainStepType.TAKE_SCREENSHOT, f"Screenshot: {activity_name} start", {}),
            (ChainStepType.VERIFY_TEXT, f"Verify {activity_name} loaded", {"text": "Welcome"}),
            (ChainStepType.TAKE_SCREENSHOT, f"Screenshot: {activity_name} complete", {})
        ]

    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def create_custom_activity_template(executor, chain, activity_name, activity_desc):
    """Create a custom activity template"""
    steps = [
        (ChainStepType.LAUNCH_APP, f"Launch app for {activity_name}", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, f"Screenshot: {activity_name} start", {}),
        (ChainStepType.VERIFY_TEXT, f"Verify {activity_name} screen", {"text": "Welcome"}),
        (ChainStepType.TAKE_SCREENSHOT, f"Screenshot: {activity_name} end", {})
    ]

    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def add_screenshot_template(executor, chain):
    """Add screenshot testing template"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: App launch", {}),
        (ChainStepType.SWIPE, "Swipe down", {"direction": "down", "distance": "medium"}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After swipe", {}),
        (ChainStepType.PRESS_HOME, "Go to home", {}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Home screen", {})
    ]

    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)
