"""
Streamlit interface for Chain-like Command System
Integrates with the existing simplified emulator testing interface
"""

import streamlit as st
import json
import base64
import time
from typing import Dict, List, Any
from test_chain_system import TestChainExecutor, ChainStepType, ChainStepStatus, TestChain, ChainStep

def render_chain_testing_interface(appium_driver):
    """Render the chain testing interface"""
    
    st.markdown("### 🔗 Chain-like Command System")
    st.markdown("Create automated test sequences by chaining multiple Appium operations together")
    
    # Initialize chain executor
    if 'chain_executor' not in st.session_state:
        st.session_state['chain_executor'] = TestChainExecutor(appium_driver)
    
    executor = st.session_state['chain_executor']
    
    # Check if device is connected
    if not appium_driver.is_connected():
        st.warning("⚠️ Please connect to a device first before creating test chains")
        return
    
    # Main interface tabs
    chain_tabs = st.tabs([
        "🔨 Chain Builder",
        "▶️ Execute Chain", 
        "📊 Results",
        "💾 Saved Chains",
        "🔧 Generated Code"
    ])
    
    with chain_tabs[0]:
        render_chain_builder(executor)
    
    with chain_tabs[1]:
        render_chain_executor(executor)
    
    with chain_tabs[2]:
        render_chain_results(executor)
    
    with chain_tabs[3]:
        render_saved_chains(executor)
    
    with chain_tabs[4]:
        render_generated_code(executor)

def render_chain_builder(executor):
    """Render the chain builder interface"""
    
    st.markdown("#### 🔨 Build Test Chain")
    
    # Chain metadata
    col1, col2 = st.columns(2)
    
    with col1:
        chain_name = st.text_input("Chain Name", placeholder="Login Test Chain")
        app_package = st.text_input("App Package (Optional)", placeholder="com.example.app")
    
    with col2:
        chain_description = st.text_area("Description", placeholder="Test user login flow")
    
    # Create new chain button
    if st.button("🆕 Create New Chain", type="primary"):
        if chain_name:
            try:
                chain = executor.create_chain(chain_name, chain_description, app_package)
                st.session_state['current_chain'] = chain
                st.success(f"✅ Created chain: {chain_name}")
                st.rerun()
            except Exception as e:
                st.error(f"❌ Failed to create chain: {str(e)}")
        else:
            st.error("Chain name is required")
    
    # Show current chain
    if 'current_chain' in st.session_state:
        chain = st.session_state['current_chain']
        st.markdown(f"**Current Chain:** {chain.name}")
        st.markdown(f"**Description:** {chain.description}")
        
        if chain.app_package:
            st.markdown(f"**App Package:** {chain.app_package}")
        
        st.markdown("---")
        
        # Add step interface
        st.markdown("#### ➕ Add Step to Chain")
        
        # Step type selection
        step_type_options = {
            "Launch App": ChainStepType.LAUNCH_APP,
            "Take Screenshot": ChainStepType.TAKE_SCREENSHOT,
            "Verify Element Exists": ChainStepType.VERIFY_ELEMENT,
            "Verify Text on Screen": ChainStepType.VERIFY_TEXT,
            "Tap Element": ChainStepType.TAP_ELEMENT,
            "Tap Coordinates": ChainStepType.TAP_COORDINATES,
            "Type Text": ChainStepType.TYPE_TEXT,
            "Swipe Gesture": ChainStepType.SWIPE,
            "Press Device Button": ChainStepType.PRESS_BUTTON,
            "Wait": ChainStepType.WAIT,
            "Scroll": ChainStepType.SCROLL
        }
        
        selected_step_type = st.selectbox(
            "Step Type",
            list(step_type_options.keys()),
            key="step_type_selector"
        )
        
        step_type = step_type_options[selected_step_type]
        
        # Dynamic parameter inputs based on step type
        parameters = {}
        step_description = ""
        
        if step_type == ChainStepType.LAUNCH_APP:
            col1, col2 = st.columns(2)
            with col1:
                package_name = st.text_input("Package Name", placeholder="com.example.app")
            with col2:
                activity = st.text_input("Activity (Optional)", placeholder="MainActivity")
            
            parameters = {"package_name": package_name, "activity": activity}
            step_description = f"Launch app: {package_name}"
        
        elif step_type == ChainStepType.TAKE_SCREENSHOT:
            parameters = {}
            step_description = "Take screenshot"
        
        elif step_type == ChainStepType.VERIFY_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type", ["id", "xpath", "class", "text", "partial_text"])
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login")
            
            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Verify element exists: {locator_type}={locator_value}"
        
        elif step_type == ChainStepType.VERIFY_TEXT:
            col1, col2 = st.columns(2)
            with col1:
                text_to_find = st.text_input("Text to Find", placeholder="Welcome")
            with col2:
                case_sensitive = st.checkbox("Case Sensitive")
            
            parameters = {"text": text_to_find, "case_sensitive": case_sensitive}
            step_description = f"Verify text: '{text_to_find}'"
        
        elif step_type == ChainStepType.TAP_ELEMENT:
            col1, col2 = st.columns(2)
            with col1:
                locator_type = st.selectbox("Locator Type", ["id", "xpath", "class", "text", "partial_text"], key="tap_locator_type")
            with col2:
                locator_value = st.text_input("Locator Value", placeholder="button_login", key="tap_locator_value")
            
            parameters = {"locator_type": locator_type, "locator_value": locator_value}
            step_description = f"Tap element: {locator_type}={locator_value}"
        
        elif step_type == ChainStepType.TAP_COORDINATES:
            col1, col2 = st.columns(2)
            with col1:
                x = st.number_input("X Coordinate", min_value=0, max_value=2000, value=500)
            with col2:
                y = st.number_input("Y Coordinate", min_value=0, max_value=2000, value=1000)
            
            parameters = {"x": x, "y": y}
            step_description = f"Tap coordinates: ({x}, {y})"
        
        elif step_type == ChainStepType.TYPE_TEXT:
            text_to_type = st.text_input("Text to Type", placeholder="<EMAIL>")
            parameters = {"text": text_to_type}
            step_description = f"Type text: '{text_to_type}'"
        
        elif step_type == ChainStepType.SWIPE:
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                start_x = st.number_input("Start X", min_value=0, max_value=2000, value=500)
            with col2:
                start_y = st.number_input("Start Y", min_value=0, max_value=2000, value=1500)
            with col3:
                end_x = st.number_input("End X", min_value=0, max_value=2000, value=500)
            with col4:
                end_y = st.number_input("End Y", min_value=0, max_value=2000, value=500)
            
            duration = st.number_input("Duration (ms)", min_value=100, max_value=5000, value=1000)
            
            parameters = {"start_x": start_x, "start_y": start_y, "end_x": end_x, "end_y": end_y, "duration": duration}
            step_description = f"Swipe from ({start_x}, {start_y}) to ({end_x}, {end_y})"
        
        elif step_type == ChainStepType.PRESS_BUTTON:
            button = st.selectbox("Button", ["home", "back", "menu"])
            parameters = {"button": button}
            step_description = f"Press {button} button"
        
        elif step_type == ChainStepType.WAIT:
            duration = st.number_input("Wait Duration (seconds)", min_value=0.1, max_value=30.0, value=2.0, step=0.1)
            parameters = {"duration": duration}
            step_description = f"Wait {duration} seconds"
        
        elif step_type == ChainStepType.SCROLL:
            direction = st.selectbox("Scroll Direction", ["up", "down"])
            parameters = {"direction": direction}
            step_description = f"Scroll {direction}"
        
        # Additional options
        col1, col2 = st.columns(2)
        with col1:
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=60, value=10)
        with col2:
            continue_on_failure = st.checkbox("Continue on Failure")
        
        # Custom description override
        custom_description = st.text_input("Custom Description (Optional)", placeholder=step_description)
        final_description = custom_description if custom_description else step_description
        
        # Add step button
        if st.button("➕ Add Step to Chain"):
            if step_type == ChainStepType.LAUNCH_APP and not parameters.get("package_name"):
                st.error("Package name is required for Launch App step")
            elif step_type in [ChainStepType.VERIFY_ELEMENT, ChainStepType.TAP_ELEMENT] and not parameters.get("locator_value"):
                st.error("Locator value is required for this step type")
            elif step_type == ChainStepType.VERIFY_TEXT and not parameters.get("text"):
                st.error("Text is required for Verify Text step")
            elif step_type == ChainStepType.TYPE_TEXT and not parameters.get("text"):
                st.error("Text is required for Type Text step")
            else:
                try:
                    step = executor.add_step(
                        step_type=step_type,
                        description=final_description,
                        parameters=parameters,
                        timeout=timeout,
                        continue_on_failure=continue_on_failure
                    )
                    st.success(f"✅ Added step: {final_description}")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Failed to add step: {str(e)}")
        
        # Show current steps
        if chain.steps:
            st.markdown("---")
            st.markdown("#### 📋 Current Steps")
            
            for i, step in enumerate(chain.steps):
                with st.expander(f"Step {i+1}: {step.description}", expanded=False):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write(f"**Type:** {step.step_type.value}")
                        st.write(f"**Timeout:** {step.timeout}s")
                        st.write(f"**Continue on Failure:** {step.continue_on_failure}")
                    
                    with col2:
                        st.write(f"**Status:** {step.status.value}")
                        if step.parameters:
                            st.write("**Parameters:**")
                            for key, value in step.parameters.items():
                                st.write(f"  • {key}: {value}")
                    
                    if st.button(f"🗑️ Remove Step {i+1}", key=f"remove_step_{i}"):
                        chain.steps.pop(i)
                        chain.total_steps = len(chain.steps)
                        st.success(f"Removed step {i+1}")
                        st.rerun()
        
        # Quick templates
        st.markdown("---")
        st.markdown("#### 🚀 Quick Templates")
        
        template_col1, template_col2 = st.columns(2)
        
        with template_col1:
            if st.button("📱 Basic App Launch Template"):
                add_app_launch_template(executor, chain)
                st.rerun()
        
        with template_col2:
            if st.button("🔐 Login Flow Template"):
                add_login_flow_template(executor, chain)
                st.rerun()

def add_app_launch_template(executor, chain):
    """Add basic app launch template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch target app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Take initial screenshot", {}),
        (ChainStepType.VERIFY_TEXT, "Verify app loaded", {"text": "Welcome", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def add_login_flow_template(executor, chain):
    """Add login flow template steps"""
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Initial screen", {}),
        (ChainStepType.TAP_ELEMENT, "Tap username field", {"locator_type": "id", "locator_value": "username"}),
        (ChainStepType.TYPE_TEXT, "Enter username", {"text": "<EMAIL>"}),
        (ChainStepType.TAP_ELEMENT, "Tap password field", {"locator_type": "id", "locator_value": "password"}),
        (ChainStepType.TYPE_TEXT, "Enter password", {"text": "password123"}),
        (ChainStepType.TAP_ELEMENT, "Tap login button", {"locator_type": "id", "locator_value": "login_button"}),
        (ChainStepType.WAIT, "Wait for login", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After login", {}),
        (ChainStepType.VERIFY_TEXT, "Verify login success", {"text": "Dashboard", "case_sensitive": False})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)

def render_chain_executor(executor):
    """Render the chain execution interface"""

    st.markdown("#### ▶️ Execute Test Chain")

    if 'current_chain' not in st.session_state:
        st.info("📝 Create a chain first in the Chain Builder tab")
        return

    chain = st.session_state['current_chain']

    if not chain.steps:
        st.warning("⚠️ Add some steps to your chain before executing")
        return

    # Chain summary
    st.markdown(f"**Chain:** {chain.name}")
    st.markdown(f"**Description:** {chain.description}")
    st.markdown(f"**Total Steps:** {len(chain.steps)}")

    # Execution options
    col1, col2 = st.columns(2)

    with col1:
        dry_run = st.checkbox("🔍 Dry Run (Validate Only)", help="Check chain without executing")
        stop_on_error = st.checkbox("🛑 Stop on First Error", value=True)

    with col2:
        save_screenshots = st.checkbox("📸 Save Screenshots", value=True)
        generate_report = st.checkbox("📊 Generate Report", value=True)

    # Execute button
    if st.button("▶️ Execute Chain", type="primary", use_container_width=True):
        if dry_run:
            st.info("🔍 Performing dry run validation...")
            # Validate chain without execution
            validation_results = validate_chain(chain)
            display_validation_results(validation_results)
        else:
            st.info("▶️ Executing test chain...")
            execute_chain_with_progress(executor, chain)

def validate_chain(chain):
    """Validate chain without execution"""
    results = {
        "valid": True,
        "warnings": [],
        "errors": [],
        "step_validations": []
    }

    for i, step in enumerate(chain.steps):
        step_result = {"step": i+1, "description": step.description, "issues": []}

        # Validate step parameters
        if step.step_type == ChainStepType.LAUNCH_APP:
            if not step.parameters.get("package_name"):
                step_result["issues"].append("Missing package_name parameter")
                results["errors"].append(f"Step {i+1}: Missing package_name")
                results["valid"] = False

        elif step.step_type in [ChainStepType.VERIFY_ELEMENT, ChainStepType.TAP_ELEMENT]:
            if not step.parameters.get("locator_value"):
                step_result["issues"].append("Missing locator_value parameter")
                results["errors"].append(f"Step {i+1}: Missing locator_value")
                results["valid"] = False

        elif step.step_type == ChainStepType.VERIFY_TEXT:
            if not step.parameters.get("text"):
                step_result["issues"].append("Missing text parameter")
                results["errors"].append(f"Step {i+1}: Missing text parameter")
                results["valid"] = False

        elif step.step_type == ChainStepType.TYPE_TEXT:
            if not step.parameters.get("text"):
                step_result["issues"].append("Missing text parameter")
                results["errors"].append(f"Step {i+1}: Missing text parameter")
                results["valid"] = False

        elif step.step_type == ChainStepType.TAP_COORDINATES:
            if step.parameters.get("x") is None or step.parameters.get("y") is None:
                step_result["issues"].append("Missing x or y coordinates")
                results["errors"].append(f"Step {i+1}: Missing coordinates")
                results["valid"] = False

        # Check for potential issues
        if step.timeout < 1:
            step_result["issues"].append("Very short timeout (< 1s)")
            results["warnings"].append(f"Step {i+1}: Short timeout")

        results["step_validations"].append(step_result)

    return results

def display_validation_results(results):
    """Display validation results"""
    if results["valid"]:
        st.success("✅ Chain validation passed!")
    else:
        st.error("❌ Chain validation failed!")

    if results["errors"]:
        st.markdown("**❌ Errors:**")
        for error in results["errors"]:
            st.write(f"• {error}")

    if results["warnings"]:
        st.markdown("**⚠️ Warnings:**")
        for warning in results["warnings"]:
            st.write(f"• {warning}")

    # Detailed step validation
    with st.expander("📋 Detailed Step Validation", expanded=False):
        for step_val in results["step_validations"]:
            if step_val["issues"]:
                st.write(f"**Step {step_val['step']}: {step_val['description']}**")
                for issue in step_val["issues"]:
                    st.write(f"  ❌ {issue}")
            else:
                st.write(f"✅ Step {step_val['step']}: {step_val['description']}")

def execute_chain_with_progress(executor, chain):
    """Execute chain with progress tracking"""

    # Create progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()

    # Results container
    results_container = st.container()

    try:
        # Execute chain
        execution_results = executor.execute_chain(chain)

        # Update progress and display results
        with results_container:
            display_execution_results(execution_results, chain)

        # Store results in session state
        st.session_state['last_execution_results'] = execution_results
        st.session_state['executed_chain'] = chain

        progress_bar.progress(1.0)
        status_text.success("✅ Chain execution completed!")

    except Exception as e:
        status_text.error(f"❌ Chain execution failed: {str(e)}")
        st.error(f"Execution error: {str(e)}")

def display_execution_results(results, chain):
    """Display chain execution results"""

    # Overall results
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Steps", results["total_steps"])

    with col2:
        completed = len([r for r in results["step_results"] if r["status"] == "success"])
        st.metric("Completed", completed)

    with col3:
        failed = len([r for r in results["step_results"] if r["status"] == "failed"])
        st.metric("Failed", failed)

    with col4:
        st.metric("Execution Time", f"{results['execution_time']:.2f}s")

    # Overall status
    if results["overall_success"]:
        st.success("🎉 Chain executed successfully!")
    else:
        st.error("❌ Chain execution failed")

    # Step-by-step results
    st.markdown("#### 📋 Step Results")

    for i, step_result in enumerate(results["step_results"]):
        step = chain.steps[i]

        # Status icon
        if step_result["status"] == "success":
            status_icon = "✅"
            status_color = "green"
        elif step_result["status"] == "failed":
            status_icon = "❌"
            status_color = "red"
        else:
            status_icon = "⏸️"
            status_color = "orange"

        with st.expander(f"{status_icon} Step {i+1}: {step.description}", expanded=(step_result["status"] == "failed")):
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**Status:** {step_result['status']}")
                st.write(f"**Execution Time:** {step_result['execution_time']:.2f}s")
                st.write(f"**Step Type:** {step_result['step_type']}")

            with col2:
                if step_result.get("result"):
                    st.write("**Result:**")
                    st.json(step_result["result"])

            if step_result.get("error"):
                st.error(f"**Error:** {step_result['error']}")

            # Show screenshot if available
            if step.screenshot_data:
                try:
                    screenshot_data = base64.b64decode(step.screenshot_data)
                    st.image(screenshot_data, caption=f"Screenshot after step {i+1}", width=300)
                except:
                    st.write("Screenshot available but failed to display")

def render_chain_results(executor):
    """Render chain results and history"""

    st.markdown("#### 📊 Execution Results")

    if 'last_execution_results' not in st.session_state:
        st.info("📝 Execute a chain to see results here")
        return

    results = st.session_state['last_execution_results']
    chain = st.session_state.get('executed_chain')

    if not results or not chain:
        st.info("📝 No execution results available")
        return

    # Display results
    display_execution_results(results, chain)

def render_saved_chains(executor):
    """Render saved chains interface"""

    st.markdown("#### 💾 Saved Chains")
    st.info("🚧 Chain saving/loading functionality - Coming soon!")

    # Placeholder for future implementation
    st.markdown("**Features to be implemented:**")
    st.write("• Save chains to file")
    st.write("• Load chains from file")
    st.write("• Chain library management")
    st.write("• Share chains between users")

def render_generated_code(executor):
    """Render generated Appium code interface"""

    st.markdown("#### 🔧 Generated Appium Code")

    if 'executed_chain' not in st.session_state:
        st.info("📝 Execute a chain to see generated code here")
        return

    chain = st.session_state['executed_chain']

    if not chain.generated_code:
        st.warning("⚠️ No generated code available")
        return

    st.markdown("**Generated Appium Test Case:**")

    # Display code with syntax highlighting
    st.code(chain.generated_code, language="python")

    # Download button
    st.download_button(
        label="💾 Download Python Code",
        data=chain.generated_code,
        file_name=f"{chain.name.replace(' ', '_')}_test.py",
        mime="text/x-python"
    )

    # Code explanation
    with st.expander("📖 Code Explanation", expanded=False):
        st.markdown("""
        **Generated Code Features:**

        • **Complete Appium test case** - Ready to run with minimal setup
        • **Proper error handling** - Try-catch blocks for robust execution
        • **WebDriverWait usage** - Explicit waits for reliable element interactions
        • **Step-by-step comments** - Clear documentation of each action
        • **Assertion statements** - Verification steps for test validation
        • **Standard Appium patterns** - Industry best practices

        **To use this code:**
        1. Install Appium and required drivers
        2. Set up your desired capabilities
        3. Initialize the WebDriver
        4. Create an instance of the test class
        5. Call the test method
        """)

    # Code customization options
    st.markdown("---")
    st.markdown("#### ⚙️ Code Customization")

    col1, col2 = st.columns(2)

    with col1:
        include_setup = st.checkbox("Include WebDriver Setup", value=False)
        include_teardown = st.checkbox("Include Teardown", value=True)

    with col2:
        add_logging = st.checkbox("Add Logging Statements", value=False)
        add_screenshots = st.checkbox("Add Screenshot Capture", value=True)

    if st.button("🔄 Regenerate Code with Options"):
        # Regenerate code with custom options
        custom_code = generate_custom_code(chain, include_setup, include_teardown, add_logging, add_screenshots)
        st.code(custom_code, language="python")

        st.download_button(
            label="💾 Download Custom Code",
            data=custom_code,
            file_name=f"{chain.name.replace(' ', '_')}_custom_test.py",
            mime="text/x-python"
        )

def generate_custom_code(chain, include_setup=False, include_teardown=True, add_logging=False, add_screenshots=True):
    """Generate customized Appium code"""
    code_lines = []

    # Header with imports
    code_lines.extend([
        "# Generated Appium Test Case",
        f"# Test: {chain.name}",
        f"# Description: {chain.description}",
        f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "import time",
        "from appium import webdriver",
        "from selenium.webdriver.common.by import By",
        "from selenium.webdriver.support.ui import WebDriverWait",
        "from selenium.webdriver.support import expected_conditions as EC"
    ])

    if add_logging:
        code_lines.extend([
            "import logging",
            "",
            "# Configure logging",
            "logging.basicConfig(level=logging.INFO)",
            "logger = logging.getLogger(__name__)"
        ])

    code_lines.append("")

    # WebDriver setup if requested
    if include_setup:
        code_lines.extend([
            "# Desired Capabilities",
            "desired_caps = {",
            "    'platformName': 'Android',",
            "    'deviceName': 'emulator-5554',",
            "    'automationName': 'UiAutomator2',",
            "    'newCommandTimeout': 300",
            "}",
            "",
            "# Initialize WebDriver",
            "driver = webdriver.Remote('http://localhost:4723', desired_caps)",
            "wait = WebDriverWait(driver, 10)",
            ""
        ])

    # Test class
    code_lines.extend([
        "class GeneratedTestCase:",
        "    def __init__(self, driver):",
        "        self.driver = driver",
        "        self.wait = WebDriverWait(driver, 10)",
        ""
    ])

    if add_logging:
        code_lines.extend([
            "        self.logger = logging.getLogger(__name__)",
            ""
        ])

    # Test method
    code_lines.extend([
        f"    def test_{chain.name.lower().replace(' ', '_')}(self):",
        f'        """',
        f'        {chain.description}',
        f'        """'
    ])

    if add_logging:
        code_lines.append(f'        self.logger.info("Starting test: {chain.name}")')

    code_lines.append("        try:")

    # Generate steps with custom options
    for i, step in enumerate(chain.steps):
        if add_logging:
            code_lines.append(f'            self.logger.info("Step {i+1}: {step.description}")')

        code_lines.append(f"            # Step {i+1}: {step.description}")

        # Add step-specific code (reuse from original generator)
        # ... (step generation logic would go here)

        if add_screenshots and step.step_type != ChainStepType.TAKE_SCREENSHOT:
            code_lines.extend([
                f"            # Take screenshot after step {i+1}",
                f"            screenshot = self.driver.get_screenshot_as_base64()",
                f"            # Save screenshot as needed"
            ])

        code_lines.append("")

    # Success logging
    if add_logging:
        code_lines.append('            self.logger.info("Test completed successfully")')

    code_lines.append("            print('Test completed successfully')")

    # Error handling
    code_lines.extend([
        "",
        "        except Exception as e:"
    ])

    if add_logging:
        code_lines.append('            self.logger.error(f"Test failed: {str(e)}")')

    code_lines.extend([
        "            print(f'Test failed: {str(e)}')",
        "            raise"
    ])

    # Teardown
    if include_teardown:
        code_lines.extend([
            "",
            "        finally:",
            "            # Cleanup",
            "            try:",
            "                self.driver.quit()",
            "            except:",
            "                pass"
        ])
    else:
        code_lines.extend([
            "",
            "        finally:",
            "            # Cleanup if needed",
            "            pass"
        ])

    # Main execution if setup included
    if include_setup:
        code_lines.extend([
            "",
            "if __name__ == '__main__':",
            "    # Run the test",
            "    test_case = GeneratedTestCase(driver)",
            f"    test_case.test_{chain.name.lower().replace(' ', '_')}()"
        ])

    return "\n".join(code_lines)
