"""
Integrated HAR File Comparison System
Complete implementation based on index.html reference with all features
"""

import streamlit as st
import pandas as pd
import json
import base64
import difflib
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from urllib.parse import urlparse
import re

class IntegratedHARComparison:
    """Complete HAR comparison system with all index.html features"""
    
    def __init__(self):
        self.comparison_results = []
        self.ignored_urls = [
            "dashboard.paytm.com/next/p4b",
            "dashboard.paytm.com/next/micro/common",
            "google-analytics.com",
            "googletagmanager.com",
            "doubleclick.net",
            "facebook.com",
            "cdn.jsdelivr.net"
        ]
        
    def decode_response(self, response: str) -> str:
        """Decode base64 response content"""
        try:
            return base64.b64decode(response).decode('utf-8')
        except:
            return response
    
    def filter_paytm_urls(self, entries: List[Dict]) -> List[Dict]:
        """Filter entries to include only Paytm dashboard URLs"""
        filtered = []
        for entry in entries:
            url = entry.get('request', {}).get('url', '')
            if ('dashboard.paytm.com' in url and 
                not any(ignored in url for ignored in self.ignored_urls)):
                filtered.append(entry)
        return filtered
    
    def parse_har_file(self, file_content: bytes) -> Dict[str, Any]:
        """Parse HAR file content"""
        try:
            content_str = file_content.decode('utf-8')
            data = json.loads(content_str)
            
            if 'log' in data and 'entries' in data['log']:
                return {
                    'success': True,
                    'entries': data['log']['entries'],
                    'format': 'HAR'
                }
            else:
                return {
                    'success': False,
                    'error': 'Invalid HAR format',
                    'entries': []
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'entries': []
            }
    
    def get_highlighted_mismatched_keys(self, res1: str, res2: str) -> Dict[str, Any]:
        """Compare JSON responses and highlight differences"""
        try:
            obj1 = json.loads(res1)
            obj2 = json.loads(res2)
            
            mismatches = []
            added_keys = []
            removed_keys = []
            
            def compare_objects(o1, o2, prefix=''):
                # Find modified and removed keys
                if isinstance(o1, dict) and isinstance(o2, dict):
                    for key in o1.keys():
                        full_key = f"{prefix}.{key}" if prefix else key
                        if key not in o2:
                            removed_keys.append(full_key)
                        elif json.dumps(o1[key], sort_keys=True) != json.dumps(o2[key], sort_keys=True):
                            mismatches.append(full_key)
                            if isinstance(o1[key], dict) and isinstance(o2[key], dict):
                                compare_objects(o1[key], o2[key], full_key)
                    
                    # Find added keys
                    for key in o2.keys():
                        full_key = f"{prefix}.{key}" if prefix else key
                        if key not in o1:
                            added_keys.append(full_key)
            
            compare_objects(obj1, obj2)
            
            return {
                'mismatch_keys': mismatches,
                'added_keys': added_keys,
                'removed_keys': removed_keys,
                'highlighted_response1': json.dumps(obj1, indent=2),
                'highlighted_response2': json.dumps(obj2, indent=2)
            }
            
        except Exception as e:
            return {
                'mismatch_keys': ['Invalid JSON Response'],
                'added_keys': [],
                'removed_keys': [],
                'highlighted_response1': res1,
                'highlighted_response2': res2
            }
    
    def process_comparison(self, har1_data: Dict, har2_data: Dict) -> Dict[str, Any]:
        """Process complete HAR file comparison"""
        if not har1_data['success'] or not har2_data['success']:
            return {
                'success': False,
                'error': 'Failed to parse HAR files'
            }
        
        # Filter Paytm entries
        paytm_entries1 = self.filter_paytm_urls(har1_data['entries'])
        paytm_entries2 = self.filter_paytm_urls(har2_data['entries'])
        
        # Reset comparison results
        self.comparison_results = []
        
        total_requests = 0
        matched_responses = 0
        mismatched_responses = 0
        seen_urls = set()
        
        passed_results = []
        failed_results = []
        
        for entry1 in paytm_entries1:
            url = entry1['request']['url']
            if url in seen_urls:
                continue
            seen_urls.add(url)
            total_requests += 1
            
            # Find matching entry in second HAR
            matching_entry2 = None
            for entry2 in paytm_entries2:
                if entry2['request']['url'] == url:
                    matching_entry2 = entry2
                    break
            
            # Get responses
            response1 = self.decode_response(entry1.get('response', {}).get('content', {}).get('text', ''))
            response2 = self.decode_response(matching_entry2.get('response', {}).get('content', {}).get('text', '')) if matching_entry2 else "Not Found"
            
            # Compare responses
            comparison = self.get_highlighted_mismatched_keys(response1, response2)
            schema_status = "✅ Passed" if len(comparison['mismatch_keys']) == 0 else "❌ Failed"
            
            # Store comparison result
            result = {
                'url': url,
                'schema_status': schema_status,
                'mismatch_keys': comparison['mismatch_keys'],
                'added_keys': comparison['added_keys'],
                'removed_keys': comparison['removed_keys'],
                'response1': response1,
                'response2': response2,
                'highlighted_response1': comparison['highlighted_response1'],
                'highlighted_response2': comparison['highlighted_response2']
            }
            
            self.comparison_results.append(result)
            
            if len(comparison['mismatch_keys']) == 0:
                matched_responses += 1
                passed_results.append(result)
            else:
                mismatched_responses += 1
                failed_results.append(result)
        
        return {
            'success': True,
            'total_requests': total_requests,
            'matched_responses': matched_responses,
            'mismatched_responses': mismatched_responses,
            'passed_results': passed_results,
            'failed_results': failed_results,
            'comparison_results': self.comparison_results
        }
    
    def generate_ai_analysis(self) -> Dict[str, Any]:
        """Generate AI-powered analysis of comparison results"""
        if not self.comparison_results:
            return {'error': 'No comparison data available'}
        
        failed_apis = [r for r in self.comparison_results if 'Failed' in r['schema_status']]
        total_mismatches = sum(len(api['mismatch_keys']) for api in failed_apis)
        
        total_requests = len(self.comparison_results)
        matched_responses = len([r for r in self.comparison_results if 'Passed' in r['schema_status']])
        pass_rate = round((matched_responses / total_requests) * 100) if total_requests > 0 else 0
        
        # Analyze common patterns
        all_mismatches = []
        for api in failed_apis:
            all_mismatches.extend(api['mismatch_keys'])
        
        key_frequency = {}
        for key in all_mismatches:
            base_key = key.split('.')[0]
            key_frequency[base_key] = key_frequency.get(base_key, 0) + 1
        
        top_issue_keys = sorted(key_frequency.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            'pass_rate': pass_rate,
            'failed_apis_count': len(failed_apis),
            'total_mismatches': total_mismatches,
            'top_issue_keys': top_issue_keys,
            'recommendations': [
                "Review the failed endpoints, particularly those with critical functionality",
                "Check for version differences between environments",
                "Verify if the differences are expected due to environment configurations",
                "Consider updating documentation if the changes are intentional"
            ],
            'top_apis_to_investigate': failed_apis[:3]
        }
    
    def export_to_json(self) -> str:
        """Export comparison results to JSON"""
        return json.dumps(self.comparison_results, indent=2)
    
    def generate_report_data(self) -> Dict[str, Any]:
        """Generate report data for PDF/HTML export"""
        if not self.comparison_results:
            return {'error': 'No comparison data available'}
        
        total_requests = len(self.comparison_results)
        passed = len([r for r in self.comparison_results if 'Passed' in r['schema_status']])
        failed = len([r for r in self.comparison_results if 'Failed' in r['schema_status']])
        
        return {
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_requests': total_requests,
                'passed': passed,
                'failed': failed
            },
            'detailed_results': [
                {
                    'url': result['url'],
                    'status': result['schema_status'],
                    'discrepancies': ', '.join(result['mismatch_keys']) if result['mismatch_keys'] else 'None'
                }
                for result in self.comparison_results
            ]
        }

def render_integrated_har_comparison_tab():
    """Render the complete integrated HAR comparison tab"""
    st.markdown("## 🔄 Paytm Dashboard API Response Comparison")
    st.markdown("*Complete HAR file comparison with advanced analysis and filtering*")

    # Initialize comparison engine
    if 'har_comparison_engine' not in st.session_state:
        st.session_state['har_comparison_engine'] = IntegratedHARComparison()

    engine = st.session_state['har_comparison_engine']

    # File upload section
    st.markdown("### 📁 Upload HAR Files")
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Upload First HAR File:**")
        file1 = st.file_uploader(
            "Choose first HAR file",
            type=['har'],
            key="integrated_har_file1",
            help="Upload the first HAR file for comparison"
        )
        if file1:
            st.success(f"✅ {file1.name} loaded ({file1.size:,} bytes)")

    with col2:
        st.markdown("**Upload Second HAR File:**")
        file2 = st.file_uploader(
            "Choose second HAR file",
            type=['har'],
            key="integrated_har_file2",
            help="Upload the second HAR file for comparison"
        )
        if file2:
            st.success(f"✅ {file2.name} loaded ({file2.size:,} bytes)")

    # Compare button
    if file1 and file2:
        if st.button("🔄 Compare HAR Files", type="primary", use_container_width=True):
            with st.spinner("Processing files, please wait..."):
                # Parse HAR files
                har1_data = engine.parse_har_file(file1.read())
                har2_data = engine.parse_har_file(file2.read())

                # Process comparison
                comparison_result = engine.process_comparison(har1_data, har2_data)

                if comparison_result['success']:
                    st.session_state['comparison_result'] = comparison_result
                    st.success("✅ Comparison completed successfully!")
                else:
                    st.error(f"❌ Comparison failed: {comparison_result.get('error', 'Unknown error')}")

    # Display results if available
    if 'comparison_result' in st.session_state:
        render_comparison_results(st.session_state['comparison_result'], engine)

def render_comparison_results(result: Dict[str, Any], engine: IntegratedHARComparison):
    """Render comprehensive comparison results"""
    st.markdown("---")

    # Search and filter section
    st.markdown("### 🔍 Search and Filter")
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        search_query = st.text_input(
            "Search by URL or text...",
            key="har_search_bar",
            placeholder="Enter search terms..."
        )

    with col2:
        filter_type = st.selectbox(
            "Filter Type",
            options=["All Requests", "Passed Only", "Failed Only"],
            key="har_filter_type"
        )

    with col3:
        sort_by = st.selectbox(
            "Sort By",
            options=["URL", "Status"],
            key="har_sort_by"
        )

    # Export and analysis buttons
    st.markdown("### 📊 Actions")
    action_col1, action_col2, action_col3 = st.columns(3)

    with action_col1:
        if st.button("📄 Export as JSON", use_container_width=True):
            json_data = engine.export_to_json()
            st.download_button(
                label="Download JSON",
                data=json_data,
                file_name=f"comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

    with action_col2:
        if st.button("📋 Generate Report", use_container_width=True):
            report_data = engine.generate_report_data()
            if 'error' not in report_data:
                st.session_state['report_data'] = report_data
                st.success("Report generated! Check the Export tab.")

    with action_col3:
        if st.button("🤖 AI Analysis", use_container_width=True):
            ai_analysis = engine.generate_ai_analysis()
            if 'error' not in ai_analysis:
                st.session_state['ai_analysis'] = ai_analysis
                st.success("AI analysis completed!")

    # Summary cards
    st.markdown("### 📊 Summary")
    summary_col1, summary_col2, summary_col3 = st.columns(3)

    with summary_col1:
        st.metric(
            "Total Requests",
            result['total_requests'],
            help="Total number of unique requests processed"
        )

    with summary_col2:
        st.metric(
            "✅ Passed Responses",
            result['matched_responses'],
            delta=f"{(result['matched_responses']/result['total_requests']*100):.1f}%" if result['total_requests'] > 0 else "0%"
        )

    with summary_col3:
        st.metric(
            "❌ Failed Responses",
            result['mismatched_responses'],
            delta=f"{(result['mismatched_responses']/result['total_requests']*100):.1f}%" if result['total_requests'] > 0 else "0%",
            delta_color="inverse"
        )

    # Results tabs
    results_tabs = st.tabs([
        "✅ Passed Results",
        "❌ Failed Results",
        "🤖 AI Analysis",
        "📋 Report",
        "📊 Visualizations"
    ])

    with results_tabs[0]:
        render_passed_results(result['passed_results'], search_query, filter_type)

    with results_tabs[1]:
        render_failed_results(result['failed_results'], search_query, filter_type)

    with results_tabs[2]:
        render_ai_analysis_results()

    with results_tabs[3]:
        render_report_section()

    with results_tabs[4]:
        render_visualizations(result)

def render_passed_results(passed_results: List[Dict], search_query: str, filter_type: str):
    """Render passed API results"""
    if filter_type == "Failed Only":
        st.info("No results to show (filtered to Failed Only)")
        return

    # Filter results based on search
    filtered_results = passed_results
    if search_query:
        filtered_results = [
            r for r in passed_results
            if search_query.lower() in r['url'].lower()
        ]

    if not filtered_results:
        st.info("No passed results found matching your criteria")
        return

    st.markdown(f"**Showing {len(filtered_results)} passed API(s)**")

    for i, result in enumerate(filtered_results):
        with st.expander(f"✅ {result['url'][:80]}{'...' if len(result['url']) > 80 else ''}", expanded=False):
            st.markdown(f"**URL:** `{result['url']}`")
            st.markdown(f"**Status:** {result['schema_status']}")

            # Show response comparison
            resp_col1, resp_col2 = st.columns(2)

            with resp_col1:
                st.markdown("**Response 1:**")
                st.code(result['highlighted_response1'][:1000] + ('...' if len(result['highlighted_response1']) > 1000 else ''), language='json')

            with resp_col2:
                st.markdown("**Response 2:**")
                st.code(result['highlighted_response2'][:1000] + ('...' if len(result['highlighted_response2']) > 1000 else ''), language='json')

def render_failed_results(failed_results: List[Dict], search_query: str, filter_type: str):
    """Render failed API results with detailed differences"""
    if filter_type == "Passed Only":
        st.info("No results to show (filtered to Passed Only)")
        return

    # Filter results based on search
    filtered_results = failed_results
    if search_query:
        filtered_results = [
            r for r in failed_results
            if search_query.lower() in r['url'].lower()
        ]

    if not filtered_results:
        st.info("No failed results found matching your criteria")
        return

    st.markdown(f"**Showing {len(filtered_results)} failed API(s)**")

    for i, result in enumerate(filtered_results):
        with st.expander(f"❌ {result['url'][:80]}{'...' if len(result['url']) > 80 else ''}", expanded=False):
            st.markdown(f"**URL:** `{result['url']}`")
            st.markdown(f"**Status:** {result['schema_status']}")

            # Show mismatched keys
            if result['mismatch_keys']:
                st.markdown("**🔍 Mismatched Keys:**")
                for key in result['mismatch_keys']:
                    st.markdown(f"- `{key}`")

            if result['added_keys']:
                st.markdown("**➕ Added Keys:**")
                for key in result['added_keys']:
                    st.markdown(f"- `{key}`")

            if result['removed_keys']:
                st.markdown("**➖ Removed Keys:**")
                for key in result['removed_keys']:
                    st.markdown(f"- `{key}`")

            # Show response comparison
            st.markdown("**📊 Response Comparison:**")
            resp_col1, resp_col2 = st.columns(2)

            with resp_col1:
                st.markdown("**Response 1:**")
                st.code(result['highlighted_response1'][:1000] + ('...' if len(result['highlighted_response1']) > 1000 else ''), language='json')

            with resp_col2:
                st.markdown("**Response 2:**")
                st.code(result['highlighted_response2'][:1000] + ('...' if len(result['highlighted_response2']) > 1000 else ''), language='json')

def render_ai_analysis_results():
    """Render AI analysis results"""
    if 'ai_analysis' not in st.session_state:
        st.info("Run AI Analysis to see intelligent insights about your comparison results")
        return

    analysis = st.session_state['ai_analysis']

    if 'error' in analysis:
        st.error(f"AI Analysis Error: {analysis['error']}")
        return

    # Analysis summary
    st.markdown("### 🤖 AI Analysis Summary")

    # Compatibility score
    pass_rate = analysis['pass_rate']
    if pass_rate > 80:
        st.success(f"**API Compatibility Score: {pass_rate}%** - Excellent compatibility!")
    elif pass_rate > 60:
        st.warning(f"**API Compatibility Score: {pass_rate}%** - Good compatibility with some issues")
    else:
        st.error(f"**API Compatibility Score: {pass_rate}%** - Poor compatibility, needs attention")

    # Key findings
    st.markdown("### 🔍 Key Findings")
    col1, col2 = st.columns(2)

    with col1:
        st.metric("Failed APIs", analysis['failed_apis_count'])
        st.metric("Total Mismatches", analysis['total_mismatches'])

    with col2:
        if analysis['top_issue_keys']:
            st.markdown("**Most Common Issues:**")
            for key, count in analysis['top_issue_keys']:
                st.markdown(f"- `{key}` ({count} occurrences)")

    # Recommendations
    st.markdown("### 💡 Recommendations")
    for i, rec in enumerate(analysis['recommendations'], 1):
        st.markdown(f"{i}. {rec}")

    # Top APIs to investigate
    if analysis['top_apis_to_investigate']:
        st.markdown("### 🎯 Top APIs to Investigate")
        for api in analysis['top_apis_to_investigate']:
            st.markdown(f"- **{api['url'].split('?')[0]}** - {len(api['mismatch_keys'])} differences")

def render_report_section():
    """Render report generation and preview"""
    if 'report_data' not in st.session_state:
        st.info("Generate a report to see detailed analysis")
        return

    report = st.session_state['report_data']

    if 'error' in report:
        st.error(f"Report Error: {report['error']}")
        return

    st.markdown("### 📋 Automated Test Report")
    st.markdown(f"**Generated:** {report['date']}")

    # Summary table
    st.markdown("#### Summary")
    summary_df = pd.DataFrame([report['summary']])
    st.dataframe(summary_df, use_container_width=True)

    # Detailed results
    st.markdown("#### Detailed Results")
    details_df = pd.DataFrame(report['detailed_results'])
    st.dataframe(details_df, use_container_width=True)

    # Export options
    st.markdown("#### Export Options")
    col1, col2 = st.columns(2)

    with col1:
        # CSV export
        csv_data = details_df.to_csv(index=False)
        st.download_button(
            label="📊 Download CSV Report",
            data=csv_data,
            file_name=f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    with col2:
        # JSON export
        json_data = json.dumps(report, indent=2)
        st.download_button(
            label="📄 Download JSON Report",
            data=json_data,
            file_name=f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )

def render_visualizations(result: Dict[str, Any]):
    """Render comparison visualizations"""
    st.markdown("### 📊 Comparison Visualizations")

    # Summary pie chart
    fig_pie = go.Figure(data=[go.Pie(
        labels=['Passed', 'Failed'],
        values=[result['matched_responses'], result['mismatched_responses']],
        hole=0.3,
        marker_colors=['#28a745', '#dc3545']
    )])

    fig_pie.update_layout(
        title="API Response Comparison Results",
        showlegend=True,
        height=400
    )

    st.plotly_chart(fig_pie, use_container_width=True)

    # Status distribution bar chart
    if result['comparison_results']:
        status_data = []
        for res in result['comparison_results']:
            status_data.append({
                'URL': res['url'][:50] + '...' if len(res['url']) > 50 else res['url'],
                'Status': 'Passed' if 'Passed' in res['schema_status'] else 'Failed',
                'Mismatch Count': len(res['mismatch_keys'])
            })

        status_df = pd.DataFrame(status_data)

        # Bar chart of mismatch counts
        fig_bar = px.bar(
            status_df,
            x='URL',
            y='Mismatch Count',
            color='Status',
            title='Mismatch Count by API Endpoint',
            color_discrete_map={'Passed': '#28a745', 'Failed': '#dc3545'}
        )

        fig_bar.update_layout(
            xaxis_tickangle=-45,
            height=500
        )

        st.plotly_chart(fig_bar, use_container_width=True)
