"""
Integrated HAR File Comparison System
Complete implementation based on index.html reference with all features
"""

import streamlit as st
import pandas as pd
import json
import base64
import difflib
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from urllib.parse import urlparse
import re

class IntegratedHARComparison:
    """Complete HAR comparison system with all index.html features"""
    
    def __init__(self):
        self.comparison_results = []
        self.ignored_urls = [
            "dashboard.paytm.com/next/p4b",
            "dashboard.paytm.com/next/micro/common",
            "google-analytics.com",
            "googletagmanager.com",
            "doubleclick.net",
            "facebook.com",
            "cdn.jsdelivr.net"
        ]
        
    def decode_response(self, response: str) -> str:
        """Decode base64 response content"""
        if not response:
            return "{}"

        try:
            # Try to decode as base64 first
            decoded = base64.b64decode(response).decode('utf-8')
            # Validate it's valid JSON
            json.loads(decoded)
            return decoded
        except:
            try:
                # If base64 fails, try as plain text and validate JSO<PERSON>
                json.loads(response)
                return response
            except:
                # If neither works, return empty JSON
                return "{}"
    
    def filter_paytm_urls(self, entries: List[Dict]) -> List[Dict]:
        """Filter entries to include only Paytm dashboard URLs"""
        filtered = []
        for entry in entries:
            url = entry.get('request', {}).get('url', '') or ''
            if 'dashboard.paytm.com' in url:
                # Check if URL should be ignored
                should_ignore = False
                for ignored in self.ignored_urls:
                    if ignored and ignored in url:
                        should_ignore = True
                        break

                if not should_ignore:
                    filtered.append(entry)
        return filtered
    
    def parse_har_file(self, file_content: bytes) -> Dict[str, Any]:
        """Parse HAR file content"""
        try:
            content_str = file_content.decode('utf-8')
            data = json.loads(content_str)
            
            if 'log' in data and 'entries' in data['log']:
                return {
                    'success': True,
                    'entries': data['log']['entries'],
                    'format': 'HAR'
                }
            else:
                return {
                    'success': False,
                    'error': 'Invalid HAR format',
                    'entries': []
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'entries': []
            }
    
    def get_highlighted_mismatched_keys(self, res1: str, res2: str) -> Dict[str, Any]:
        """Compare JSON responses and highlight differences"""
        # Handle None or empty responses
        if not res1:
            res1 = "{}"
        if not res2:
            res2 = "{}"

        try:
            obj1 = json.loads(res1)
            obj2 = json.loads(res2)

            mismatches = []
            added_keys = []
            removed_keys = []

            def compare_objects(o1, o2, prefix=''):
                # Find modified and removed keys
                if isinstance(o1, dict) and isinstance(o2, dict):
                    for key in o1.keys():
                        full_key = f"{prefix}.{key}" if prefix else key
                        if key not in o2:
                            removed_keys.append(full_key)
                        elif json.dumps(o1[key], sort_keys=True) != json.dumps(o2[key], sort_keys=True):
                            mismatches.append(full_key)
                            if isinstance(o1[key], dict) and isinstance(o2[key], dict):
                                compare_objects(o1[key], o2[key], full_key)

                    # Find added keys
                    for key in o2.keys():
                        full_key = f"{prefix}.{key}" if prefix else key
                        if key not in o1:
                            added_keys.append(full_key)

            compare_objects(obj1, obj2)

            return {
                'mismatch_keys': mismatches or [],
                'added_keys': added_keys or [],
                'removed_keys': removed_keys or [],
                'highlighted_response1': json.dumps(obj1, indent=2),
                'highlighted_response2': json.dumps(obj2, indent=2)
            }

        except Exception as e:
            # If JSON parsing fails, treat as text comparison
            text_match = res1.strip() == res2.strip()
            return {
                'mismatch_keys': [] if text_match else ['Response Content Differs'],
                'added_keys': [],
                'removed_keys': [],
                'highlighted_response1': str(res1),
                'highlighted_response2': str(res2)
            }
    
    def process_comparison(self, har1_data: Dict, har2_data: Dict) -> Dict[str, Any]:
        """Process complete HAR file comparison"""
        if not har1_data['success'] or not har2_data['success']:
            return {
                'success': False,
                'error': 'Failed to parse HAR files'
            }
        
        # Filter Paytm entries
        paytm_entries1 = self.filter_paytm_urls(har1_data['entries'])
        paytm_entries2 = self.filter_paytm_urls(har2_data['entries'])
        
        # Reset comparison results
        self.comparison_results = []
        
        total_requests = 0
        matched_responses = 0
        mismatched_responses = 0
        seen_urls = set()
        
        passed_results = []
        failed_results = []
        
        for entry1 in paytm_entries1:
            url = entry1.get('request', {}).get('url', '') or ''
            if not url or url in seen_urls:
                continue
            seen_urls.add(url)
            total_requests += 1

            # Find matching entry in second HAR
            matching_entry2 = None
            for entry2 in paytm_entries2:
                entry2_url = entry2.get('request', {}).get('url', '') or ''
                if entry2_url == url:
                    matching_entry2 = entry2
                    break

            # Get responses with safe extraction
            response1_text = entry1.get('response', {}).get('content', {}).get('text', '') or ''
            response1 = self.decode_response(response1_text)

            if matching_entry2:
                response2_text = matching_entry2.get('response', {}).get('content', {}).get('text', '') or ''
                response2 = self.decode_response(response2_text)
            else:
                response2 = "Not Found"
            
            # Compare responses
            comparison = self.get_highlighted_mismatched_keys(response1, response2)
            schema_status = "PASSED" if len(comparison['mismatch_keys']) == 0 else "FAILED"
            
            # Store comparison result
            result = {
                'url': url,
                'schema_status': schema_status,
                'mismatch_keys': comparison['mismatch_keys'],
                'added_keys': comparison['added_keys'],
                'removed_keys': comparison['removed_keys'],
                'response1': response1,
                'response2': response2,
                'highlighted_response1': comparison['highlighted_response1'],
                'highlighted_response2': comparison['highlighted_response2']
            }
            
            self.comparison_results.append(result)
            
            if len(comparison['mismatch_keys']) == 0:
                matched_responses += 1
                passed_results.append(result)
            else:
                mismatched_responses += 1
                failed_results.append(result)
        
        return {
            'success': True,
            'total_requests': total_requests,
            'matched_responses': matched_responses,
            'mismatched_responses': mismatched_responses,
            'passed_results': passed_results,
            'failed_results': failed_results,
            'comparison_results': self.comparison_results
        }
    
    def generate_ai_analysis(self) -> Dict[str, Any]:
        """Generate comprehensive AI-powered analysis with visualization data"""
        if not self.comparison_results:
            return {'error': 'No comparison data available'}

        failed_apis = [r for r in self.comparison_results if 'FAILED' in r['schema_status']]
        passed_apis = [r for r in self.comparison_results if 'PASSED' in r['schema_status']]
        total_mismatches = sum(len(api.get('mismatch_keys', []) or []) for api in failed_apis)

        total_requests = len(self.comparison_results)
        matched_responses = len(passed_apis)
        pass_rate = round((matched_responses / total_requests) * 100) if total_requests > 0 else 0

        # Analyze common patterns
        all_mismatches = []
        endpoint_mismatch_counts = {}
        error_type_distribution = {}

        for api in failed_apis:
            mismatch_keys = api.get('mismatch_keys', []) or []
            all_mismatches.extend(mismatch_keys)
            endpoint = api['url'].split('?')[0].split('/')[-1] or 'root'
            endpoint_mismatch_counts[endpoint] = endpoint_mismatch_counts.get(endpoint, 0) + len(mismatch_keys)

            # Categorize error types
            for key in mismatch_keys:
                if not key or not isinstance(key, str):
                    continue
                key_lower = key.lower()
                if 'status' in key_lower:
                    error_type_distribution['Status Changes'] = error_type_distribution.get('Status Changes', 0) + 1
                elif 'id' in key_lower:
                    error_type_distribution['ID Mismatches'] = error_type_distribution.get('ID Mismatches', 0) + 1
                elif 'amount' in key_lower or 'value' in key_lower:
                    error_type_distribution['Value Changes'] = error_type_distribution.get('Value Changes', 0) + 1
                elif 'date' in key_lower or 'time' in key_lower:
                    error_type_distribution['Timestamp Differences'] = error_type_distribution.get('Timestamp Differences', 0) + 1
                else:
                    error_type_distribution['Schema Changes'] = error_type_distribution.get('Schema Changes', 0) + 1

        key_frequency = {}
        for key in all_mismatches:
            if not key or not isinstance(key, str):
                continue
            base_key = key.split('.')[0]
            key_frequency[base_key] = key_frequency.get(base_key, 0) + 1

        top_issue_keys = sorted(key_frequency.items(), key=lambda x: x[1], reverse=True)[:5]

        # Performance analysis
        performance_data = []
        for result in self.comparison_results:
            url_parts = result['url'].split('/')
            endpoint = url_parts[-1] if url_parts else 'unknown'
            mismatch_keys = result.get('mismatch_keys', []) or []
            performance_data.append({
                'endpoint': endpoint,
                'status': 'Failed' if 'FAILED' in result['schema_status'] else 'Passed',
                'mismatch_count': len(mismatch_keys),
                'url': result['url']
            })

        return {
            'pass_rate': pass_rate,
            'failed_apis_count': len(failed_apis),
            'passed_apis_count': len(passed_apis),
            'total_mismatches': total_mismatches,
            'top_issue_keys': top_issue_keys,
            'endpoint_mismatch_counts': endpoint_mismatch_counts,
            'error_type_distribution': error_type_distribution,
            'performance_data': performance_data,
            'recommendations': [
                "Review the failed endpoints, particularly those with critical functionality",
                "Check for version differences between environments",
                "Verify if the differences are expected due to environment configurations",
                "Consider updating documentation if the changes are intentional",
                "Focus on endpoints with highest mismatch counts first"
            ],
            'top_apis_to_investigate': failed_apis[:5],
            'severity_assessment': self._assess_severity(pass_rate, total_mismatches, len(failed_apis))
        }

    def _assess_severity(self, pass_rate: int, total_mismatches: int, failed_count: int) -> Dict[str, str]:
        """Assess the severity of API differences"""
        if pass_rate >= 95:
            severity = "LOW"
            color = "success"
            message = "Excellent API compatibility with minimal differences"
        elif pass_rate >= 80:
            severity = "MEDIUM"
            color = "warning"
            message = "Good compatibility with some notable differences"
        elif pass_rate >= 60:
            severity = "HIGH"
            color = "error"
            message = "Significant compatibility issues requiring attention"
        else:
            severity = "CRITICAL"
            color = "error"
            message = "Critical compatibility problems - immediate action required"

        return {
            'level': severity,
            'color': color,
            'message': message,
            'impact': f"{failed_count} APIs affected with {total_mismatches} total differences"
        }
    
    def export_to_json(self) -> str:
        """Export comparison results to JSON"""
        return json.dumps(self.comparison_results, indent=2)
    
    def generate_report_data(self) -> Dict[str, Any]:
        """Generate report data for PDF/HTML export"""
        if not self.comparison_results:
            return {'error': 'No comparison data available'}
        
        total_requests = len(self.comparison_results)
        passed = len([r for r in self.comparison_results if 'PASSED' in r['schema_status']])
        failed = len([r for r in self.comparison_results if 'FAILED' in r['schema_status']])
        
        return {
            'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_requests': total_requests,
                'passed': passed,
                'failed': failed
            },
            'detailed_results': [
                {
                    'url': result['url'],
                    'status': result['schema_status'],
                    'discrepancies': ', '.join(result.get('mismatch_keys', []) or []) if result.get('mismatch_keys') else 'None'
                }
                for result in self.comparison_results
            ]
        }

def render_integrated_har_comparison_tab():
    """Render the complete integrated HAR comparison tab"""
    st.markdown("## Paytm Dashboard API Response Comparison")
    st.markdown("*Complete HAR file comparison with advanced analysis and filtering*")

    # Initialize comparison engine
    if 'har_comparison_engine' not in st.session_state:
        st.session_state['har_comparison_engine'] = IntegratedHARComparison()

    engine = st.session_state['har_comparison_engine']

    # File upload section
    st.markdown("### Upload HAR Files")
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Upload First HAR File:**")
        file1 = st.file_uploader(
            "Choose first HAR file",
            type=['har'],
            key="integrated_har_file1",
            help="Upload the first HAR file for comparison"
        )
        if file1:
            st.success(f"File loaded: {file1.name} ({file1.size:,} bytes)")

    with col2:
        st.markdown("**Upload Second HAR File:**")
        file2 = st.file_uploader(
            "Choose second HAR file",
            type=['har'],
            key="integrated_har_file2",
            help="Upload the second HAR file for comparison"
        )
        if file2:
            st.success(f"File loaded: {file2.name} ({file2.size:,} bytes)")

    # Compare button
    if file1 and file2:
        if st.button("Compare HAR Files", type="primary", use_container_width=True):
            with st.spinner("Processing files, please wait..."):
                # Parse HAR files
                har1_data = engine.parse_har_file(file1.read())
                har2_data = engine.parse_har_file(file2.read())

                # Process comparison
                comparison_result = engine.process_comparison(har1_data, har2_data)

                if comparison_result['success']:
                    st.session_state['comparison_result'] = comparison_result
                    st.success("Comparison completed successfully!")
                else:
                    st.error(f"Comparison failed: {comparison_result.get('error', 'Unknown error')}")

    # Display results if available
    if 'comparison_result' in st.session_state:
        render_comparison_results(st.session_state['comparison_result'], engine)

def render_comparison_results(result: Dict[str, Any], engine: IntegratedHARComparison):
    """Render comprehensive comparison results"""
    st.markdown("---")

    # Search and filter section
    st.markdown("### Search and Filter")
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        search_query = st.text_input(
            "Search by URL or text...",
            key="har_search_bar",
            placeholder="Enter search terms..."
        )

    with col2:
        filter_type = st.selectbox(
            "Filter Type",
            options=["All Requests", "Passed Only", "Failed Only"],
            key="har_filter_type"
        )

    with col3:
        sort_by = st.selectbox(
            "Sort By",
            options=["URL", "Status"],
            key="har_sort_by"
        )

    # Export and analysis buttons
    st.markdown("### Actions")
    action_col1, action_col2, action_col3 = st.columns(3)

    with action_col1:
        if st.button("Export as JSON", use_container_width=True):
            json_data = engine.export_to_json()
            st.download_button(
                label="Download JSON",
                data=json_data,
                file_name=f"comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

    with action_col2:
        if st.button("Generate Report", use_container_width=True):
            report_data = engine.generate_report_data()
            if 'error' not in report_data:
                st.session_state['report_data'] = report_data
                st.success("Report generated! Check the Export tab.")

    with action_col3:
        if st.button("AI Analysis", use_container_width=True):
            ai_analysis = engine.generate_ai_analysis()
            if 'error' not in ai_analysis:
                st.session_state['ai_analysis'] = ai_analysis
                st.success("AI analysis completed!")

    # Summary cards
    st.markdown("### Summary")
    summary_col1, summary_col2, summary_col3 = st.columns(3)

    with summary_col1:
        st.metric(
            "Total Requests",
            result['total_requests'],
            help="Total number of unique requests processed"
        )

    with summary_col2:
        st.metric(
            "Passed Responses",
            result['matched_responses'],
            delta=f"{(result['matched_responses']/result['total_requests']*100):.1f}%" if result['total_requests'] > 0 else "0%"
        )

    with summary_col3:
        st.metric(
            "Failed Responses",
            result['mismatched_responses'],
            delta=f"{(result['mismatched_responses']/result['total_requests']*100):.1f}%" if result['total_requests'] > 0 else "0%",
            delta_color="inverse"
        )

    # Results tabs
    results_tabs = st.tabs([
        "Passed Results",
        "Failed Results",
        "AI Analysis",
        "Report",
        "Visualizations"
    ])

    with results_tabs[0]:
        render_passed_results(result['passed_results'], search_query, filter_type)

    with results_tabs[1]:
        render_failed_results(result['failed_results'], search_query, filter_type)

    with results_tabs[2]:
        render_ai_analysis_results()

    with results_tabs[3]:
        render_report_section()

    with results_tabs[4]:
        render_visualizations(result)

def render_passed_results(passed_results: List[Dict], search_query: str, filter_type: str):
    """Render passed API results"""
    if filter_type == "Failed Only":
        st.info("No results to show (filtered to Failed Only)")
        return

    # Filter results based on search
    filtered_results = passed_results
    if search_query:
        filtered_results = [
            r for r in passed_results
            if search_query.lower() in r['url'].lower()
        ]

    if not filtered_results:
        st.info("No passed results found matching your criteria")
        return

    st.markdown(f"**Showing {len(filtered_results)} passed API(s)**")

    for i, result in enumerate(filtered_results):
        with st.expander(f"PASSED: {result['url'][:80]}{'...' if len(result['url']) > 80 else ''}", expanded=False):
            st.markdown(f"**URL:** `{result['url']}`")
            st.markdown(f"**Status:** {result['schema_status']}")

            # Show response comparison
            resp_col1, resp_col2 = st.columns(2)

            with resp_col1:
                st.markdown("**Response 1:**")
                st.code(result['highlighted_response1'][:1000] + ('...' if len(result['highlighted_response1']) > 1000 else ''), language='json')

            with resp_col2:
                st.markdown("**Response 2:**")
                st.code(result['highlighted_response2'][:1000] + ('...' if len(result['highlighted_response2']) > 1000 else ''), language='json')

def render_failed_results(failed_results: List[Dict], search_query: str, filter_type: str):
    """Render failed API results with detailed differences"""
    if filter_type == "Passed Only":
        st.info("No results to show (filtered to Passed Only)")
        return

    # Filter results based on search
    filtered_results = failed_results
    if search_query:
        filtered_results = [
            r for r in failed_results
            if search_query.lower() in r['url'].lower()
        ]

    if not filtered_results:
        st.info("No failed results found matching your criteria")
        return

    st.markdown(f"**Showing {len(filtered_results)} failed API(s)**")

    for i, result in enumerate(filtered_results):
        with st.expander(f"FAILED: {result['url'][:80]}{'...' if len(result['url']) > 80 else ''}", expanded=False):
            st.markdown(f"**URL:** `{result['url']}`")
            st.markdown(f"**Status:** {result['schema_status']}")

            # Show mismatched keys
            mismatch_keys = result.get('mismatch_keys', []) or []
            if mismatch_keys:
                st.markdown("**Mismatched Keys:**")
                for key in mismatch_keys:
                    st.markdown(f"- `{key}`")

            added_keys = result.get('added_keys', []) or []
            if added_keys:
                st.markdown("**Added Keys:**")
                for key in added_keys:
                    st.markdown(f"- `{key}`")

            removed_keys = result.get('removed_keys', []) or []
            if removed_keys:
                st.markdown("**Removed Keys:**")
                for key in removed_keys:
                    st.markdown(f"- `{key}`")

            # Show response comparison
            st.markdown("**Response Comparison:**")
            resp_col1, resp_col2 = st.columns(2)

            with resp_col1:
                st.markdown("**Response 1:**")
                st.code(result['highlighted_response1'][:1000] + ('...' if len(result['highlighted_response1']) > 1000 else ''), language='json')

            with resp_col2:
                st.markdown("**Response 2:**")
                st.code(result['highlighted_response2'][:1000] + ('...' if len(result['highlighted_response2']) > 1000 else ''), language='json')

def render_ai_analysis_results():
    """Render enhanced AI analysis results with comprehensive visualizations"""
    if 'ai_analysis' not in st.session_state:
        st.info("Run AI Analysis to see intelligent insights about your comparison results")
        return

    analysis = st.session_state['ai_analysis']

    if not analysis or not isinstance(analysis, dict):
        st.info("Run AI Analysis to see intelligent insights about your comparison results")
        return

    if 'error' in analysis:
        st.error(f"AI Analysis Error: {analysis['error']}")
        return

    # Severity Assessment Banner
    severity = analysis.get('severity_assessment', {})
    if severity:
        if severity['color'] == 'success':
            st.success(f"**{severity['level']} SEVERITY**: {severity['message']}")
        elif severity['color'] == 'warning':
            st.warning(f"**{severity['level']} SEVERITY**: {severity['message']}")
        else:
            st.error(f"**{severity['level']} SEVERITY**: {severity['message']}")
        st.caption(severity['impact'])

    # Analysis summary with enhanced metrics
    st.markdown("### AI Analysis Summary")

    # Enhanced metrics display
    metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

    with metric_col1:
        st.metric(
            "Compatibility Score",
            f"{analysis['pass_rate']}%",
            delta=f"{analysis['pass_rate'] - 85}%" if analysis['pass_rate'] < 85 else None,
            delta_color="normal" if analysis['pass_rate'] >= 85 else "inverse"
        )

    with metric_col2:
        st.metric("Passed APIs", analysis['passed_apis_count'])

    with metric_col3:
        st.metric("Failed APIs", analysis['failed_apis_count'])

    with metric_col4:
        st.metric("Total Mismatches", analysis['total_mismatches'])

    # Visualization tabs for AI analysis
    viz_tabs = st.tabs([
        "Error Distribution",
        "Endpoint Analysis",
        "Mismatch Patterns",
        "Performance Impact",
        "Recommendations"
    ])

    with viz_tabs[0]:
        render_error_distribution_chart(analysis)

    with viz_tabs[1]:
        render_endpoint_analysis_chart(analysis)

    with viz_tabs[2]:
        render_mismatch_patterns_chart(analysis)

    with viz_tabs[3]:
        render_performance_impact_chart(analysis)

    with viz_tabs[4]:
        render_enhanced_recommendations(analysis)

def render_error_distribution_chart(analysis):
    """Render error type distribution pie chart"""
    st.markdown("#### Error Type Distribution")

    error_dist = analysis.get('error_type_distribution', {})
    if not error_dist:
        st.info("No error distribution data available")
        return

    # Create pie chart for error types
    fig_pie = go.Figure(data=[go.Pie(
        labels=list(error_dist.keys()),
        values=list(error_dist.values()),
        hole=0.4,
        marker_colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    )])

    fig_pie.update_layout(
        title="Distribution of Error Types",
        showlegend=True,
        height=400,
        font=dict(size=12)
    )

    st.plotly_chart(fig_pie, use_container_width=True)

    # Error type summary table
    if error_dist:
        st.markdown("#### Error Type Summary")
        error_df = pd.DataFrame([
            {'Error Type': k, 'Count': v, 'Percentage': f"{(v/sum(error_dist.values())*100):.1f}%"}
            for k, v in error_dist.items()
        ])
        st.dataframe(error_df, use_container_width=True)

def render_endpoint_analysis_chart(analysis):
    """Render endpoint mismatch analysis"""
    st.markdown("#### Endpoint Mismatch Analysis")

    endpoint_data = analysis.get('endpoint_mismatch_counts', {})
    if not endpoint_data:
        st.info("No endpoint analysis data available")
        return

    # Create bar chart for endpoint mismatches
    endpoints = list(endpoint_data.keys())
    counts = list(endpoint_data.values())

    fig_bar = go.Figure(data=[
        go.Bar(
            x=endpoints,
            y=counts,
            marker_color=['#FF6B6B' if count > 5 else '#4ECDC4' if count > 2 else '#96CEB4' for count in counts],
            text=counts,
            textposition='auto'
        )
    ])

    fig_bar.update_layout(
        title="Mismatch Count by API Endpoint",
        xaxis_title="API Endpoint",
        yaxis_title="Number of Mismatches",
        height=400,
        xaxis_tickangle=-45
    )

    st.plotly_chart(fig_bar, use_container_width=True)

    # Top problematic endpoints
    if endpoint_data:
        st.markdown("#### Most Problematic Endpoints")
        sorted_endpoints = sorted(endpoint_data.items(), key=lambda x: x[1], reverse=True)[:5]
        for i, (endpoint, count) in enumerate(sorted_endpoints, 1):
            severity = "Critical" if count > 10 else "Medium" if count > 5 else "Low"
            st.markdown(f"{i}. **{endpoint}** - {count} mismatches ({severity})")

def render_mismatch_patterns_chart(analysis):
    """Render mismatch patterns analysis"""
    st.markdown("#### Common Mismatch Patterns")

    top_keys = analysis.get('top_issue_keys', [])
    if not top_keys:
        st.info("No mismatch pattern data available")
        return

    # Create horizontal bar chart for top issue keys
    keys = [item[0] for item in top_keys]
    counts = [item[1] for item in top_keys]

    fig_hbar = go.Figure(data=[
        go.Bar(
            y=keys,
            x=counts,
            orientation='h',
            marker_color='#45B7D1',
            text=counts,
            textposition='auto'
        )
    ])

    fig_hbar.update_layout(
        title="Most Frequently Mismatched Keys",
        xaxis_title="Occurrence Count",
        yaxis_title="JSON Keys",
        height=400
    )

    st.plotly_chart(fig_hbar, use_container_width=True)

    # Pattern analysis insights
    st.markdown("#### Pattern Analysis Insights")
    if top_keys:
        total_patterns = sum(count for _, count in top_keys)
        st.markdown(f"**Total Pattern Occurrences:** {total_patterns}")

        for key, count in top_keys:
            percentage = (count / total_patterns) * 100
            st.markdown(f"- **{key}**: {count} occurrences ({percentage:.1f}% of all patterns)")

def render_performance_impact_chart(analysis):
    """Render performance impact visualization"""
    st.markdown("#### Performance Impact Analysis")

    perf_data = analysis.get('performance_data', [])
    if not perf_data:
        st.info("No performance data available")
        return

    # Create performance impact heatmap
    df = pd.DataFrame(perf_data)

    if not df.empty:
        # Aggregate data for heatmap
        heatmap_data = df.groupby(['endpoint', 'status']).agg({
            'mismatch_count': 'sum'
        }).reset_index()

        # Pivot for heatmap
        pivot_data = heatmap_data.pivot(index='endpoint', columns='status', values='mismatch_count').fillna(0)

        if not pivot_data.empty:
            fig_heatmap = go.Figure(data=go.Heatmap(
                z=pivot_data.values,
                x=pivot_data.columns,
                y=pivot_data.index,
                colorscale='RdYlBu_r',
                text=pivot_data.values,
                texttemplate="%{text}",
                textfont={"size": 10}
            ))

            fig_heatmap.update_layout(
                title="API Endpoint Performance Heatmap",
                xaxis_title="Status",
                yaxis_title="Endpoint",
                height=400
            )

            st.plotly_chart(fig_heatmap, use_container_width=True)

        # Performance summary
        st.markdown("#### Performance Summary")
        failed_endpoints = df[df['status'] == 'Failed']['endpoint'].nunique()
        total_endpoints = df['endpoint'].nunique()

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Endpoints", total_endpoints)
        with col2:
            st.metric("Failed Endpoints", failed_endpoints)
        with col3:
            impact_rate = (failed_endpoints / total_endpoints * 100) if total_endpoints > 0 else 0
            st.metric("Impact Rate", f"{impact_rate:.1f}%")

def render_enhanced_recommendations(analysis):
    """Render enhanced recommendations with priority levels"""
    st.markdown("#### AI-Generated Recommendations")

    recommendations = analysis.get('recommendations', [])
    top_apis = analysis.get('top_apis_to_investigate', [])

    # Priority recommendations
    st.markdown("##### High Priority Actions")
    for i, rec in enumerate(recommendations[:3], 1):
        st.markdown(f"**{i}.** {rec}")

    # Additional recommendations
    if len(recommendations) > 3:
        st.markdown("##### Additional Recommendations")
        for i, rec in enumerate(recommendations[3:], 4):
            st.markdown(f"**{i}.** {rec}")

    # Critical APIs to investigate
    if top_apis:
        st.markdown("##### Critical APIs Requiring Investigation")

        api_df = pd.DataFrame([
            {
                'API Endpoint': api['url'].split('?')[0],
                'Mismatch Count': len(api.get('mismatch_keys', []) or []),
                'Status': api['schema_status'],
                'Key Issues': ', '.join((api.get('mismatch_keys', []) or [])[:3]) + ('...' if len(api.get('mismatch_keys', []) or []) > 3 else '')
            }
            for api in top_apis
        ])

        st.dataframe(api_df, use_container_width=True)

    # Action plan
    st.markdown("##### Suggested Action Plan")
    pass_rate = analysis.get('pass_rate', 0)

    if pass_rate >= 90:
        st.success("**Low Priority**: Monitor the few failing endpoints and investigate during regular maintenance windows.")
    elif pass_rate >= 75:
        st.warning("**Medium Priority**: Schedule investigation of failing endpoints within the next sprint cycle.")
    else:
        st.error("**High Priority**: Immediate investigation required. Consider halting deployment until critical issues are resolved.")

def render_report_section():
    """Render report generation and preview"""
    if 'report_data' not in st.session_state:
        st.info("Generate a report to see detailed analysis")
        return

    report = st.session_state['report_data']

    if 'error' in report:
        st.error(f"Report Error: {report['error']}")
        return

    st.markdown("### Automated Test Report")
    st.markdown(f"**Generated:** {report['date']}")

    # Summary table
    st.markdown("#### Summary")
    summary_df = pd.DataFrame([report['summary']])
    st.dataframe(summary_df, use_container_width=True)

    # Detailed results
    st.markdown("#### Detailed Results")
    details_df = pd.DataFrame(report['detailed_results'])
    st.dataframe(details_df, use_container_width=True)

    # Export options
    st.markdown("#### Export Options")
    col1, col2 = st.columns(2)

    with col1:
        # CSV export
        csv_data = details_df.to_csv(index=False)
        st.download_button(
            label="Download CSV Report",
            data=csv_data,
            file_name=f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    with col2:
        # JSON export
        json_data = json.dumps(report, indent=2)
        st.download_button(
            label="Download JSON Report",
            data=json_data,
            file_name=f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )

def render_visualizations(result: Dict[str, Any]):
    """Render comparison visualizations"""
    st.markdown("### Comparison Visualizations")

    # Create visualization tabs
    viz_tabs = st.tabs([
        "Overview",
        "Detailed Analysis",
        "Trends",
        "Performance Metrics"
    ])

    with viz_tabs[0]:
        render_overview_charts(result)

    with viz_tabs[1]:
        render_detailed_analysis_charts(result)

    with viz_tabs[2]:
        render_trend_analysis(result)

    with viz_tabs[3]:
        render_performance_metrics(result)

def render_overview_charts(result: Dict[str, Any]):
    """Render overview charts"""
    col1, col2 = st.columns(2)

    with col1:
        # Summary pie chart
        fig_pie = go.Figure(data=[go.Pie(
            labels=['Passed', 'Failed'],
            values=[result['matched_responses'], result['mismatched_responses']],
            hole=0.4,
            marker_colors=['#28a745', '#dc3545']
        )])

        fig_pie.update_layout(
            title="API Response Comparison Results",
            showlegend=True,
            height=400
        )

        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        # Success rate gauge
        success_rate = (result['matched_responses'] / result['total_requests'] * 100) if result['total_requests'] > 0 else 0

        fig_gauge = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = success_rate,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Success Rate (%)"},
            delta = {'reference': 85},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 85], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))

        fig_gauge.update_layout(height=400)
        st.plotly_chart(fig_gauge, use_container_width=True)

def render_detailed_analysis_charts(result: Dict[str, Any]):
    """Render detailed analysis charts"""
    if not result['comparison_results']:
        st.info("No detailed data available")
        return

    # Prepare data
    status_data = []
    for res in result['comparison_results']:
        mismatch_keys = res.get('mismatch_keys', []) or []
        status_data.append({
            'URL': res['url'][:50] + '...' if len(res['url']) > 50 else res['url'],
            'Status': 'Passed' if 'PASSED' in res['schema_status'] else 'Failed',
            'Mismatch Count': len(mismatch_keys),
            'Endpoint': res['url'].split('/')[-1].split('?')[0] or 'root'
        })

    status_df = pd.DataFrame(status_data)

    # Bar chart of mismatch counts
    fig_bar = px.bar(
        status_df,
        x='URL',
        y='Mismatch Count',
        color='Status',
        title='Mismatch Count by API Endpoint',
        color_discrete_map={'Passed': '#28a745', 'Failed': '#dc3545'}
    )

    fig_bar.update_layout(
        xaxis_tickangle=-45,
        height=500
    )

    st.plotly_chart(fig_bar, use_container_width=True)

    # Endpoint grouping analysis
    endpoint_summary = status_df.groupby('Endpoint').agg({
        'Mismatch Count': 'sum',
        'Status': lambda x: (x == 'Failed').sum()
    }).rename(columns={'Status': 'Failed Count'})

    if not endpoint_summary.empty:
        st.markdown("#### Endpoint Summary")
        st.dataframe(endpoint_summary, use_container_width=True)

def render_trend_analysis(result: Dict[str, Any]):
    """Render trend analysis"""
    st.markdown("#### API Endpoint Trends")

    if not result['comparison_results']:
        st.info("No trend data available")
        return

    # Create trend data
    trend_data = []
    for i, res in enumerate(result['comparison_results']):
        mismatch_keys = res.get('mismatch_keys', []) or []
        trend_data.append({
            'Sequence': i + 1,
            'Endpoint': res['url'].split('/')[-1].split('?')[0] or 'root',
            'Mismatch Count': len(mismatch_keys),
            'Status': 'Failed' if 'FAILED' in res['schema_status'] else 'Passed'
        })

    trend_df = pd.DataFrame(trend_data)

    # Line chart showing mismatch trends
    fig_line = px.line(
        trend_df,
        x='Sequence',
        y='Mismatch Count',
        color='Status',
        title='Mismatch Trend Across API Calls',
        markers=True
    )

    fig_line.update_layout(height=400)
    st.plotly_chart(fig_line, use_container_width=True)

def render_performance_metrics(result: Dict[str, Any]):
    """Render performance metrics"""
    st.markdown("#### Performance Metrics")

    if not result['comparison_results']:
        st.info("No performance data available")
        return

    # Calculate metrics
    total_apis = len(result['comparison_results'])
    failed_apis = result['mismatched_responses']
    passed_apis = result['matched_responses']

    # Metrics display
    metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)

    with metric_col1:
        st.metric("Total APIs", total_apis)

    with metric_col2:
        st.metric("Passed APIs", passed_apis)

    with metric_col3:
        st.metric("Failed APIs", failed_apis)

    with metric_col4:
        reliability = (passed_apis / total_apis * 100) if total_apis > 0 else 0
        st.metric("Reliability", f"{reliability:.1f}%")

    # Performance distribution
    mismatch_counts = [len(res.get('mismatch_keys', []) or []) for res in result['comparison_results']]

    fig_hist = px.histogram(
        x=mismatch_counts,
        nbins=10,
        title='Distribution of Mismatch Counts',
        labels={'x': 'Number of Mismatches', 'y': 'Frequency'}
    )

    fig_hist.update_layout(height=400)
    st.plotly_chart(fig_hist, use_container_width=True)
