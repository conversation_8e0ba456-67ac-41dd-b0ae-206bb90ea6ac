#!/usr/bin/env python3
"""
Test the simplified emulator testing interface
"""

import sys
import os
sys.path.append(os.getcwd())

from emulator_testing_tab import render_emulator_testing_tab
from emulator_manager import Emulator<PERSON>anager
from appium_driver_enhanced import AppiumDriverEnhanced

def test_simplified_interface():
    print("🧪 Testing Simplified Emulator Interface")
    print("=" * 50)
    
    # Test component initialization
    print("1️⃣ Testing component initialization...")
    try:
        emulator_manager = EmulatorManager()
        appium_driver = AppiumDriverEnhanced()
        print("✅ Components initialized successfully")
    except Exception as e:
        print(f"❌ Component initialization failed: {str(e)}")
        return False
    
    # Test emulator discovery
    print("\n2️⃣ Testing emulator discovery...")
    try:
        emulators = emulator_manager.discover_emulators()
        print(f"✅ Found {len(emulators)} emulator(s)")
        
        running_emulators = [e for e in emulators if e.running]
        print(f"✅ {len(running_emulators)} running emulator(s)")
        
        if running_emulators:
            for emulator in running_emulators:
                print(f"   • {emulator.name} ({emulator.adb_id})")
        
    except Exception as e:
        print(f"❌ Emulator discovery failed: {str(e)}")
        return False
    
    # Test Appium server check
    print("\n3️⃣ Testing Appium server check...")
    try:
        server_running = appium_driver.check_appium_server()
        print(f"✅ Appium server: {'Running' if server_running else 'Not running'}")
    except Exception as e:
        print(f"❌ Appium server check failed: {str(e)}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ Simplified interface components are working!")
    print("\n🌐 Dashboard should now show:")
    print("   • Clean, simple interface")
    print("   • Prerequisites check")
    print("   • Emulator selection dropdown")
    print("   • Core Appium operations dropdown")
    print("   • No complex tabs or advanced features")
    
    return True

if __name__ == "__main__":
    success = test_simplified_interface()
    
    if success:
        print("\n🎉 SIMPLIFIED INTERFACE READY!")
        print("🌐 Open: http://localhost:8501")
        print("📱 Navigate to: Emulator Testing")
        print("🎯 Use the dropdown menus for core Appium operations")
    else:
        print("\n❌ Interface testing failed")
        print("💡 Check component dependencies")
