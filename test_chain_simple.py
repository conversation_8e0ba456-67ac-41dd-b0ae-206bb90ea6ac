
import streamlit as st
from test_chain_system import <PERSON><PERSON>hainExecutor, ChainStepType
from appium_driver_enhanced import AppiumDriverEnhanced

st.title("🔗 Chain Testing Verification")

try:
    # Initialize components
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    st.success("✅ Components initialized successfully")
    
    # Create test chain
    if st.button("Create Test Chain"):
        chain = executor.create_chain("Test Chain", "Simple test")
        executor.add_step(ChainStepType.WAIT, "Wait 1 second", {"duration": 1})
        
        st.success(f"✅ Created chain: {chain.name}")
        st.write(f"Steps: {len(chain.steps)}")
        
        # Generate code
        code = executor._generate_appium_code(chain)
        st.code(code[:500] + "..." if len(code) > 500 else code, language="python")
        
except Exception as e:
    st.error(f"❌ Error: {str(e)}")
    st.code(str(e))
