#!/usr/bin/env python3
"""
Test script to verify dashboard emulator connection functionality
"""

from emulator_manager import EmulatorManager
from appium_driver_enhanced import AppiumDriverEnhanced

def test_dashboard_workflow():
    print("🚀 Testing Dashboard Emulator Connection Workflow")
    print("=" * 60)
    
    # Step 1: Initialize components
    print("1️⃣ Initializing components...")
    emulator_manager = EmulatorManager()
    appium_driver = AppiumDriverEnhanced()
    
    # Step 2: Discover emulators (like clicking "Discover Emulators")
    print("\n2️⃣ Discovering emulators...")
    emulators = emulator_manager.discover_emulators()
    
    if not emulators:
        print("❌ No emulators found!")
        return False
    
    print(f"✅ Found {len(emulators)} emulator(s)")
    
    # Step 3: Show emulator status (like dashboard display)
    print("\n3️⃣ Emulator Status:")
    running_emulators = []
    
    for emulator in emulators:
        status = "🟢 Running" if emulator.running else "🔴 Stopped"
        print(f"   📱 {emulator.name}")
        print(f"      Status: {status}")
        print(f"      Device: {emulator.device_name}")
        print(f"      API Level: {emulator.api_level}")
        print(f"      Resolution: {emulator.resolution}")
        
        if emulator.running:
            print(f"      ADB ID: {emulator.adb_id}")
            print(f"      Port: {emulator.port}")
            running_emulators.append(emulator)
        print()
    
    if not running_emulators:
        print("❌ No running emulators found!")
        return False
    
    # Step 4: Test connection to first running emulator
    print("4️⃣ Testing connection to first running emulator...")
    target_emulator = running_emulators[0]
    print(f"   Connecting to: {target_emulator.name} ({target_emulator.adb_id})")
    
    # Step 5: Connect via Appium (like clicking "Connect" button)
    print("\n5️⃣ Connecting via Appium...")
    success, diagnostics = appium_driver.connect_to_device(target_emulator.adb_id)
    
    if success:
        print("✅ Connection successful!")
        
        # Step 6: Test functionality (like dashboard functionality tests)
        print("\n6️⃣ Testing functionality...")
        tests = appium_driver.test_basic_functionality()
        
        all_passed = True
        for test_name, result in tests.items():
            status = "✅" if result else "❌"
            print(f"   {status} {test_name.replace('_', ' ').title()}")
            if not result:
                all_passed = False
        
        # Step 7: Take screenshot (like dashboard screenshot feature)
        print("\n7️⃣ Taking screenshot...")
        screenshot = appium_driver.take_screenshot()
        if screenshot:
            print("✅ Screenshot captured successfully")
            print(f"   Screenshot size: {len(screenshot)} characters (base64)")
        else:
            print("❌ Screenshot failed")
            all_passed = False
        
        # Step 8: Disconnect
        print("\n8️⃣ Disconnecting...")
        appium_driver.disconnect()
        print("✅ Disconnected successfully")
        
        # Final result
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL TESTS PASSED! Dashboard connection workflow is working!")
            print("\n✅ Your dashboard should now show:")
            print("   • Emulators as '🟢 Running'")
            print("   • Successful connection when clicking 'Connect'")
            print("   • Working screenshot and functionality tests")
            print("   • Proper device information display")
            return True
        else:
            print("⚠️ Some tests failed, but basic connection is working")
            return True
    
    else:
        print("❌ Connection failed!")
        print(f"   Error: {diagnostics.error_message}")
        
        if diagnostics.suggestions:
            print("   💡 Suggestions:")
            for suggestion in diagnostics.suggestions:
                print(f"   • {suggestion}")
        
        return False

def main():
    success = test_dashboard_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🌟 DASHBOARD READY!")
        print("🌐 Open: http://localhost:8501")
        print("📱 Navigate to: Emulator Testing → Emulator Connection")
        print("🔄 Click 'Refresh List' to see your running emulators")
        print("🔗 Click 'Connect' to connect to your emulator")
    else:
        print("❌ Dashboard connection needs troubleshooting")
        print("💡 Check Appium server and emulator status")

if __name__ == "__main__":
    main()
