#!/usr/bin/env python3
"""
Test dashboard connection functionality
"""

from appium_driver_enhanced import AppiumDriverEnhanced
from test_chain_system import Test<PERSON>hainExecutor, ChainStepType

def test_dashboard_connection():
    """Test the dashboard connection process"""
    print("🔍 Testing Dashboard Connection Process")
    print("=" * 40)
    
    try:
        # Initialize driver (same as dashboard does)
        print("1️⃣ Initializing AppiumDriverEnhanced...")
        driver = AppiumDriverEnhanced()
        print("✅ Driver initialized")
        
        # Test connection (same as dashboard does)
        print("2️⃣ Connecting to emulator-5554...")
        success, diagnostics = driver.connect_to_device("emulator-5554")
        
        if success:
            print("✅ Connection successful!")
            print(f"   Device ID: {diagnostics.device_id}")
            print(f"   Connection time: {diagnostics.connection_time:.2f}s")
            
            # Test connection status
            print("3️⃣ Testing connection status...")
            status = driver.get_connection_status()
            print(f"   Connected: {status['connected']}")
            print(f"   Device ID: {status['device_id']}")
            print(f"   Appium server: {status['appium_server_running']}")
            
            # Test basic functionality
            print("4️⃣ Testing basic functionality...")
            tests = driver.test_basic_functionality()
            for test_name, result in tests.items():
                status_icon = "✅" if result else "❌"
                print(f"   {status_icon} {test_name}")
            
            # Test screenshot
            print("5️⃣ Testing screenshot...")
            screenshot = driver.take_screenshot()
            if screenshot:
                print(f"✅ Screenshot captured: {len(screenshot)} chars")
            else:
                print("❌ Screenshot failed")
            
            # Test app launch
            print("6️⃣ Testing app launch...")
            try:
                driver.launch_app("com.android.settings")
                print("✅ Settings app launched")
                
                # Wait and take screenshot
                import time
                time.sleep(2)
                app_screenshot = driver.take_screenshot()
                if app_screenshot:
                    print(f"✅ App screenshot: {len(app_screenshot)} chars")
                
                # Go back to home
                driver.press_home()
                print("✅ Returned to home")
                
            except Exception as e:
                print(f"❌ App launch failed: {e}")
            
            # Disconnect
            driver.disconnect()
            print("✅ Disconnected successfully")
            
            return True
            
        else:
            print(f"❌ Connection failed: {diagnostics.error_message}")
            print("Suggestions:")
            for suggestion in diagnostics.suggestions:
                print(f"   • {suggestion}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return False

def test_chain_execution_with_dashboard_driver():
    """Test chain execution using dashboard driver"""
    print("\n🔗 Testing Chain Execution with Dashboard Driver")
    print("=" * 50)
    
    try:
        # Initialize components (same as dashboard)
        print("1️⃣ Initializing components...")
        driver = AppiumDriverEnhanced()
        executor = TestChainExecutor(driver)
        
        # Connect
        print("2️⃣ Connecting...")
        success, diagnostics = driver.connect_to_device("emulator-5554")
        
        if not success:
            print(f"❌ Connection failed: {diagnostics.error_message}")
            return False
        
        print("✅ Connected")
        
        # Create simple chain
        print("3️⃣ Creating test chain...")
        chain = executor.create_chain("Dashboard Test", "Test chain execution from dashboard")
        
        # Add simple steps
        executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Initial screenshot", {})
        executor.add_step(ChainStepType.WAIT, "Wait 2 seconds", {"duration": 2})
        executor.add_step(ChainStepType.LAUNCH_APP, "Launch Settings", {"package_name": "com.android.settings"})
        executor.add_step(ChainStepType.WAIT, "Wait for app", {"duration": 3})
        executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Settings screenshot", {})
        executor.add_step(ChainStepType.PRESS_BUTTON, "Press home", {"button": "home"})
        executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Final screenshot", {})
        
        print(f"✅ Chain created with {len(chain.steps)} steps")
        
        # Execute chain
        print("4️⃣ Executing chain...")
        results = executor.execute_chain(chain)
        
        print(f"Overall success: {'✅' if results['overall_success'] else '❌'}")
        print(f"Execution time: {results['execution_time']:.2f}s")
        
        # Show step results
        for i, step_result in enumerate(results['step_results'], 1):
            status = "✅" if step_result['status'] == 'success' else "❌"
            print(f"   {status} Step {i}: {step_result['description']}")
            if step_result['status'] == 'failed' and step_result.get('error'):
                print(f"      Error: {step_result['error']}")
        
        # Test code generation
        if results.get('generated_code'):
            print(f"✅ Code generated: {len(results['generated_code'])} chars")
        
        # Disconnect
        driver.disconnect()
        print("✅ Test completed")
        
        return results['overall_success']
        
    except Exception as e:
        print(f"❌ Chain execution test failed: {e}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return False

def main():
    """Run dashboard connection tests"""
    print("🧪 DASHBOARD CONNECTION TESTING")
    print("=" * 35)
    
    # Test 1: Basic connection
    connection_success = test_dashboard_connection()
    
    # Test 2: Chain execution
    chain_success = test_chain_execution_with_dashboard_driver()
    
    # Summary
    print("\n" + "=" * 35)
    print("📊 TEST RESULTS")
    print("=" * 35)
    
    print(f"Dashboard Connection: {'✅ PASSED' if connection_success else '❌ FAILED'}")
    print(f"Chain Execution: {'✅ PASSED' if chain_success else '❌ FAILED'}")
    
    if connection_success and chain_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Dashboard connection is working")
        print("✅ Chain execution is working")
        print("✅ App functionality is working")
        
        print("\n🚀 DASHBOARD SHOULD WORK NOW:")
        print("1. Go to: http://localhost:8501")
        print("2. Tab: 📱 Emulator Testing")
        print("3. Connect to device")
        print("4. Switch to Chain Testing")
        print("5. Build and execute chains!")
        
    else:
        print("\n⚠️ ISSUES FOUND:")
        if not connection_success:
            print("• Dashboard connection has issues")
        if not chain_success:
            print("• Chain execution has issues")
        print("Check error messages above")

if __name__ == "__main__":
    main()
