#!/usr/bin/env python3
"""
Comprehensive test of the Chain-like Command System
Tests all components and integration with the dashboard
"""

import sys
import time
from test_chain_system import <PERSON><PERSON>hainExecutor, ChainStepType, TestChain
from appium_driver_enhanced import AppiumDriverEnhanced

def test_chain_system_components():
    """Test all chain system components"""
    print("🔗 Testing Chain-like Command System")
    print("=" * 50)
    
    # Test 1: Component imports
    print("1️⃣ Testing component imports...")
    try:
        from test_chain_system import TestChainExecutor, ChainStepType, ChainStepStatus, TestChain, ChainStep
        from chain_testing_interface import render_chain_testing_interface
        print("✅ All components imported successfully")
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        return False
    
    # Test 2: Initialize components
    print("\n2️⃣ Testing component initialization...")
    try:
        appium_driver = AppiumDriverEnhanced()
        executor = TestChainExecutor(appium_driver)
        print("✅ Components initialized successfully")
    except Exception as e:
        print(f"❌ Initialization failed: {str(e)}")
        return False
    
    # Test 3: Create test chain
    print("\n3️⃣ Testing chain creation...")
    try:
        chain = executor.create_chain(
            name="Test Chain",
            description="Comprehensive test chain for validation",
            app_package="com.android.settings"
        )
        print(f"✅ Chain created: {chain.name}")
    except Exception as e:
        print(f"❌ Chain creation failed: {str(e)}")
        return False
    
    # Test 4: Add steps to chain
    print("\n4️⃣ Testing step addition...")
    try:
        # Add various step types
        steps_to_add = [
            (ChainStepType.LAUNCH_APP, "Launch Settings app", {"package_name": "com.android.settings"}),
            (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
            (ChainStepType.TAKE_SCREENSHOT, "Take initial screenshot", {}),
            (ChainStepType.VERIFY_TEXT, "Verify Settings header", {"text": "Settings", "case_sensitive": False}),
            (ChainStepType.PRESS_BUTTON, "Press home button", {"button": "home"}),
            (ChainStepType.TAKE_SCREENSHOT, "Take final screenshot", {})
        ]
        
        for step_type, description, parameters in steps_to_add:
            executor.add_step(step_type, description, parameters)
        
        print(f"✅ Added {len(steps_to_add)} steps to chain")
        print(f"   Total steps in chain: {len(chain.steps)}")
    except Exception as e:
        print(f"❌ Step addition failed: {str(e)}")
        return False
    
    # Test 5: Validate chain
    print("\n5️⃣ Testing chain validation...")
    try:
        from chain_testing_interface import validate_chain
        validation_results = validate_chain(chain)
        
        if validation_results["valid"]:
            print("✅ Chain validation passed")
        else:
            print("⚠️ Chain validation found issues:")
            for error in validation_results["errors"]:
                print(f"   • {error}")
    except Exception as e:
        print(f"❌ Chain validation failed: {str(e)}")
        return False
    
    # Test 6: Code generation
    print("\n6️⃣ Testing code generation...")
    try:
        generated_code = executor._generate_appium_code(chain)
        
        if generated_code and len(generated_code) > 100:
            print("✅ Appium code generated successfully")
            print(f"   Generated code length: {len(generated_code)} characters")
            print(f"   Lines of code: {len(generated_code.split('\\n'))}")
        else:
            print("❌ Generated code is too short or empty")
            return False
    except Exception as e:
        print(f"❌ Code generation failed: {str(e)}")
        return False
    
    # Test 7: Export/Import functionality
    print("\n7️⃣ Testing export/import functionality...")
    try:
        # Export chain
        exported_data = executor.export_chain(chain)
        
        if exported_data and "name" in exported_data:
            print("✅ Chain export successful")
        
        # Import chain
        new_executor = TestChainExecutor(appium_driver)
        imported_chain = new_executor.import_chain(exported_data)
        
        if imported_chain.name == chain.name and len(imported_chain.steps) == len(chain.steps):
            print("✅ Chain import successful")
        else:
            print("❌ Chain import validation failed")
            return False
    except Exception as e:
        print(f"❌ Export/Import failed: {str(e)}")
        return False
    
    return True

def test_chain_execution_dry_run():
    """Test chain execution in dry run mode"""
    print("\n🔍 Testing Chain Execution (Dry Run)")
    print("=" * 40)
    
    try:
        appium_driver = AppiumDriverEnhanced()
        executor = TestChainExecutor(appium_driver)
        
        # Create a simple test chain
        chain = executor.create_chain(
            name="Dry Run Test",
            description="Test chain for dry run validation"
        )
        
        # Add steps that don't require device connection
        executor.add_step(ChainStepType.WAIT, "Wait 1 second", {"duration": 1})
        executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Take screenshot", {})
        
        print(f"✅ Created test chain with {len(chain.steps)} steps")
        
        # Validate without execution
        from chain_testing_interface import validate_chain
        validation_results = validate_chain(chain)
        
        if validation_results["valid"]:
            print("✅ Dry run validation passed")
            return True
        else:
            print("❌ Dry run validation failed")
            for error in validation_results["errors"]:
                print(f"   • {error}")
            return False
    
    except Exception as e:
        print(f"❌ Dry run test failed: {str(e)}")
        return False

def test_dashboard_integration():
    """Test integration with dashboard interface"""
    print("\n🌐 Testing Dashboard Integration")
    print("=" * 35)
    
    try:
        # Test interface imports
        from chain_testing_interface import (
            render_chain_testing_interface,
            render_chain_builder,
            render_chain_executor,
            render_chain_results,
            render_saved_chains,
            render_generated_code
        )
        print("✅ Dashboard interface components imported")
        
        # Test template functions
        from chain_testing_interface import add_app_launch_template, add_login_flow_template
        print("✅ Template functions imported")
        
        # Test integration with emulator testing tab
        from emulator_testing_tab import render_chain_testing_mode
        print("✅ Emulator testing tab integration available")
        
        return True
    
    except Exception as e:
        print(f"❌ Dashboard integration test failed: {str(e)}")
        return False

def test_appium_driver_enhancements():
    """Test enhanced Appium driver methods"""
    print("\n🚀 Testing Appium Driver Enhancements")
    print("=" * 40)
    
    try:
        driver = AppiumDriverEnhanced()
        
        # Test new methods exist
        methods_to_test = [
            'swipe', 'scroll_up', 'scroll_down', 'find_element_by_text',
            'wait_for_element', 'get_current_activity', 'get_current_package',
            'launch_app', 'close_app', 'press_menu', 'type_text'
        ]
        
        for method_name in methods_to_test:
            if hasattr(driver, method_name):
                print(f"✅ Method available: {method_name}")
            else:
                print(f"❌ Method missing: {method_name}")
                return False
        
        print("✅ All enhanced methods available")
        return True
    
    except Exception as e:
        print(f"❌ Driver enhancement test failed: {str(e)}")
        return False

def main():
    """Run comprehensive chain system tests"""
    print("🧪 COMPREHENSIVE CHAIN SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Chain System Components", test_chain_system_components),
        ("Chain Execution (Dry Run)", test_chain_execution_dry_run),
        ("Dashboard Integration", test_dashboard_integration),
        ("Appium Driver Enhancements", test_appium_driver_enhancements)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status:12} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("🌟 Chain-like Command System is ready for use!")
        print("\n🚀 READY FOR DASHBOARD:")
        print("   1. Go to: http://localhost:8501")
        print("   2. Navigate to: 📱 Emulator Testing")
        print("   3. Connect to: Pixel_6_API_34 (emulator-5554)")
        print("   4. Select: 🔗 Chain Testing")
        print("   5. Build and execute test chains!")
        
        print("\n✨ FEATURES AVAILABLE:")
        print("   • 🔨 Chain Builder - Create test sequences")
        print("   • ▶️ Chain Executor - Run automated tests")
        print("   • 📊 Results Viewer - Analyze execution results")
        print("   • 🔧 Code Generator - Export Appium test code")
        print("   • 🚀 Quick Templates - Pre-built test patterns")
        
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        print("💡 Check error messages above for details")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
