{"log": {"version": "1.2", "creator": {"name": "Test HAR Generator", "version": "1.0"}, "entries": [{"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/user/profile", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 150, "mimeType": "application/json", "text": "********************************************************************************************************************"}}, "time": 150, "startedDateTime": "2024-01-15T10:30:00.000Z"}, {"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/transactions/recent", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 250, "mimeType": "application/json", "text": "eyJ0cmFuc2FjdGlvbnMiOlt7ImlkIjoiVFhOMTIzIiwiYW1vdW50IjoxMDAwLCJzdGF0dXMiOiJzdWNjZXNzIiwiZGF0ZSI6IjIwMjQtMDEtMTUifSx7ImlkIjoiVFhOMTI0IiwiYW1vdW50Ijo1MDAsInN0YXR1cyI6InBlbmRpbmciLCJkYXRlIjoiMjAyNC0wMS0xNCJ9XX0="}}, "time": 200, "startedDateTime": "2024-01-15T10:30:01.000Z"}, {"request": {"method": "POST", "url": "https://dashboard.paytm.com/api/payment/initiate", "headers": [{"name": "Content-Type", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 180, "mimeType": "application/json", "text": "eyJwYXltZW50SWQiOiJQWU0xMjM0NSIsInN0YXR1cyI6ImluaXRpYXRlZCIsImFtb3VudCI6MTUwMCwiY3VycmVuY3kiOiJJTlIiLCJtZXJjaGFudElkIjoiTUVSQ0gxMjMifQ=="}}, "time": 300, "startedDateTime": "2024-01-15T10:30:02.000Z"}, {"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/analytics/dashboard", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 320, "mimeType": "application/json", "text": "eyJkYXNoYm9hcmQiOnsidG90YWxUcmFuc2FjdGlvbnMiOjEyNTAsInRvdGFsUmV2ZW51ZSI6MjUwMDAwLCJhY3RpdmVVc2VycyI6ODUwLCJwZW5kaW5nUGF5bWVudHMiOjE1LCJzdWNjZXNzUmF0ZSI6OTguNX19"}}, "time": 120, "startedDateTime": "2024-01-15T10:30:03.000Z"}, {"request": {"method": "GET", "url": "https://google-analytics.com/collect", "headers": [{"name": "Accept", "value": "*/*"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "image/gif"}], "content": {"size": 35, "mimeType": "image/gif", "text": "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"}}, "time": 50, "startedDateTime": "2024-01-15T10:30:04.000Z"}]}}