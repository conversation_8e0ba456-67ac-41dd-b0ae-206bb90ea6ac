"""
Android Studio Emulator Manager
Handles emulator detection, connection, and management
"""

import subprocess
import json
import re
import time
import os
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import psutil
import socket
from pathlib import Path

@dataclass
class EmulatorInstance:
    """Represents an Android emulator instance"""
    name: str
    port: int
    status: str
    api_level: int
    resolution: str
    device_name: str
    arch: str
    running: bool
    pid: Optional[int] = None
    adb_id: Optional[str] = None

class EmulatorManager:
    """Manages Android Studio emulator instances"""
    
    def __init__(self):
        self.emulators: Dict[str, EmulatorInstance] = {}
        self.adb_path = self._find_adb_path()
        self.emulator_path = self._find_emulator_path()
        
    def _find_adb_path(self) -> Optional[str]:
        """Find ADB executable path"""
        possible_paths = [
            "adb",  # If in PATH
            "/Users/<USER>/Library/Android/sdk/platform-tools/adb",  # macOS
            "/home/<USER>/Android/Sdk/platform-tools/adb",  # Linux
            "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools\\adb.exe",  # Windows
        ]

        for path_pattern in possible_paths:
            try:
                if "*" in path_pattern:
                    # Handle wildcard paths
                    from glob import glob
                    matches = glob(path_pattern)
                    if matches:
                        path = matches[0]
                    else:
                        continue
                else:
                    path = path_pattern

                # Check if path exists first
                if not os.path.exists(path) and "*" not in path_pattern:
                    continue

                result = subprocess.run([path, "version"], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError, NotADirectoryError, OSError):
                continue

        return None
    
    def _find_emulator_path(self) -> Optional[str]:
        """Find Android emulator executable path"""
        possible_paths = [
            "emulator",  # If in PATH
            "/Users/<USER>/Library/Android/sdk/emulator/emulator",  # macOS
            "/home/<USER>/Android/Sdk/emulator/emulator",  # Linux
            "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\emulator\\emulator.exe",  # Windows
        ]

        for path_pattern in possible_paths:
            try:
                if "*" in path_pattern:
                    from glob import glob
                    matches = glob(path_pattern)
                    if matches:
                        path = matches[0]
                    else:
                        continue
                else:
                    path = path_pattern

                # Check if path exists first
                if not os.path.exists(path) and "*" not in path_pattern:
                    continue

                result = subprocess.run([path, "-help"], capture_output=True, text=True, timeout=5)
                if result.returncode == 0 or "Android Emulator" in result.stderr:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError, NotADirectoryError, OSError):
                continue

        return None
    
    def discover_emulators(self) -> List[EmulatorInstance]:
        """Discover available and running emulators"""
        self.emulators.clear()
        
        # Get available AVDs
        available_avds = self._get_available_avds()
        
        # Get running emulators
        running_emulators = self._get_running_emulators()
        
        # Combine information
        for avd_name, avd_info in available_avds.items():
            running_info = running_emulators.get(avd_name)
            
            emulator = EmulatorInstance(
                name=avd_name,
                port=running_info.get('port', 0) if running_info else 0,
                status="running" if running_info else "stopped",
                api_level=avd_info.get('api_level', 0),
                resolution=avd_info.get('resolution', 'Unknown'),
                device_name=avd_info.get('device_name', 'Unknown'),
                arch=avd_info.get('arch', 'Unknown'),
                running=bool(running_info),
                pid=running_info.get('pid') if running_info else None,
                adb_id=running_info.get('adb_id') if running_info else None
            )
            
            self.emulators[avd_name] = emulator
        
        return list(self.emulators.values())
    
    def _get_available_avds(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available Android Virtual Devices"""
        if not self.emulator_path:
            return {}
        
        try:
            result = subprocess.run(
                [self.emulator_path, "-list-avds"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            avds = {}
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    avd_name = line.strip()
                    avd_info = self._get_avd_info(avd_name)
                    avds[avd_name] = avd_info
            
            return avds
        
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return {}
    
    def _get_avd_info(self, avd_name: str) -> Dict[str, Any]:
        """Get detailed information about an AVD"""
        try:
            # Try to get AVD configuration
            avd_dir = Path.home() / ".android" / "avd" / f"{avd_name}.avd"
            config_file = avd_dir / "config.ini"
            
            info = {
                'api_level': 0,
                'resolution': 'Unknown',
                'device_name': 'Unknown',
                'arch': 'Unknown'
            }
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config_content = f.read()
                
                # Parse configuration
                for line in config_content.split('\n'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if key == 'image.sysdir.1':
                            # Extract API level from system image path
                            api_match = re.search(r'android-(\d+)', value)
                            if api_match:
                                info['api_level'] = int(api_match.group(1))
                        
                        elif key == 'hw.lcd.width' or key == 'hw.lcd.height':
                            # Build resolution string
                            if 'resolution' not in info or info['resolution'] == 'Unknown':
                                info['resolution'] = value
                            else:
                                info['resolution'] = f"{info['resolution']}x{value}"
                        
                        elif key == 'hw.device.name':
                            info['device_name'] = value
                        
                        elif key == 'abi.type':
                            info['arch'] = value
            
            return info
        
        except Exception:
            return {
                'api_level': 0,
                'resolution': 'Unknown',
                'device_name': 'Unknown',
                'arch': 'Unknown'
            }
    
    def _get_running_emulators(self) -> Dict[str, Dict[str, Any]]:
        """Get list of currently running emulators"""
        running = {}
        
        if not self.adb_path:
            return running
        
        try:
            # Get ADB devices
            result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            for line in result.stdout.split('\n')[1:]:  # Skip header
                if line.strip() and '\t' in line:
                    device_id, status = line.strip().split('\t')
                    
                    if device_id.startswith('emulator-') and status == 'device':
                        port = int(device_id.split('-')[1])
                        
                        # Get emulator name
                        name_result = subprocess.run(
                            [self.adb_path, "-s", device_id, "emu", "avd", "name"],
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        
                        avd_name = name_result.stdout.strip() if name_result.returncode == 0 else f"emulator-{port}"
                        
                        # Find process PID
                        pid = self._find_emulator_pid(port)
                        
                        running[avd_name] = {
                            'port': port,
                            'adb_id': device_id,
                            'pid': pid
                        }
        
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
        
        return running
    
    def _find_emulator_pid(self, port: int) -> Optional[int]:
        """Find the process ID of an emulator by port"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'emulator' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        if any(str(port) in arg for arg in cmdline):
                            return proc.info['pid']
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass
        
        return None
    
    def start_emulator(self, avd_name: str, additional_args: List[str] = None) -> bool:
        """Start an emulator instance"""
        if not self.emulator_path:
            return False

        # First refresh the emulator list to get current status
        self.discover_emulators()

        # Check if already running
        if avd_name in self.emulators and self.emulators[avd_name].running:
            return True  # Already running

        # Check if there's an ADB device that might be this emulator
        adb_devices = self.get_adb_devices()
        for device_id, status in adb_devices.items():
            if status == 'device' and device_id.startswith('emulator-'):
                # This might be our emulator, refresh discovery
                self.discover_emulators()
                if avd_name in self.emulators and self.emulators[avd_name].running:
                    return True

        try:
            args = [self.emulator_path, "-avd", avd_name]
            if additional_args:
                args.extend(additional_args)
            else:
                # Add default args for better performance
                args.extend(["-no-snapshot-save", "-no-boot-anim"])

            # Start emulator in background
            subprocess.Popen(
                args,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True
            )

            # Wait a moment for startup
            time.sleep(3)

            # Refresh emulator list multiple times to catch startup
            for _ in range(5):
                self.discover_emulators()
                if avd_name in self.emulators and self.emulators[avd_name].running:
                    return True
                time.sleep(2)

            return avd_name in self.emulators and self.emulators[avd_name].running

        except Exception:
            return False
    
    def stop_emulator(self, avd_name: str) -> bool:
        """Stop an emulator instance"""
        if avd_name not in self.emulators or not self.emulators[avd_name].running:
            return True  # Already stopped
        
        emulator = self.emulators[avd_name]
        
        try:
            # Try graceful shutdown via ADB
            if self.adb_path and emulator.adb_id:
                subprocess.run(
                    [self.adb_path, "-s", emulator.adb_id, "emu", "kill"],
                    capture_output=True,
                    timeout=10
                )
            
            # If that doesn't work, try killing the process
            if emulator.pid:
                try:
                    proc = psutil.Process(emulator.pid)
                    proc.terminate()
                    proc.wait(timeout=10)
                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    if emulator.pid:
                        try:
                            proc = psutil.Process(emulator.pid)
                            proc.kill()
                        except psutil.NoSuchProcess:
                            pass
            
            # Wait and refresh
            time.sleep(2)
            self.discover_emulators()
            
            return avd_name not in self.emulators or not self.emulators[avd_name].running
        
        except Exception:
            return False
    
    def get_emulator_status(self, avd_name: str) -> Optional[EmulatorInstance]:
        """Get current status of a specific emulator"""
        return self.emulators.get(avd_name)
    
    def is_adb_available(self) -> bool:
        """Check if ADB is available"""
        return self.adb_path is not None
    
    def is_emulator_available(self) -> bool:
        """Check if Android emulator is available"""
        return self.emulator_path is not None

    def get_adb_devices(self) -> Dict[str, str]:
        """Get list of devices connected via ADB"""
        if not self.adb_path:
            return {}

        try:
            result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return {}

            devices = {}
            lines = result.stdout.strip().split('\n')[1:]  # Skip header

            for line in lines:
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        device_id = parts[0]
                        status = parts[1]
                        devices[device_id] = status

            return devices

        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return {}
    
    def get_device_logs(self, avd_name: str, lines: int = 100) -> str:
        """Get device logs from emulator"""
        if not self.adb_path or avd_name not in self.emulators:
            return ""
        
        emulator = self.emulators[avd_name]
        if not emulator.running or not emulator.adb_id:
            return ""
        
        try:
            result = subprocess.run(
                [self.adb_path, "-s", emulator.adb_id, "logcat", "-t", str(lines)],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            return result.stdout if result.returncode == 0 else ""
        
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return ""
