"""
Comprehensive Appium WebDriver Wrapper
Handles all mobile automation actions and gestures
"""

import time
import base64
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json

try:
    from appium import webdriver
    from appium.webdriver.common.touch_action import TouchAction
    from appium.webdriver.common.multi_action import MultiAction
    APPIUM_AVAILABLE = True
except ImportError:
    APPIUM_AVAILABLE = False
    # Mock classes for when Appium is not available
    class webdriver:
        class Remote:
            def __init__(self, *args, **kwargs):
                pass
    
    class TouchAction:
        def __init__(self, *args, **kwargs):
            pass
    
    class MultiAction:
        def __init__(self, *args, **kwargs):
            pass

@dataclass
class ElementInfo:
    """Information about a UI element"""
    id: str
    class_name: str
    text: str
    content_desc: str
    bounds: Dict[str, int]
    clickable: bool
    enabled: bool
    visible: bool

@dataclass
class TestAction:
    """Represents a test action"""
    action_type: str
    element_locator: Optional[str] = None
    text_input: Optional[str] = None
    coordinates: Optional[Tuple[int, int]] = None
    duration: Optional[int] = None
    description: str = ""

class AppiumDriverWrapper:
    """Comprehensive Appium WebDriver wrapper with advanced automation capabilities"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Remote] = None
        self.wait: Optional[WebDriverWait] = None
        self.connected_device: Optional[str] = None
        self.capabilities: Dict[str, Any] = {}
        
    def connect_to_device(self, device_id: str, app_package: str = None, 
                         app_activity: str = None, additional_caps: Dict[str, Any] = None) -> bool:
        """Connect to an Android device/emulator"""
        if not APPIUM_AVAILABLE:
            return False
        
        try:
            # Default capabilities
            desired_caps = {
                'platformName': 'Android',
                'deviceName': device_id,
                'udid': device_id,
                'automationName': 'UiAutomator2',
                'newCommandTimeout': 300,
                'noReset': True,
                'fullReset': False,
                'unicodeKeyboard': True,
                'resetKeyboard': True
            }
            
            # Add app-specific capabilities
            if app_package:
                desired_caps['appPackage'] = app_package
            if app_activity:
                desired_caps['appActivity'] = app_activity
            
            # Add additional capabilities
            if additional_caps:
                desired_caps.update(additional_caps)
            
            # Connect to Appium server
            self.driver = webdriver.Remote(
                command_executor='http://localhost:4723/wd/hub',
                desired_capabilities=desired_caps
            )
            
            self.wait = WebDriverWait(self.driver, 10)
            self.connected_device = device_id
            self.capabilities = desired_caps
            
            return True
        
        except Exception as e:
            print(f"Failed to connect to device {device_id}: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from the device"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
            finally:
                self.driver = None
                self.wait = None
                self.connected_device = None
    
    def is_connected(self) -> bool:
        """Check if connected to a device"""
        return self.driver is not None
    
    # Element Location Methods
    def find_element_by_id(self, element_id: str, timeout: int = 10):
        """Find element by resource ID"""
        if not self.driver:
            return None
        
        try:
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.ID, element_id))
            )
        except TimeoutException:
            return None
    
    def find_element_by_xpath(self, xpath: str, timeout: int = 10):
        """Find element by XPath"""
        if not self.driver:
            return None
        
        try:
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
        except TimeoutException:
            return None
    
    def find_element_by_accessibility_id(self, accessibility_id: str, timeout: int = 10):
        """Find element by accessibility ID"""
        if not self.driver:
            return None
        
        try:
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(("accessibility id", accessibility_id))
            )
        except TimeoutException:
            return None
    
    def find_element_by_class_name(self, class_name: str, timeout: int = 10):
        """Find element by class name"""
        if not self.driver:
            return None
        
        try:
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.CLASS_NAME, class_name))
            )
        except TimeoutException:
            return None
    
    def find_elements_by_text(self, text: str, exact_match: bool = True) -> List:
        """Find elements containing specific text"""
        if not self.driver:
            return []
        
        try:
            if exact_match:
                xpath = f"//*[@text='{text}']"
            else:
                xpath = f"//*[contains(@text, '{text}')]"
            
            return self.driver.find_elements(By.XPATH, xpath)
        except Exception:
            return []
    
    # Basic Actions
    def tap(self, x: int, y: int, duration: int = 100) -> bool:
        """Tap at specific coordinates"""
        if not self.driver:
            return False
        
        try:
            TouchAction(self.driver).tap(x=x, y=y).wait(duration).perform()
            return True
        except Exception:
            return False
    
    def tap_element(self, element) -> bool:
        """Tap on a specific element"""
        if not element:
            return False
        
        try:
            element.click()
            return True
        except Exception:
            return False
    
    def long_press(self, x: int, y: int, duration: int = 1000) -> bool:
        """Long press at specific coordinates"""
        if not self.driver:
            return False
        
        try:
            TouchAction(self.driver).long_press(x=x, y=y, duration=duration).perform()
            return True
        except Exception:
            return False
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 1000) -> bool:
        """Swipe from start coordinates to end coordinates"""
        if not self.driver:
            return False
        
        try:
            self.driver.swipe(start_x, start_y, end_x, end_y, duration)
            return True
        except Exception:
            return False
    
    def scroll_down(self, distance: int = None) -> bool:
        """Scroll down on the screen"""
        if not self.driver:
            return False
        
        try:
            size = self.driver.get_window_size()
            start_x = size['width'] // 2
            start_y = size['height'] * 0.8
            end_y = size['height'] * 0.2
            
            if distance:
                end_y = max(start_y - distance, size['height'] * 0.1)
            
            return self.swipe(start_x, int(start_y), start_x, int(end_y))
        except Exception:
            return False
    
    def scroll_up(self, distance: int = None) -> bool:
        """Scroll up on the screen"""
        if not self.driver:
            return False
        
        try:
            size = self.driver.get_window_size()
            start_x = size['width'] // 2
            start_y = size['height'] * 0.2
            end_y = size['height'] * 0.8
            
            if distance:
                end_y = min(start_y + distance, size['height'] * 0.9)
            
            return self.swipe(start_x, int(start_y), start_x, int(end_y))
        except Exception:
            return False
    
    def type_text(self, text: str, element=None) -> bool:
        """Type text into an element or active field"""
        if not self.driver:
            return False
        
        try:
            if element:
                element.clear()
                element.send_keys(text)
            else:
                self.driver.set_value(text)
            return True
        except Exception:
            return False
    
    def clear_text(self, element=None) -> bool:
        """Clear text from an element or active field"""
        if not self.driver:
            return False
        
        try:
            if element:
                element.clear()
            else:
                # Select all and delete
                self.driver.keyevent(113)  # CTRL+A
                self.driver.keyevent(67)   # DEL
            return True
        except Exception:
            return False
    
    # Advanced Gestures
    def pinch_zoom(self, center_x: int, center_y: int, scale: float = 2.0) -> bool:
        """Perform pinch zoom gesture"""
        if not self.driver:
            return False
        
        try:
            # Calculate touch points
            offset = 100
            x1, y1 = center_x - offset, center_y - offset
            x2, y2 = center_x + offset, center_y + offset
            
            # Create multi-touch action
            action1 = TouchAction(self.driver)
            action2 = TouchAction(self.driver)
            
            if scale > 1.0:  # Zoom in
                action1.press(x=center_x, y=center_y).move_to(x=x1, y=y1).release()
                action2.press(x=center_x, y=center_y).move_to(x=x2, y=y2).release()
            else:  # Zoom out
                action1.press(x=x1, y=y1).move_to(x=center_x, y=center_y).release()
                action2.press(x=x2, y=y2).move_to(x=center_x, y=center_y).release()
            
            multi_action = MultiAction(self.driver)
            multi_action.add(action1, action2)
            multi_action.perform()
            
            return True
        except Exception:
            return False
    
    def drag_and_drop(self, start_x: int, start_y: int, end_x: int, end_y: int) -> bool:
        """Drag and drop from start to end coordinates"""
        if not self.driver:
            return False
        
        try:
            TouchAction(self.driver).press(x=start_x, y=start_y).wait(500).move_to(x=end_x, y=end_y).release().perform()
            return True
        except Exception:
            return False
    
    # App Management
    def install_app(self, app_path: str) -> bool:
        """Install an app from APK file"""
        if not self.driver:
            return False
        
        try:
            self.driver.install_app(app_path)
            return True
        except Exception:
            return False
    
    def launch_app(self, package_name: str, activity_name: str = None) -> bool:
        """Launch an app by package name"""
        if not self.driver:
            return False
        
        try:
            if activity_name:
                self.driver.start_activity(package_name, activity_name)
            else:
                self.driver.activate_app(package_name)
            return True
        except Exception:
            return False
    
    def close_app(self, package_name: str) -> bool:
        """Close/terminate an app"""
        if not self.driver:
            return False
        
        try:
            self.driver.terminate_app(package_name)
            return True
        except Exception:
            return False
    
    def background_app(self, duration: int = 5) -> bool:
        """Put app in background for specified duration"""
        if not self.driver:
            return False
        
        try:
            self.driver.background_app(duration)
            return True
        except Exception:
            return False
    
    # Device Actions
    def press_back(self) -> bool:
        """Press device back button"""
        if not self.driver:
            return False
        
        try:
            self.driver.back()
            return True
        except Exception:
            return False
    
    def press_home(self) -> bool:
        """Press device home button"""
        if not self.driver:
            return False
        
        try:
            self.driver.press_keycode(3)  # HOME key
            return True
        except Exception:
            return False
    
    def press_menu(self) -> bool:
        """Press device menu button"""
        if not self.driver:
            return False
        
        try:
            self.driver.press_keycode(82)  # MENU key
            return True
        except Exception:
            return False
    
    def rotate_device(self, orientation: str) -> bool:
        """Rotate device to specified orientation"""
        if not self.driver:
            return False
        
        try:
            if orientation.lower() in ['portrait', 'landscape']:
                self.driver.orientation = orientation.upper()
                return True
            return False
        except Exception:
            return False
    
    # Information Gathering
    def take_screenshot(self) -> Optional[str]:
        """Take screenshot and return as base64 string"""
        if not self.driver:
            return None
        
        try:
            return self.driver.get_screenshot_as_base64()
        except Exception:
            return None
    
    def get_page_source(self) -> Optional[str]:
        """Get current page source XML"""
        if not self.driver:
            return None
        
        try:
            return self.driver.page_source
        except Exception:
            return None
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get device information"""
        if not self.driver:
            return {}
        
        try:
            return {
                'platform_name': self.driver.capabilities.get('platformName'),
                'platform_version': self.driver.capabilities.get('platformVersion'),
                'device_name': self.driver.capabilities.get('deviceName'),
                'udid': self.driver.capabilities.get('udid'),
                'screen_size': self.driver.get_window_size(),
                'orientation': self.driver.orientation
            }
        except Exception:
            return {}
    
    def wait_for_element(self, locator_type: str, locator_value: str, timeout: int = 10):
        """Wait for element to be present"""
        if not self.driver or not self.wait:
            return None

        try:
            locator_map = {
                'id': By.ID,
                'xpath': By.XPATH,
                'class': By.CLASS_NAME,
                'accessibility_id': 'accessibility id'
            }

            locator = locator_map.get(locator_type, By.ID)
            return WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((locator, locator_value))
            )
        except TimeoutException:
            return None

    def get_element_info(self, element) -> Optional[ElementInfo]:
        """Get detailed information about an element"""
        if not element:
            return None

        try:
            bounds = element.rect
            return ElementInfo(
                id=element.get_attribute('resource-id') or '',
                class_name=element.get_attribute('class') or '',
                text=element.get_attribute('text') or '',
                content_desc=element.get_attribute('content-desc') or '',
                bounds=bounds,
                clickable=element.get_attribute('clickable') == 'true',
                enabled=element.get_attribute('enabled') == 'true',
                visible=element.is_displayed()
            )
        except Exception:
            return None

    def execute_test_action(self, action: TestAction) -> bool:
        """Execute a test action"""
        try:
            if action.action_type == 'tap':
                if action.coordinates:
                    return self.tap(action.coordinates[0], action.coordinates[1])
                elif action.element_locator:
                    element = self.find_element_by_xpath(action.element_locator)
                    return self.tap_element(element)

            elif action.action_type == 'type':
                if action.element_locator:
                    element = self.find_element_by_xpath(action.element_locator)
                    return self.type_text(action.text_input, element)
                else:
                    return self.type_text(action.text_input)

            elif action.action_type == 'swipe':
                if len(action.coordinates) == 4:
                    return self.swipe(*action.coordinates, action.duration or 1000)

            elif action.action_type == 'scroll_down':
                return self.scroll_down()

            elif action.action_type == 'scroll_up':
                return self.scroll_up()

            elif action.action_type == 'back':
                return self.press_back()

            elif action.action_type == 'home':
                return self.press_home()

            return False
        except Exception:
            return False
