"""
Figma Design Validation Engine
Comprehensive design validation and comparison system
"""

import streamlit as st
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import json
import colorsys
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import base64
from io import BytesIO
import pandas as pd

@dataclass
class ColorMatch:
    """Color matching result"""
    reference_color: Tuple[int, int, int]
    actual_color: Tuple[int, int, int]
    difference: float
    tolerance_met: bool
    hex_reference: str
    hex_actual: str

@dataclass
class PixelDifference:
    """Pixel-level difference data"""
    x: int
    y: int
    reference_color: Tuple[int, int, int]
    actual_color: Tuple[int, int, int]
    difference_score: float

@dataclass
class ValidationResult:
    """Complete validation result"""
    overall_score: float
    pixel_accuracy: float
    color_accuracy: float
    layout_accuracy: float
    accessibility_score: float
    differences: List[PixelDifference]
    color_mismatches: List[ColorMatch]
    recommendations: List[str]

class FigmaValidationEngine:
    """Core engine for Figma design validation"""
    
    def __init__(self):
        self.color_tolerance = 10  # RGB tolerance
        self.pixel_tolerance = 0.95  # 95% similarity threshold
        self.accessibility_standards = {
            'min_contrast_ratio': 4.5,
            'min_font_size': 12,
            'min_touch_target': 44
        }
    
    def extract_colors_from_image(self, image: Image.Image, max_colors: int = 16) -> List[Tuple[int, int, int]]:
        """Extract dominant colors from image using simplified method"""
        try:
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Resize for faster processing
            image = image.resize((150, 150))

            # Convert to numpy array
            img_array = np.array(image)
            pixels = img_array.reshape(-1, 3)

            # Simple color extraction using histogram
            unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)

            # Sort by frequency and take top colors
            sorted_indices = np.argsort(counts)[::-1]
            top_colors = unique_colors[sorted_indices[:max_colors]]

            return [tuple(color) for color in top_colors]

        except Exception as e:
            st.error(f"Error extracting colors: {str(e)}")
            # Return some default colors for demonstration
            return [(255, 255, 255), (0, 0, 0), (128, 128, 128), (255, 0, 0), (0, 255, 0), (0, 0, 255)]
    
    def rgb_to_hex(self, rgb: Tuple[int, int, int]) -> str:
        """Convert RGB to hex"""
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def calculate_color_difference(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """Calculate perceptual color difference using Delta E"""
        try:
            # Convert RGB to LAB color space for better perceptual difference
            def rgb_to_lab(rgb):
                # Simplified RGB to LAB conversion
                r, g, b = [x/255.0 for x in rgb]
                
                # Apply gamma correction
                def gamma_correct(c):
                    return pow((c + 0.055) / 1.055, 2.4) if c > 0.04045 else c / 12.92
                
                r, g, b = map(gamma_correct, [r, g, b])
                
                # Convert to XYZ
                x = r * 0.4124564 + g * 0.3575761 + b * 0.1804375
                y = r * 0.2126729 + g * 0.7151522 + b * 0.0721750
                z = r * 0.0193339 + g * 0.1191920 + b * 0.9503041
                
                # Convert to LAB
                def f(t):
                    return pow(t, 1/3) if t > 0.008856 else (7.787 * t + 16/116)
                
                fx, fy, fz = map(f, [x/0.95047, y/1.00000, z/1.08883])
                
                L = 116 * fy - 16
                a = 500 * (fx - fy)
                b = 200 * (fy - fz)
                
                return L, a, b
            
            lab1 = rgb_to_lab(color1)
            lab2 = rgb_to_lab(color2)
            
            # Calculate Delta E
            delta_e = np.sqrt(sum((a - b) ** 2 for a, b in zip(lab1, lab2)))
            return delta_e
        
        except Exception:
            # Fallback to simple Euclidean distance
            return np.sqrt(sum((a - b) ** 2 for a, b in zip(color1, color2)))
    
    def compare_images_pixel_perfect(self, reference_img: Image.Image, actual_img: Image.Image) -> Tuple[float, List[PixelDifference], Image.Image]:
        """Perform pixel-perfect comparison between two images"""
        try:
            # Ensure both images are the same size
            if reference_img.size != actual_img.size:
                actual_img = actual_img.resize(reference_img.size, Image.Resampling.LANCZOS)
            
            # Convert to RGB
            ref_rgb = reference_img.convert('RGB')
            actual_rgb = actual_img.convert('RGB')
            
            # Convert to numpy arrays
            ref_array = np.array(ref_rgb)
            actual_array = np.array(actual_rgb)
            
            # Calculate pixel differences
            differences = []
            total_pixels = ref_array.shape[0] * ref_array.shape[1]
            matching_pixels = 0
            
            # Create difference overlay
            diff_overlay = np.zeros_like(ref_array)
            
            for y in range(ref_array.shape[0]):
                for x in range(ref_array.shape[1]):
                    ref_pixel = tuple(ref_array[y, x])
                    actual_pixel = tuple(actual_array[y, x])
                    
                    color_diff = self.calculate_color_difference(ref_pixel, actual_pixel)
                    
                    if color_diff <= self.color_tolerance:
                        matching_pixels += 1
                        diff_overlay[y, x] = [0, 255, 0]  # Green for matches
                    else:
                        # Red intensity based on difference
                        intensity = min(255, int(color_diff * 10))
                        diff_overlay[y, x] = [intensity, 0, 0]  # Red for differences
                        
                        differences.append(PixelDifference(
                            x=x, y=y,
                            reference_color=ref_pixel,
                            actual_color=actual_pixel,
                            difference_score=color_diff
                        ))
            
            accuracy = matching_pixels / total_pixels
            overlay_image = Image.fromarray(diff_overlay)
            
            return accuracy, differences, overlay_image
        
        except Exception as e:
            st.error(f"Error in pixel comparison: {str(e)}")
            return 0.0, [], Image.new('RGB', (100, 100), 'red')
    
    def validate_color_palette(self, reference_colors: List[Tuple[int, int, int]], actual_colors: List[Tuple[int, int, int]]) -> List[ColorMatch]:
        """Validate color palette consistency"""
        matches = []
        
        for ref_color in reference_colors:
            best_match = None
            best_difference = float('inf')
            
            for actual_color in actual_colors:
                difference = self.calculate_color_difference(ref_color, actual_color)
                if difference < best_difference:
                    best_difference = difference
                    best_match = actual_color
            
            if best_match:
                tolerance_met = best_difference <= self.color_tolerance
                matches.append(ColorMatch(
                    reference_color=ref_color,
                    actual_color=best_match,
                    difference=best_difference,
                    tolerance_met=tolerance_met,
                    hex_reference=self.rgb_to_hex(ref_color),
                    hex_actual=self.rgb_to_hex(best_match)
                ))
        
        return matches
    
    def check_accessibility_compliance(self, image: Image.Image) -> Dict[str, Any]:
        """Check accessibility compliance"""
        try:
            # Extract colors for contrast checking
            colors = self.extract_colors_from_image(image)
            
            # Calculate contrast ratios
            contrast_issues = []
            for i, color1 in enumerate(colors):
                for j, color2 in enumerate(colors[i+1:], i+1):
                    contrast_ratio = self.calculate_contrast_ratio(color1, color2)
                    if contrast_ratio < self.accessibility_standards['min_contrast_ratio']:
                        contrast_issues.append({
                            'color1': self.rgb_to_hex(color1),
                            'color2': self.rgb_to_hex(color2),
                            'contrast_ratio': contrast_ratio,
                            'required': self.accessibility_standards['min_contrast_ratio']
                        })
            
            return {
                'contrast_issues': contrast_issues,
                'total_colors': len(colors),
                'compliance_score': max(0, 1 - len(contrast_issues) / max(1, len(colors)))
            }
        
        except Exception as e:
            st.error(f"Error in accessibility check: {str(e)}")
            return {'contrast_issues': [], 'total_colors': 0, 'compliance_score': 0}
    
    def calculate_contrast_ratio(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """Calculate WCAG contrast ratio"""
        def luminance(rgb):
            r, g, b = [x/255.0 for x in rgb]
            
            def gamma_correct(c):
                return pow((c + 0.055) / 1.055, 2.4) if c > 0.04045 else c / 12.92
            
            r, g, b = map(gamma_correct, [r, g, b])
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        lum1 = luminance(color1)
        lum2 = luminance(color2)
        
        lighter = max(lum1, lum2)
        darker = min(lum1, lum2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    def perform_comprehensive_validation(self, reference_img: Image.Image, actual_img: Image.Image, figma_data: Optional[Dict] = None) -> ValidationResult:
        """Perform comprehensive design validation"""
        try:
            # Pixel-perfect comparison
            pixel_accuracy, pixel_differences, overlay = self.compare_images_pixel_perfect(reference_img, actual_img)
            
            # Color validation
            ref_colors = self.extract_colors_from_image(reference_img)
            actual_colors = self.extract_colors_from_image(actual_img)
            color_matches = self.validate_color_palette(ref_colors, actual_colors)
            
            color_accuracy = sum(1 for match in color_matches if match.tolerance_met) / max(1, len(color_matches))
            
            # Accessibility check
            accessibility_result = self.check_accessibility_compliance(actual_img)
            accessibility_score = accessibility_result['compliance_score']
            
            # Layout accuracy (simplified - based on structural similarity)
            layout_accuracy = self.calculate_structural_similarity(reference_img, actual_img)
            
            # Overall score
            overall_score = (pixel_accuracy * 0.3 + color_accuracy * 0.3 + layout_accuracy * 0.2 + accessibility_score * 0.2)
            
            # Generate recommendations
            recommendations = self.generate_recommendations(pixel_accuracy, color_accuracy, layout_accuracy, accessibility_score, accessibility_result)
            
            return ValidationResult(
                overall_score=overall_score,
                pixel_accuracy=pixel_accuracy,
                color_accuracy=color_accuracy,
                layout_accuracy=layout_accuracy,
                accessibility_score=accessibility_score,
                differences=pixel_differences[:100],  # Limit for performance
                color_mismatches=[match for match in color_matches if not match.tolerance_met],
                recommendations=recommendations
            )
        
        except Exception as e:
            st.error(f"Error in comprehensive validation: {str(e)}")
            return ValidationResult(0, 0, 0, 0, 0, [], [], ["Validation failed due to technical error"])
    
    def calculate_structural_similarity(self, img1: Image.Image, img2: Image.Image) -> float:
        """Calculate structural similarity between images"""
        try:
            # Convert to grayscale
            gray1 = img1.convert('L')
            gray2 = img2.convert('L')
            
            # Resize to same size
            if gray1.size != gray2.size:
                gray2 = gray2.resize(gray1.size)
            
            # Convert to numpy arrays
            arr1 = np.array(gray1)
            arr2 = np.array(gray2)
            
            # Calculate mean squared error
            mse = np.mean((arr1 - arr2) ** 2)
            
            # Convert to similarity score (0-1)
            max_pixel_value = 255
            similarity = 1 - (mse / (max_pixel_value ** 2))
            
            return max(0, similarity)
        
        except Exception:
            return 0.5  # Default moderate similarity
    
    def generate_recommendations(self, pixel_acc: float, color_acc: float, layout_acc: float, accessibility_score: float, accessibility_data: Dict) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        if pixel_acc < 0.9:
            recommendations.append(f"Pixel accuracy is {pixel_acc:.1%}. Review element positioning and sizing.")
        
        if color_acc < 0.8:
            recommendations.append(f"Color accuracy is {color_acc:.1%}. Verify brand color implementation.")
        
        if layout_acc < 0.8:
            recommendations.append(f"Layout similarity is {layout_acc:.1%}. Check component structure and spacing.")
        
        if accessibility_score < 0.8:
            recommendations.append(f"Accessibility score is {accessibility_score:.1%}. Review contrast ratios and text sizes.")
            
            if accessibility_data.get('contrast_issues'):
                recommendations.append(f"Found {len(accessibility_data['contrast_issues'])} contrast ratio issues.")
        
        if not recommendations:
            recommendations.append("Excellent! Design implementation meets all validation criteria.")
        
        return recommendations
