#!/usr/bin/env python3
"""
Fix core app functionality and chain execution issues
"""

import subprocess
import time
import sys

def check_basic_connectivity():
    """Check basic connectivity to emulator"""
    print("🔍 Checking Basic Connectivity")
    print("=" * 35)
    
    # Check ADB devices
    print("1️⃣ Checking ADB devices...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        print(f"ADB output:\n{result.stdout}")
        
        if "emulator-5554" in result.stdout and "device" in result.stdout:
            print("✅ emulator-5554 is connected via ADB")
        else:
            print("❌ emulator-5554 not found or not ready")
            return False
    except Exception as e:
        print(f"❌ ADB check failed: {e}")
        return False
    
    # Test basic ADB command
    print("\n2️⃣ Testing ADB shell access...")
    try:
        result = subprocess.run(['adb', '-s', 'emulator-5554', 'shell', 'echo', 'test'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ADB shell access working")
        else:
            print("❌ ADB shell access failed")
            return False
    except Exception as e:
        print(f"❌ ADB shell test failed: {e}")
        return False
    
    # Check if emulator is fully booted
    print("\n3️⃣ Checking emulator boot status...")
    try:
        result = subprocess.run(['adb', '-s', 'emulator-5554', 'shell', 'getprop', 'sys.boot_completed'], 
                              capture_output=True, text=True, timeout=10)
        boot_completed = result.stdout.strip() == '1'
        print(f"Boot completed: {'✅' if boot_completed else '❌'}")
        
        if not boot_completed:
            print("⚠️ Emulator not fully booted - this will cause app launch failures")
            return False
    except Exception as e:
        print(f"❌ Boot status check failed: {e}")
        return False
    
    return True

def check_appium_server():
    """Check Appium server status and capabilities"""
    print("\n🔍 Checking Appium Server")
    print("=" * 25)
    
    try:
        import requests
        
        # Check server status
        print("1️⃣ Checking server status...")
        response = requests.get("http://localhost:4723/status", timeout=5)
        if response.status_code == 200:
            print("✅ Appium server is running")
            data = response.json()
            print(f"   Version: {data.get('value', {}).get('build', {}).get('version', 'Unknown')}")
        else:
            print(f"❌ Appium server returned status {response.status_code}")
            return False
        
        # Check sessions
        print("\n2️⃣ Checking active sessions...")
        response = requests.get("http://localhost:4723/sessions", timeout=5)
        if response.status_code == 200:
            sessions = response.json().get('value', [])
            print(f"Active sessions: {len(sessions)}")
            
            # Clean up any existing sessions
            for session in sessions:
                session_id = session.get('id')
                if session_id:
                    try:
                        requests.delete(f"http://localhost:4723/session/{session_id}", timeout=5)
                        print(f"   Cleaned up session: {session_id}")
                    except:
                        pass
        
        return True
        
    except Exception as e:
        print(f"❌ Appium server check failed: {e}")
        return False

def test_direct_appium_connection():
    """Test direct Appium connection with proper capabilities"""
    print("\n🔍 Testing Direct Appium Connection")
    print("=" * 35)
    
    try:
        from appium import webdriver
        from appium.options.android import UiAutomator2Options
        
        print("1️⃣ Setting up capabilities...")
        
        # Use proper Appium 2.x capabilities
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.no_reset = True
        options.full_reset = False
        
        print("✅ Capabilities configured")
        
        print("2️⃣ Connecting to Appium...")
        driver = webdriver.Remote("http://localhost:4723", options=options)
        print("✅ Connected to Appium successfully")
        
        print("3️⃣ Testing basic operations...")
        
        # Test window size
        window_size = driver.get_window_size()
        print(f"✅ Window size: {window_size['width']}x{window_size['height']}")
        
        # Test screenshot
        screenshot = driver.get_screenshot_as_base64()
        print(f"✅ Screenshot captured: {len(screenshot)} characters")
        
        # Test current activity
        current_activity = driver.current_activity
        print(f"✅ Current activity: {current_activity}")
        
        # Test app launch
        print("4️⃣ Testing app launch...")
        driver.activate_app("com.android.settings")
        time.sleep(3)
        
        new_activity = driver.current_activity
        print(f"✅ App launched, new activity: {new_activity}")
        
        # Test home button
        print("5️⃣ Testing home button...")
        driver.press_keycode(3)  # Home key
        time.sleep(2)
        
        home_activity = driver.current_activity
        print(f"✅ Home button pressed, activity: {home_activity}")
        
        # Cleanup
        driver.quit()
        print("✅ Connection test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct Appium connection failed: {e}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return False

def fix_appium_driver_enhanced():
    """Fix the AppiumDriverEnhanced class"""
    print("\n🔧 Fixing AppiumDriverEnhanced")
    print("=" * 30)
    
    # Create fixed version
    fixed_code = '''
def connect_to_device_fixed(self, device_id: str) -> Tuple[bool, 'ConnectionDiagnostics']:
    """Fixed connection method with proper Appium 2.x support"""
    
    diagnostics = ConnectionDiagnostics()
    
    try:
        # Clean up any existing connection
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
        
        # Check Appium server
        if not self.check_appium_server():
            diagnostics.error_message = "Appium server not running"
            diagnostics.suggestions.append("Start Appium server: appium --port 4723")
            return False, diagnostics
        
        # Set up proper capabilities for Appium 2.x
        from appium import webdriver
        from appium.options.android import UiAutomator2Options
        
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = device_id
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.no_reset = True
        options.full_reset = False
        
        # Connect
        self.driver = webdriver.Remote("http://localhost:4723", options=options)
        self.device_id = device_id
        self.connected = True
        
        # Test connection
        window_size = self.driver.get_window_size()
        current_activity = self.driver.current_activity
        
        diagnostics.success = True
        diagnostics.connection_time = 2.0  # Approximate
        
        return True, diagnostics
        
    except Exception as e:
        diagnostics.error_message = str(e)
        diagnostics.suggestions.append("Check emulator is running and responsive")
        diagnostics.suggestions.append("Restart Appium server if needed")
        return False, diagnostics
'''
    
    # Write to file
    with open("appium_driver_fixed.py", "w") as f:
        f.write(fixed_code)
    
    print("✅ Created appium_driver_fixed.py")
    return True

def create_simple_working_test():
    """Create a simple test that should work"""
    print("\n🧪 Creating Simple Working Test")
    print("=" * 30)
    
    test_code = '''
#!/usr/bin/env python3
"""
Simple working test for app functionality
"""

from appium import webdriver
from appium.options.android import UiAutomator2Options
import time

def test_basic_functionality():
    """Test basic app functionality"""
    print("🧪 Testing Basic App Functionality")
    print("=" * 35)
    
    try:
        # Setup
        print("1️⃣ Setting up connection...")
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.no_reset = True
        
        # Connect
        driver = webdriver.Remote("http://localhost:4723", options=options)
        print("✅ Connected to emulator")
        
        # Test 1: Screenshot
        print("2️⃣ Testing screenshot...")
        screenshot = driver.get_screenshot_as_base64()
        print(f"✅ Screenshot: {len(screenshot)} chars")
        
        # Test 2: Current activity
        print("3️⃣ Testing current activity...")
        activity = driver.current_activity
        print(f"✅ Current activity: {activity}")
        
        # Test 3: Launch Settings
        print("4️⃣ Testing app launch...")
        driver.activate_app("com.android.settings")
        time.sleep(3)
        
        new_activity = driver.current_activity
        print(f"✅ Settings launched: {new_activity}")
        
        # Test 4: Take screenshot of Settings
        print("5️⃣ Testing screenshot in app...")
        app_screenshot = driver.get_screenshot_as_base64()
        print(f"✅ App screenshot: {len(app_screenshot)} chars")
        
        # Test 5: Home button
        print("6️⃣ Testing home button...")
        driver.press_keycode(3)
        time.sleep(2)
        
        home_activity = driver.current_activity
        print(f"✅ Home button: {home_activity}")
        
        # Cleanup
        driver.quit()
        print("✅ Test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_basic_functionality()
'''
    
    with open("test_basic_app_functionality.py", "w") as f:
        f.write(test_code)
    
    print("✅ Created test_basic_app_functionality.py")
    return True

def restart_appium_server():
    """Restart Appium server"""
    print("\n🔄 Restarting Appium Server")
    print("=" * 25)
    
    try:
        # Kill existing Appium processes
        print("1️⃣ Stopping existing Appium...")
        subprocess.run(['pkill', '-f', 'appium'], capture_output=True)
        time.sleep(2)
        
        # Start new Appium server
        print("2️⃣ Starting new Appium server...")
        subprocess.Popen(['appium', '--port', '4723'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        print("3️⃣ Waiting for server to start...")
        time.sleep(5)
        
        # Test server
        import requests
        response = requests.get("http://localhost:4723/status", timeout=5)
        if response.status_code == 200:
            print("✅ Appium server restarted successfully")
            return True
        else:
            print("❌ Appium server failed to start")
            return False
            
    except Exception as e:
        print(f"❌ Failed to restart Appium: {e}")
        return False

def main():
    """Main diagnostic and fix function"""
    print("🔧 FIXING APP FUNCTIONALITY ISSUES")
    print("=" * 40)
    
    # Step 1: Check basic connectivity
    if not check_basic_connectivity():
        print("\n❌ BASIC CONNECTIVITY FAILED")
        print("💡 Fix emulator connection first:")
        print("   • Ensure emulator-5554 is running")
        print("   • Wait for complete boot")
        print("   • Try: adb kill-server && adb start-server")
        return
    
    # Step 2: Check Appium server
    if not check_appium_server():
        print("\n🔄 Attempting to restart Appium server...")
        if not restart_appium_server():
            print("❌ APPIUM SERVER ISSUES")
            print("💡 Manual fix needed:")
            print("   • Kill Appium: pkill -f appium")
            print("   • Start Appium: appium --port 4723")
            return
    
    # Step 3: Test direct connection
    if not test_direct_appium_connection():
        print("\n❌ DIRECT CONNECTION FAILED")
        print("💡 This indicates core Appium issues")
        return
    
    # Step 4: Create fixes
    fix_appium_driver_enhanced()
    create_simple_working_test()
    
    print("\n" + "=" * 40)
    print("✅ FIXES CREATED")
    print("=" * 40)
    
    print("\n🧪 TEST THE FIXES:")
    print("1. Run basic test:")
    print("   ./kb_venv/bin/python test_basic_app_functionality.py")
    
    print("\n2. If basic test works, try dashboard:")
    print("   • Go to: http://localhost:8501")
    print("   • Connect to device first")
    print("   • Try simple operations")
    
    print("\n💡 TROUBLESHOOTING:")
    print("• If basic test fails: Emulator/Appium issues")
    print("• If basic test works but dashboard fails: UI integration issues")
    print("• Always connect to device BEFORE trying operations")

if __name__ == "__main__":
    main()
