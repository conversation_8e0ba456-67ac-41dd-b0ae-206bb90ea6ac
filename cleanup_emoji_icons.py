#!/usr/bin/env python3
"""
Comprehensive cleanup script to remove all emoji icons from dashboard files
"""

import re
import os

def clean_emoji_icons(file_path):
    """Remove emoji icons from a file and replace with professional alternatives"""
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Define emoji replacements
    emoji_replacements = {
        # Status indicators
        '✅': '✓',
        '❌': '✗',
        '⚠️': '⚠',
        '🔴': '●',
        '🟡': '●',
        '🟢': '●',
        '⚪': '○',
        
        # Action icons
        '🔍': '',  # Search - remove emoji, keep text
        '🔄': '',  # Refresh - remove emoji, keep text
        '📊': '',  # Analytics - remove emoji, keep text
        '💾': '',  # Save - remove emoji, keep text
        '🔧': '',  # Settings - remove emoji, keep text
        '📱': '',  # Mobile - remove emoji, keep text
        '🎯': '',  # Target - remove emoji, keep text
        '▶️': '',  # Play - remove emoji, keep text
        '⏸️': '⏸',  # Pause - keep simple version
        '🚀': '',  # Rocket - remove emoji, keep text
        '💡': '',  # Idea - remove emoji, keep text
        '🔒': '',  # Security - remove emoji, keep text
        '📈': '',  # Trending up - remove emoji, keep text
        '📄': '',  # Document - remove emoji, keep text
        '🤖': '',  # Robot - remove emoji, keep text
        '🎨': '',  # Art - remove emoji, keep text
        '🛡️': '',  # Shield - remove emoji, keep text
        '⚡': '',  # Lightning - remove emoji, keep text
        '🏠': '',  # Home - remove emoji, keep text
        '⬅️': '',  # Left arrow - remove emoji, keep text
        '👆': '',  # Point up - remove emoji, keep text
        '⌨️': '',  # Keyboard - remove emoji, keep text
        '📸': '',  # Camera - remove emoji, keep text
        '📋': '',  # Clipboard - remove emoji, keep text
        '🔗': '',  # Link - remove emoji, keep text
        '🔨': '',  # Hammer - remove emoji, keep text
        '🆕': '',  # New - remove emoji, keep text
        '🔐': '',  # Locked - remove emoji, keep text
        '🛑': '',  # Stop - remove emoji, keep text
        '🎉': '',  # Party - remove emoji, keep text
        '🐛': '',  # Bug - remove emoji, keep text
        '📚': '',  # Books - remove emoji, keep text
        '📖': '',  # Book - remove emoji, keep text
        '⚙️': '',  # Gear - remove emoji, keep text
        '🚧': '',  # Construction - remove emoji, keep text
        '📝': '',  # Memo - remove emoji, keep text
        '🌐': '',  # Globe - remove emoji, keep text
    }
    
    # Apply replacements
    changes_made = 0
    for emoji, replacement in emoji_replacements.items():
        if emoji in content:
            old_count = content.count(emoji)
            content = content.replace(emoji, replacement)
            changes_made += old_count
            print(f"  Replaced {old_count} instances of {emoji}")
    
    # Clean up extra spaces that might be left after emoji removal (but preserve line breaks)
    # Only clean up multiple spaces on the same line, not line breaks
    lines = content.split('\n')
    cleaned_lines = []
    for line in lines:
        # Remove multiple spaces but keep single spaces and line structure
        cleaned_line = re.sub(r' +', ' ', line.strip())
        cleaned_lines.append(cleaned_line)
    content = '\n'.join(cleaned_lines)
    
    # Write back if changes were made
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ Updated {file_path} - {changes_made} emoji icons removed")
        return True
    else:
        print(f"✓ {file_path} - No emoji icons found")
        return False

def main():
    """Clean emoji icons from all dashboard files"""
    print("🧹 COMPREHENSIVE EMOJI CLEANUP")
    print("=" * 35)
    
    files_to_clean = [
        'chain_testing_interface.py',
        'rc_testing_tab.py'
    ]
    
    total_files_updated = 0
    
    for file_path in files_to_clean:
        print(f"\nCleaning {file_path}...")
        if clean_emoji_icons(file_path):
            total_files_updated += 1
    
    print(f"\n📊 CLEANUP SUMMARY")
    print("=" * 20)
    print(f"Files processed: {len(files_to_clean)}")
    print(f"Files updated: {total_files_updated}")
    
    if total_files_updated > 0:
        print("\n✅ CLEANUP COMPLETED!")
        print("All emoji icons have been removed or replaced with professional alternatives.")
        print("\nNext steps:")
        print("1. Test the dashboard: streamlit run qa_integrated_dashboard.py")
        print("2. Verify professional appearance")
        print("3. Test all functionality")
    else:
        print("\n✓ All files are already clean!")

if __name__ == "__main__":
    main()
