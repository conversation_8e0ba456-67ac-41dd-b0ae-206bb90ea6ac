"""
RC Testing Dashboard Tab
Comprehensive Release Candidate testing interface with APK upload and automated analysis
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any
from rc_testing_engine import RCTestingEngine, RCTestResult

def render_rc_testing_tab():
    """Render the RC Testing dashboard tab"""
    
    st.markdown("# 📱 RC Testing Dashboard")
    st.markdown("**Comprehensive Release Candidate Testing with APK Analysis**")
    
    # Initialize RC testing engine
    if 'rc_engine' not in st.session_state:
        st.session_state['rc_engine'] = RCTestingEngine()
    
    rc_engine = st.session_state['rc_engine']
    
    # Create sub-tabs for RC testing workflow
    rc_tabs = st.tabs([
        "📤 APK Upload & Analysis",
        "🔒 Security Assessment", 
        "⚡ Performance Metrics",
        "📊 Unified Results",
        "📋 RC Reports"
    ])
    
    # Tab 1: APK Upload & Analysis
    with rc_tabs[0]:
        st.markdown("### 📤 APK Upload & Automated Analysis")
        st.markdown("Upload your Android RC build for comprehensive testing")
        
        # APK Upload Section
        col1, col2 = st.columns([2, 1])
        
        with col1:
            apk_file = st.file_uploader(
                "Upload APK File",
                type=['apk'],
                help="Upload your Android Release Candidate APK file"
            )
        
        with col2:
            if apk_file:
                st.success(f"✅ APK Uploaded: {apk_file.name}")
                st.info(f"📦 Size: {len(apk_file.getvalue()) / (1024*1024):.1f} MB")
        
        # Single-Click Complete Analysis
        if apk_file:
            st.markdown("### 🚀 Complete RC Analysis")
            
            if st.button("🔍 Run Complete RC Analysis", type="primary", use_container_width=True):
                with st.spinner("Performing comprehensive RC analysis..."):
                    
                    # Progress tracking
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # Update progress
                    progress_bar.progress(20)
                    status_text.text("🔍 Extracting APK metadata...")
                    
                    # Perform complete analysis
                    result = rc_engine.perform_complete_rc_analysis(apk_file)
                    
                    progress_bar.progress(100)
                    status_text.text("✅ Analysis complete!")
                    
                    if result:
                        st.session_state['current_rc_result'] = result
                        st.success(f"🎉 RC Analysis completed! Test ID: {result.test_id}")
                        
                        # Quick summary
                        col1, col2, col3, col4 = st.columns(4)
                        
                        with col1:
                            status_color = {"PASS": "🟢", "WARNING": "🟡", "FAIL": "🔴"}
                            st.metric("Test Status", f"{status_color.get(result.test_status, '⚪')} {result.test_status}")
                        
                        with col2:
                            st.metric("Overall Score", f"{result.overall_score:.1f}/100")
                        
                        with col3:
                            st.metric("Security Risk", result.security_analysis.risk_level)
                        
                        with col4:
                            st.metric("Performance Grade", result.performance_metrics.performance_grade)
                        
                        # APK Metadata Display
                        st.markdown("### 📋 APK Metadata")
                        
                        metadata_cols = st.columns(3)
                        
                        with metadata_cols[0]:
                            st.markdown("**Build Information**")
                            st.write(f"• **Package:** {result.apk_metadata.package_name}")
                            st.write(f"• **Version:** {result.apk_metadata.version_name} ({result.apk_metadata.version_code})")
                            st.write(f"• **Size:** {result.apk_metadata.file_size / (1024*1024):.1f} MB")
                            st.write(f"• **Target SDK:** {result.apk_metadata.target_sdk_version}")
                        
                        with metadata_cols[1]:
                            st.markdown("**Components**")
                            st.write(f"• **Activities:** {len(result.apk_metadata.activities)}")
                            st.write(f"• **Services:** {len(result.apk_metadata.services)}")
                            st.write(f"• **Receivers:** {len(result.apk_metadata.receivers)}")
                            st.write(f"• **Permissions:** {len(result.apk_metadata.permissions)}")
                        
                        with metadata_cols[2]:
                            st.markdown("**Security**")
                            st.write(f"• **Certificate:** {result.apk_metadata.signing_certificate.get('issuer', 'Unknown')}")
                            st.write(f"• **File Hash:** {result.apk_metadata.file_hash[:16]}...")
                            st.write(f"• **Build Time:** {result.apk_metadata.build_timestamp[:19]}")
                    
                    else:
                        st.error("❌ RC Analysis failed. Please try again.")
        
        else:
            st.info("👆 Please upload an APK file to begin RC testing")
    
    # Tab 2: Security Assessment
    with rc_tabs[1]:
        st.markdown("### 🔒 Security Assessment Results")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            security = result.security_analysis
            
            # Security Overview
            col1, col2, col3 = st.columns(3)
            
            with col1:
                risk_colors = {"LOW": "green", "MEDIUM": "orange", "HIGH": "red"}
                st.markdown(f"""
                <div style="
                    background: linear-gradient(90deg, {risk_colors.get(security.risk_level, 'gray')} 0%, 
                    {risk_colors.get(security.risk_level, 'gray')}40 100%);
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    margin: 10px 0;
                ">
                    <h3 style="color: white; margin: 0;">Risk Level: {security.risk_level}</h3>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.metric("Vulnerability Score", f"{security.vulnerability_score:.1f}/10")
            
            with col3:
                cert_status = "✅ Valid" if security.certificate_valid else "❌ Invalid"
                st.metric("Certificate", cert_status)
            
            # Permission Risk Analysis
            st.markdown("#### 🔐 Permission Risk Analysis")
            
            if security.permission_risks:
                risk_data = []
                for risk in security.permission_risks:
                    risk_data.append({
                        "Permission": risk['permission'].split('.')[-1],
                        "Risk Level": risk['risk_level'],
                        "Description": risk['description']
                    })
                
                df_risks = pd.DataFrame(risk_data)
                st.dataframe(df_risks, use_container_width=True)
            else:
                st.success("✅ No high-risk permissions detected")
            
            # Compliance Status
            st.markdown("#### 📋 Security Compliance")
            
            compliance_cols = st.columns(3)
            
            for i, (standard, compliant) in enumerate(security.compliance_status.items()):
                with compliance_cols[i % 3]:
                    status_icon = "✅" if compliant else "❌"
                    st.write(f"{status_icon} **{standard}**")
            
            # Malware Scan Results
            st.markdown("#### 🛡️ Malware Scan Results")
            
            malware = security.malware_scan_result
            scan_status_color = "green" if malware['scan_status'] == 'CLEAN' else "red"
            
            st.markdown(f"""
            <div style="
                background-color: {scan_status_color}20;
                border-left: 4px solid {scan_status_color};
                padding: 15px;
                margin: 10px 0;
            ">
                <strong>Scan Status:</strong> {malware['scan_status']}<br>
                <strong>Threats Detected:</strong> {malware['threats_detected']}<br>
                <strong>Scan Engine:</strong> {malware['scan_engine']}<br>
                <strong>Scan Time:</strong> {malware['scan_time'][:19]}
            </div>
            """, unsafe_allow_html=True)
            
            # Security Recommendations
            st.markdown("#### 💡 Security Recommendations")
            
            for i, recommendation in enumerate(security.security_recommendations, 1):
                st.info(f"{i}. {recommendation}")
        
        else:
            st.info("👆 Please run RC analysis first to view security assessment")
    
    # Tab 3: Performance Metrics
    with rc_tabs[2]:
        st.markdown("### ⚡ Performance Analysis Results")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            performance = result.performance_metrics
            
            # Performance Overview
            st.markdown("#### 📊 Performance Overview")
            
            perf_cols = st.columns(4)
            
            with perf_cols[0]:
                st.metric("Performance Grade", performance.performance_grade)
            
            with perf_cols[1]:
                st.metric("App Size", f"{performance.app_size_mb:.1f} MB")
            
            with perf_cols[2]:
                st.metric("Avg Frame Rate", f"{performance.frame_rate_avg:.1f} FPS")
            
            with perf_cols[3]:
                st.metric("Memory Usage", f"{performance.memory_usage_mb:.1f} MB")
            
            # Launch Time Analysis
            st.markdown("#### 🚀 Launch Time Analysis")
            
            launch_data = {
                'Launch Type': ['Cold Start', 'Warm Start', 'Hot Start'],
                'Time (ms)': [
                    performance.launch_time_cold,
                    performance.launch_time_warm,
                    performance.launch_time_hot
                ],
                'Threshold': [3000, 1500, 500]
            }
            
            fig_launch = go.Figure()
            
            fig_launch.add_trace(go.Bar(
                name='Actual Time',
                x=launch_data['Launch Type'],
                y=launch_data['Time (ms)'],
                marker_color=['red' if actual > threshold else 'green' 
                             for actual, threshold in zip(launch_data['Time (ms)'], launch_data['Threshold'])]
            ))
            
            fig_launch.add_trace(go.Scatter(
                name='Threshold',
                x=launch_data['Launch Type'],
                y=launch_data['Threshold'],
                mode='markers+lines',
                line=dict(color='orange', dash='dash'),
                marker=dict(size=8)
            ))
            
            fig_launch.update_layout(
                title="Launch Time Performance",
                yaxis_title="Time (milliseconds)",
                showlegend=True
            )
            
            st.plotly_chart(fig_launch, use_container_width=True)
            
            # App Size Comparison
            st.markdown("#### 📦 App Size Analysis")
            
            size_comparison = performance.size_comparison
            
            size_cols = st.columns(2)
            
            with size_cols[0]:
                st.metric(
                    "Size Change",
                    f"{size_comparison['size_increase_mb']:+.1f} MB",
                    f"{size_comparison['size_increase_percent']:+.1f}%"
                )
                
                st.write(f"**Current:** {size_comparison['current_size_mb']:.1f} MB")
                st.write(f"**Previous:** {size_comparison['previous_size_mb']:.1f} MB")
                st.write(f"**Category:** {size_comparison['size_category']}")
            
            with size_cols[1]:
                # Size trend chart
                if 'size_trend' in size_comparison:
                    trend_data = size_comparison['size_trend']
                    trend_data.append({
                        'version': result.apk_metadata.version_name,
                        'size_mb': size_comparison['current_size_mb']
                    })
                    
                    df_trend = pd.DataFrame(trend_data)
                    
                    fig_size = px.line(
                        df_trend, 
                        x='version', 
                        y='size_mb',
                        title='App Size Trend',
                        markers=True
                    )
                    
                    fig_size.update_layout(
                        yaxis_title="Size (MB)",
                        xaxis_title="Version"
                    )
                    
                    st.plotly_chart(fig_size, use_container_width=True)
            
            # Frame Rate & Jank Analysis
            st.markdown("#### 🎮 Frame Rate & Jank Analysis")
            
            frame_cols = st.columns(3)
            
            with frame_cols[0]:
                st.metric("Average FPS", f"{performance.frame_rate_avg:.1f}")
                st.metric("Dropped Frames", performance.dropped_frames)
            
            with frame_cols[1]:
                st.metric("Jank Score", f"{performance.jank_score:.3f}")
                st.metric("UI Thread Blocking", f"{performance.ui_thread_blocking:.1f}%")
            
            with frame_cols[2]:
                # Frame rate gauge
                fig_fps = go.Figure(go.Indicator(
                    mode = "gauge+number",
                    value = performance.frame_rate_avg,
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "Frame Rate (FPS)"},
                    gauge = {
                        'axis': {'range': [None, 60]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 30], 'color': "lightgray"},
                            {'range': [30, 55], 'color': "yellow"},
                            {'range': [55, 60], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 55
                        }
                    }
                ))
                
                fig_fps.update_layout(height=300)
                st.plotly_chart(fig_fps, use_container_width=True)
        
        else:
            st.info("👆 Please run RC analysis first to view performance metrics")
