"""
RC Testing Dashboard Tab
Comprehensive Release Candidate testing interface with APK upload and automated analysis
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any
from rc_testing_engine import RCTestingE<PERSON><PERSON>, RCTestResult

def render_rc_testing_tab():
    """Render the RC Testing dashboard tab"""

    # Enhanced professional header with animations
    st.markdown("""
    <style>
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .rc-header {
        animation: fadeInUp 0.8s ease-out;
    }

    .rc-icon {
        animation: pulse 2s infinite;
    }

    .metric-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .progress-bar {
        background: linear-gradient(90deg, #1e3c72, #2a5298, #1e3c72);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }
    </style>

    <div class="rc-header" style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #1e3c72 100%);
        padding: 2.5rem;
        border-radius: 16px;
        margin-bottom: 2rem;
        box-shadow: 0 12px 40px rgba(30, 60, 114, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
    ">
        <div style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');
            opacity: 0.3;
        "></div>
        <div style="display: flex; align-items: center; gap: 2rem; position: relative; z-index: 1;">
            <div class="rc-icon" style="
                background: rgba(255, 255, 255, 0.2);
                padding: 1.5rem;
                border-radius: 20px;
                backdrop-filter: blur(15px);
                border: 2px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            ">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="white">
                    <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21C5,22.11 5.89,23 7,23H17C18.11,23 19,22.11 19,21V3C19,1.89 18.11,1 17,1Z"/>
                    <circle cx="12" cy="12" r="2" fill="white" opacity="0.8"/>
                </svg>
            </div>
            <div>
                <h1 style="
                    color: white;
                    margin: 0;
                    font-size: 3.2rem;
                    font-weight: 900;
                    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    background: linear-gradient(45deg, #ffffff, #e3f2fd);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                ">
                    Release Candidate Testing
                </h1>
                <p style="
                    color: rgba(255, 255, 255, 0.95);
                    margin: 1rem 0 0 0;
                    font-size: 1.3rem;
                    font-weight: 500;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
                ">
                    🚀 Enterprise-grade APK analysis with AI-powered insights
                </p>
                <div style="
                    display: flex;
                    gap: 1rem;
                    margin-top: 1rem;
                    flex-wrap: wrap;
                ">
                    <span style="
                        background: rgba(255,255,255,0.2);
                        padding: 0.5rem 1rem;
                        border-radius: 20px;
                        font-size: 0.9rem;
                        color: white;
                        backdrop-filter: blur(10px);
                    ">🔒 Security Analysis</span>
                    <span style="
                        background: rgba(255,255,255,0.2);
                        padding: 0.5rem 1rem;
                        border-radius: 20px;
                        font-size: 0.9rem;
                        color: white;
                        backdrop-filter: blur(10px);
                    ">⚡ Performance Testing</span>
                    <span style="
                        background: rgba(255,255,255,0.2);
                        padding: 0.5rem 1rem;
                        border-radius: 20px;
                        font-size: 0.9rem;
                        color: white;
                        backdrop-filter: blur(10px);
                    ">📊 Automated Reports</span>
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize RC testing engine
    if 'rc_engine' not in st.session_state:
        st.session_state['rc_engine'] = RCTestingEngine()
    
    rc_engine = st.session_state['rc_engine']
    
    # Enhanced professional sub-tabs with advanced styling
    st.markdown("""
    <style>
    .stTabs [data-baseweb="tab-list"] {
        gap: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 12px;
        border-radius: 16px;
        border: 2px solid #dee2e6;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
    }
    .stTabs [data-baseweb="tab"] {
        height: 60px;
        padding: 0px 32px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 12px;
        border: 2px solid #e9ecef;
        color: #495057;
        font-weight: 700;
        font-size: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    .stTabs [data-baseweb="tab"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        border-color: #667eea;
    }
    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px solid #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        transform: translateY(-3px);
    }
    .stTabs [aria-selected="true"]::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }
    </style>
    """, unsafe_allow_html=True)

    # Real-time status indicator
    st.markdown("""
    <div style="
        display: flex;
        align-items: center;
        gap: 1rem;
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        border-left: 4px solid #28a745;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
    ">
        <div style="
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        "></div>
        <span style="color: #155724; font-weight: 600;">
            🟢 RC Testing Engine Online - Ready for Analysis
        </span>
    </div>
    """, unsafe_allow_html=True)

    rc_tabs = st.tabs([
        "Upload & Analysis",
        "Security Assessment",
        "Performance Metrics",
        "Unified Results",
        "Reports & Export"
    ])
    
    # Tab 1: Enhanced APK Upload & Analysis
    with rc_tabs[0]:
        # Enhanced section header
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            text-align: center;
        ">
            <h2 style="color: white; margin: 0; font-size: 2.2rem; font-weight: 800;">
                🚀 APK Upload & Analysis
            </h2>
            <p style="color: rgba(255,255,255,0.9); margin: 0.5rem 0 0 0; font-size: 1.1rem;">
                Upload your Android Release Candidate for comprehensive automated testing
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced upload interface
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                padding: 2rem;
                border-radius: 16px;
                border: 3px dashed #667eea;
                text-align: center;
                margin: 1rem 0;
                box-shadow: 0 4px 20px rgba(0,0,0,0.05);
                transition: all 0.3s ease;
            " onmouseover="this.style.borderColor='#764ba2'; this.style.transform='scale(1.02)'"
               onmouseout="this.style.borderColor='#667eea'; this.style.transform='scale(1)'">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1.5rem auto;
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                ">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                </div>
                <h3 style="color: #495057; margin: 0.5rem 0; font-weight: 700;">Upload APK File</h3>
                <p style="color: #6c757d; margin: 0; font-size: 1rem;">
                    Drag and drop your Android Release Candidate or click to browse
                </p>
                <div style="
                    margin-top: 1rem;
                    padding: 0.5rem 1rem;
                    background: rgba(102, 126, 234, 0.1);
                    border-radius: 20px;
                    display: inline-block;
                ">
                    <small style="color: #667eea; font-weight: 600;">
                        📱 Supports: .apk files up to 200MB
                    </small>
                </div>
            </div>
            """, unsafe_allow_html=True)

            apk_file = st.file_uploader(
                "Choose APK File",
                type=['apk'],
                help="Upload your Android Release Candidate APK file",
                label_visibility="collapsed"
            )

        with col2:
            # Upload guidelines
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
                padding: 1.5rem;
                border-radius: 12px;
                border-left: 4px solid #667eea;
                height: fit-content;
            ">
                <h4 style="color: #1565c0; margin: 0 0 1rem 0; font-weight: 700;">
                    📋 Upload Guidelines
                </h4>
                <ul style="color: #424242; margin: 0; padding-left: 1.2rem; line-height: 1.8;">
                    <li><strong>File Format:</strong> .apk only</li>
                    <li><strong>Max Size:</strong> 200MB</li>
                    <li><strong>Build Type:</strong> Release Candidate</li>
                    <li><strong>Signing:</strong> Production signed preferred</li>
                    <li><strong>Testing:</strong> Automated analysis included</li>
                </ul>
                <div style="
                    margin-top: 1rem;
                    padding: 0.8rem;
                    background: rgba(102, 126, 234, 0.1);
                    border-radius: 8px;
                    text-align: center;
                ">
                    <small style="color: #667eea; font-weight: 600;">
                        🔒 Your APK is processed securely and not stored permanently
                    </small>
                </div>
            </div>
            """, unsafe_allow_html=True)

        if apk_file:
            # Enhanced file info display with animations
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                padding: 1.5rem;
                border-radius: 12px;
                border: 2px solid #28a745;
                margin: 1.5rem 0;
                animation: fadeInUp 0.6s ease-out;
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
            ">
                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                    <div style="
                        background: #28a745;
                        color: white;
                        padding: 0.8rem;
                        border-radius: 50%;
                        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                    ">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                        </svg>
                    </div>
                    <h3 style="color: #155724; margin: 0; font-weight: 700;">
                        🎉 APK Upload Successful!
                    </h3>
                </div>
                <p style="color: #155724; margin: 0; font-size: 1rem;">
                    Your APK file has been uploaded and is ready for comprehensive analysis.
                </p>
            </div>
            """, unsafe_allow_html=True)

            # Enhanced file details
            file_size_mb = len(apk_file.getvalue()) / (1024*1024)

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.markdown(f"""
                <div class="metric-card" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                ">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📄</div>
                    <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">File Name</div>
                    <div style="font-weight: 700; font-size: 1rem;">{apk_file.name[:20]}{'...' if len(apk_file.name) > 20 else ''}</div>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown(f"""
                <div class="metric-card" style="
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                ">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📦</div>
                    <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">File Size</div>
                    <div style="font-weight: 700; font-size: 1.2rem;">{file_size_mb:.1f} MB</div>
                </div>
                """, unsafe_allow_html=True)

            with col3:
                status = "Optimal" if file_size_mb < 50 else "Large" if file_size_mb < 100 else "Very Large"
                status_color = "#28a745" if file_size_mb < 50 else "#ffc107" if file_size_mb < 100 else "#dc3545"
                st.markdown(f"""
                <div class="metric-card" style="
                    background: linear-gradient(135deg, {status_color} 0%, {status_color}dd 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                    <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Size Status</div>
                    <div style="font-weight: 700; font-size: 1.1rem;">{status}</div>
                </div>
                """, unsafe_allow_html=True)

            with col4:
                st.markdown(f"""
                <div class="metric-card" style="
                    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
                ">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                    <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Status</div>
                    <div style="font-weight: 700; font-size: 1.1rem;">Ready</div>
                </div>
                """, unsafe_allow_html=True)
        
        # Enhanced Analysis Section
        if apk_file:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                padding: 2.5rem;
                border-radius: 16px;
                margin: 2rem 0;
                text-align: center;
                box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                    animation: rotate 20s linear infinite;
                "></div>
                <div style="position: relative; z-index: 1;">
                    <div style="
                        background: rgba(255, 255, 255, 0.2);
                        width: 100px;
                        height: 100px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 1.5rem auto;
                        backdrop-filter: blur(10px);
                        border: 3px solid rgba(255, 255, 255, 0.3);
                        animation: pulse 2s infinite;
                    ">
                        <svg width="50" height="50" viewBox="0 0 24 24" fill="white">
                            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                        </svg>
                    </div>
                    <h2 style="color: white; margin: 0 0 1rem 0; font-size: 2.5rem; font-weight: 900;">
                        🚀 Comprehensive RC Analysis
                    </h2>
                    <p style="color: rgba(255,255,255,0.95); margin: 0 0 2rem 0; font-size: 1.2rem; line-height: 1.6;">
                        Execute complete automated testing suite with AI-powered insights<br>
                        <strong>Security • Performance • Compliance • Quality Gates</strong>
                    </p>
                    <div style="
                        display: flex;
                        justify-content: center;
                        gap: 2rem;
                        margin-top: 1.5rem;
                        flex-wrap: wrap;
                    ">
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🛡️</div>
                            <div style="color: white; font-weight: 600;">Security Scan</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">⚡</div>
                            <div style="color: white; font-weight: 600;">Performance Test</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                            <div style="color: white; font-weight: 600;">Quality Analysis</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
                            <div style="color: white; font-weight: 600;">Report Generation</div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            </style>
            """, unsafe_allow_html=True)

            # Enhanced analysis button
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                if st.button("▶️ Execute Complete Analysis", type="primary", use_container_width=True):
                    # Enhanced progress tracking with animations
                    progress_container = st.empty()

                    with progress_container.container():
                        st.markdown("""
                    <div style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        padding: 2rem;
                        border-radius: 16px;
                        margin: 2rem 0;
                        text-align: center;
                        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
                    ">
                        <h3 style="color: white; margin: 0 0 1rem 0; font-weight: 700;">
                            🔄 Performing Comprehensive RC Analysis
                        </h3>
                        <div style="
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 20px;
                            padding: 1rem;
                            margin: 1rem 0;
                        ">
                            <div class="progress-bar" style="
                                height: 8px;
                                border-radius: 4px;
                                margin-bottom: 1rem;
                            "></div>
                            <p style="color: white; margin: 0; font-weight: 600;">
                                🔍 Extracting APK metadata and performing security analysis...
                            </p>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                    # Simulate analysis steps with progress
                    import time

                    steps = [
                    ("🔍 Extracting APK metadata...", 20),
                    ("🛡️ Performing security analysis...", 40),
                    ("⚡ Analyzing performance metrics...", 60),
                    ("📊 Generating quality reports...", 80),
                    ("✅ Finalizing analysis...", 100)
                    ]

                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    for step_text, progress in steps:
                        status_text.markdown(f"""
                        <div style="
                            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
                            padding: 1rem;
                            border-radius: 8px;
                            border-left: 4px solid #667eea;
                            margin: 0.5rem 0;
                            text-align: center;
                        ">
                            <span style="color: #1565c0; font-weight: 600;">{step_text}</span>
                        </div>
                        """, unsafe_allow_html=True)
                        progress_bar.progress(progress)
                        time.sleep(0.8)  # Simulate processing time

                    # Perform complete analysis
                    result = rc_engine.perform_complete_rc_analysis(apk_file)

                    # Clear progress and show completion
                    progress_container.empty()
                    status_text.empty()
                    
                    if result:
                        st.session_state['current_rc_result'] = result

                        # Enhanced completion notification
                        st.markdown("""
                        <div style="
                            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
                            color: white;
                            padding: 2rem;
                            border-radius: 16px;
                            margin: 2rem 0;
                            text-align: center;
                            box-shadow: 0 8px 32px rgba(0, 200, 81, 0.4);
                            animation: fadeInUp 0.8s ease-out;
                        ">
                            <div style="
                                background: rgba(255, 255, 255, 0.2);
                                width: 80px;
                                height: 80px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 1rem auto;
                                animation: pulse 2s infinite;
                            ">
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="white">
                                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                                </svg>
                            </div>
                            <h2 style="margin: 0 0 0.5rem 0; font-weight: 800;">🎉 Analysis Complete!</h2>
                            <p style="margin: 0; font-size: 1.1rem; opacity: 0.95;">
                                Test ID: <strong>{}</strong>
                            </p>
                        </div>
                        """.format(result.test_id), unsafe_allow_html=True)

                        # Enhanced executive dashboard
                        st.markdown("### 📊 Executive Dashboard")

                        # Status-based styling
                        status_colors = {
                            "PASS": {"bg": "#28a745", "icon": "🟢", "text": "Approved for Release"},
                            "WARNING": {"bg": "#ffc107", "icon": "🟡", "text": "Review Required"},
                            "FAIL": {"bg": "#dc3545", "icon": "🔴", "text": "Release Blocked"}
                        }

                        status_info = status_colors.get(result.test_status, {"bg": "#6c757d", "icon": "⚪", "text": "Unknown"})

                        # Main status card
                        st.markdown(f"""
                        <div style="
                            background: linear-gradient(135deg, {status_info['bg']} 0%, {status_info['bg']}dd 100%);
                            color: white;
                            padding: 2rem;
                            border-radius: 16px;
                            margin: 1.5rem 0;
                            text-align: center;
                            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                        ">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">{status_info['icon']}</div>
                            <h2 style="margin: 0 0 0.5rem 0; font-weight: 800;">{result.test_status}</h2>
                            <p style="margin: 0; font-size: 1.2rem; opacity: 0.95;">{status_info['text']}</p>
                            <div style="
                                margin-top: 1.5rem;
                                padding: 1rem;
                                background: rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                backdrop-filter: blur(10px);
                            ">
                                <div style="font-size: 2.5rem; font-weight: 900; margin-bottom: 0.5rem;">
                                    {result.overall_score:.1f}/100
                                </div>
                                <div style="font-size: 1rem; opacity: 0.9;">Overall Quality Score</div>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                        # Enhanced metrics grid
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            risk_colors = {"LOW": "#28a745", "MEDIUM": "#ffc107", "HIGH": "#dc3545"}
                            risk_color = risk_colors.get(result.security_analysis.risk_level, "#6c757d")
                            st.markdown(f"""
                            <div class="metric-card" style="
                                background: linear-gradient(135deg, {risk_color} 0%, {risk_color}dd 100%);
                                color: white;
                                padding: 2rem;
                                border-radius: 16px;
                                text-align: center;
                                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                                height: 200px;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                            ">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🛡️</div>
                                <div style="font-size: 1.8rem; font-weight: 800; margin-bottom: 0.5rem;">
                                    {result.security_analysis.risk_level}
                                </div>
                                <div style="font-size: 1rem; opacity: 0.9;">Security Risk Level</div>
                                <div style="
                                    margin-top: 1rem;
                                    font-size: 0.9rem;
                                    background: rgba(255,255,255,0.2);
                                    padding: 0.5rem;
                                    border-radius: 8px;
                                ">
                                    Score: {result.security_analysis.vulnerability_score:.1f}/10
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                        with col2:
                            grade_colors = {"A+": "#28a745", "A": "#28a745", "B": "#20c997", "C": "#ffc107", "D": "#dc3545"}
                            grade_color = grade_colors.get(result.performance_metrics.performance_grade, "#6c757d")
                            st.markdown(f"""
                            <div class="metric-card" style="
                                background: linear-gradient(135deg, {grade_color} 0%, {grade_color}dd 100%);
                                color: white;
                                padding: 2rem;
                                border-radius: 16px;
                                text-align: center;
                                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                                height: 200px;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                            ">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">⚡</div>
                                <div style="font-size: 1.8rem; font-weight: 800; margin-bottom: 0.5rem;">
                                    {result.performance_metrics.performance_grade}
                                </div>
                                <div style="font-size: 1rem; opacity: 0.9;">Performance Grade</div>
                                <div style="
                                    margin-top: 1rem;
                                    font-size: 0.9rem;
                                    background: rgba(255,255,255,0.2);
                                    padding: 0.5rem;
                                    border-radius: 8px;
                                ">
                                    Launch: {result.performance_metrics.launch_time_cold:.0f}ms
                                </div>
                            </div>
                            """, unsafe_allow_html=True)

                        with col3:
                            size_mb = result.performance_metrics.app_size_mb
                            size_color = "#28a745" if size_mb < 50 else "#ffc107" if size_mb < 100 else "#dc3545"
                            st.markdown(f"""
                            <div class="metric-card" style="
                                background: linear-gradient(135deg, {size_color} 0%, {size_color}dd 100%);
                                color: white;
                                padding: 2rem;
                                border-radius: 16px;
                                text-align: center;
                                box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                                height: 200px;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                            ">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📦</div>
                                <div style="font-size: 1.8rem; font-weight: 800; margin-bottom: 0.5rem;">
                                    {size_mb:.1f} MB
                                </div>
                                <div style="font-size: 1rem; opacity: 0.9;">App Size</div>
                                <div style="
                                    margin-top: 1rem;
                                    font-size: 0.9rem;
                                    background: rgba(255,255,255,0.2);
                                    padding: 0.5rem;
                                    border-radius: 8px;
                                ">
                                    FPS: {result.performance_metrics.frame_rate_avg:.1f}
                                </div>
                            </div>
                            """, unsafe_allow_html=True)
                        
                        # APK Metadata Display
                        st.markdown("#### 📋 APK Metadata")

                        metadata_cols = st.columns(3)

                        with metadata_cols[0]:
                            st.markdown("**Build Information**")
                            st.write(f"• **Package:** {result.apk_metadata.package_name}")
                            st.write(f"• **Version:** {result.apk_metadata.version_name} ({result.apk_metadata.version_code})")
                            st.write(f"• **Size:** {result.apk_metadata.file_size / (1024*1024):.1f} MB")
                            st.write(f"• **Target SDK:** {result.apk_metadata.target_sdk_version}")

                        with metadata_cols[1]:
                            st.markdown("**Components**")
                            st.write(f"• **Activities:** {len(result.apk_metadata.activities)}")
                            st.write(f"• **Services:** {len(result.apk_metadata.services)}")
                            st.write(f"• **Receivers:** {len(result.apk_metadata.receivers)}")
                            st.write(f"• **Permissions:** {len(result.apk_metadata.permissions)}")

                        with metadata_cols[2]:
                            st.markdown("**Security**")
                            st.write(f"• **Certificate:** {result.apk_metadata.signing_certificate.get('issuer', 'Unknown')}")
                            st.write(f"• **File Hash:** {result.apk_metadata.file_hash[:16]}...")
                            st.write(f"• **Build Time:** {result.apk_metadata.build_timestamp[:19]}")
                    
                    else:
                        st.error("❌ RC Analysis failed. Please try again.")
        
        else:
            st.info("👆 Please upload an APK file to begin RC testing")
    
    # Tab 2: Security Assessment
    with rc_tabs[1]:
        st.markdown("## 🔒 Security Assessment")
        st.markdown("Comprehensive vulnerability analysis and compliance validation")

        st.markdown("---")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            security = result.security_analysis
            
            # Security Overview
            col1, col2, col3 = st.columns(3)
            
            with col1:
                risk_colors = {"LOW": "green", "MEDIUM": "orange", "HIGH": "red"}
                st.markdown(f"""
                <div style="
                    background: linear-gradient(90deg, {risk_colors.get(security.risk_level, 'gray')} 0%, 
                    {risk_colors.get(security.risk_level, 'gray')}40 100%);
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    margin: 10px 0;
                ">
                    <h3 style="color: white; margin: 0;">Risk Level: {security.risk_level}</h3>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.metric("Vulnerability Score", f"{security.vulnerability_score:.1f}/10")
            
            with col3:
                cert_status = "✅ Valid" if security.certificate_valid else "❌ Invalid"
                st.metric("Certificate", cert_status)
            
            # Permission Risk Analysis
            st.markdown("#### 🔐 Permission Risk Analysis")
            
            if security.permission_risks:
                risk_data = []
                for risk in security.permission_risks:
                    risk_data.append({
                        "Permission": risk['permission'].split('.')[-1],
                        "Risk Level": risk['risk_level'],
                        "Description": risk['description']
                    })
                
                df_risks = pd.DataFrame(risk_data)
                st.dataframe(df_risks, use_container_width=True)
            else:
                st.success("✅ No high-risk permissions detected")
            
            # Compliance Status
            st.markdown("#### 📋 Security Compliance")
            
            compliance_cols = st.columns(3)
            
            for i, (standard, compliant) in enumerate(security.compliance_status.items()):
                with compliance_cols[i % 3]:
                    status_icon = "✅" if compliant else "❌"
                    st.write(f"{status_icon} **{standard}**")
            
            # Malware Scan Results
            st.markdown("#### 🛡️ Malware Scan Results")
            
            malware = security.malware_scan_result
            scan_status_color = "green" if malware['scan_status'] == 'CLEAN' else "red"
            
            st.markdown(f"""
            <div style="
                background-color: {scan_status_color}20;
                border-left: 4px solid {scan_status_color};
                padding: 15px;
                margin: 10px 0;
            ">
                <strong>Scan Status:</strong> {malware['scan_status']}<br>
                <strong>Threats Detected:</strong> {malware['threats_detected']}<br>
                <strong>Scan Engine:</strong> {malware['scan_engine']}<br>
                <strong>Scan Time:</strong> {malware['scan_time'][:19]}
            </div>
            """, unsafe_allow_html=True)
            
            # Security Recommendations
            st.markdown("#### 💡 Security Recommendations")
            
            for i, recommendation in enumerate(security.security_recommendations, 1):
                st.info(f"{i}. {recommendation}")
        
        else:
            st.info("👆 Please run RC analysis first to view security assessment")
    
    # Tab 3: Performance Metrics
    with rc_tabs[2]:
        st.markdown("## ⚡ Performance Analysis")
        st.markdown("Launch time, frame rate, and resource optimization metrics")

        st.markdown("---")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            performance = result.performance_metrics
            
            # Performance Overview
            st.markdown("#### 📊 Performance Overview")
            
            perf_cols = st.columns(4)
            
            with perf_cols[0]:
                st.metric("Performance Grade", performance.performance_grade)
            
            with perf_cols[1]:
                st.metric("App Size", f"{performance.app_size_mb:.1f} MB")
            
            with perf_cols[2]:
                st.metric("Avg Frame Rate", f"{performance.frame_rate_avg:.1f} FPS")
            
            with perf_cols[3]:
                st.metric("Memory Usage", f"{performance.memory_usage_mb:.1f} MB")
            
            # Launch Time Analysis
            st.markdown("#### 🚀 Launch Time Analysis")
            
            launch_data = {
                'Launch Type': ['Cold Start', 'Warm Start', 'Hot Start'],
                'Time (ms)': [
                    performance.launch_time_cold,
                    performance.launch_time_warm,
                    performance.launch_time_hot
                ],
                'Threshold': [3000, 1500, 500]
            }
            
            fig_launch = go.Figure()
            
            fig_launch.add_trace(go.Bar(
                name='Actual Time',
                x=launch_data['Launch Type'],
                y=launch_data['Time (ms)'],
                marker_color=['red' if actual > threshold else 'green' 
                             for actual, threshold in zip(launch_data['Time (ms)'], launch_data['Threshold'])]
            ))
            
            fig_launch.add_trace(go.Scatter(
                name='Threshold',
                x=launch_data['Launch Type'],
                y=launch_data['Threshold'],
                mode='markers+lines',
                line=dict(color='orange', dash='dash'),
                marker=dict(size=8)
            ))
            
            fig_launch.update_layout(
                title="Launch Time Performance",
                yaxis_title="Time (milliseconds)",
                showlegend=True
            )
            
            st.plotly_chart(fig_launch, use_container_width=True)
            
            # App Size Comparison
            st.markdown("#### 📦 App Size Analysis")
            
            size_comparison = performance.size_comparison
            
            size_cols = st.columns(2)
            
            with size_cols[0]:
                st.metric(
                    "Size Change",
                    f"{size_comparison['size_increase_mb']:+.1f} MB",
                    f"{size_comparison['size_increase_percent']:+.1f}%"
                )
                
                st.write(f"**Current:** {size_comparison['current_size_mb']:.1f} MB")
                st.write(f"**Previous:** {size_comparison['previous_size_mb']:.1f} MB")
                st.write(f"**Category:** {size_comparison['size_category']}")
            
            with size_cols[1]:
                # Size trend chart
                if 'size_trend' in size_comparison:
                    trend_data = size_comparison['size_trend']
                    trend_data.append({
                        'version': result.apk_metadata.version_name,
                        'size_mb': size_comparison['current_size_mb']
                    })
                    
                    df_trend = pd.DataFrame(trend_data)
                    
                    fig_size = px.line(
                        df_trend, 
                        x='version', 
                        y='size_mb',
                        title='App Size Trend',
                        markers=True
                    )
                    
                    fig_size.update_layout(
                        yaxis_title="Size (MB)",
                        xaxis_title="Version"
                    )
                    
                    st.plotly_chart(fig_size, use_container_width=True)
            
            # Frame Rate & Jank Analysis
            st.markdown("#### 🎮 Frame Rate & Jank Analysis")
            
            frame_cols = st.columns(3)
            
            with frame_cols[0]:
                st.metric("Average FPS", f"{performance.frame_rate_avg:.1f}")
                st.metric("Dropped Frames", performance.dropped_frames)
            
            with frame_cols[1]:
                st.metric("Jank Score", f"{performance.jank_score:.3f}")
                st.metric("UI Thread Blocking", f"{performance.ui_thread_blocking:.1f}%")
            
            with frame_cols[2]:
                # Frame rate gauge
                fig_fps = go.Figure(go.Indicator(
                    mode = "gauge+number",
                    value = performance.frame_rate_avg,
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "Frame Rate (FPS)"},
                    gauge = {
                        'axis': {'range': [None, 60]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 30], 'color': "lightgray"},
                            {'range': [30, 55], 'color': "yellow"},
                            {'range': [55, 60], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 55
                        }
                    }
                ))
                
                fig_fps.update_layout(height=300)
                st.plotly_chart(fig_fps, use_container_width=True)
        
        else:
            st.info("👆 Please run RC analysis first to view performance metrics")

    # Tab 4: Unified Results
    with rc_tabs[3]:
        st.markdown("### 📊 Unified RC Testing Results")

        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']

            # Overall Status Dashboard
            st.markdown("#### 🎯 RC Approval Status")

            status_colors = {"PASS": "green", "WARNING": "orange", "FAIL": "red"}
            status_color = status_colors.get(result.test_status, "gray")

            st.markdown(f"""
            <div style="
                background: linear-gradient(90deg, {status_color} 0%, {status_color}40 100%);
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin: 20px 0;
            ">
                <h1 style="color: white; margin: 0;">RC Status: {result.test_status}</h1>
                <h3 style="color: white; margin: 10px 0;">Overall Score: {result.overall_score:.1f}/100</h3>
            </div>
            """, unsafe_allow_html=True)

            # Comprehensive Metrics Dashboard
            st.markdown("#### 📈 Comprehensive Metrics")

            # Create metrics summary
            metrics_data = {
                'Category': ['Security', 'Performance', 'App Size', 'Launch Time', 'Frame Rate', 'Memory'],
                'Score': [
                    max(0, (10 - result.security_analysis.vulnerability_score) / 10 * 100),
                    {"A+": 100, "A": 90, "B": 80, "C": 70, "D": 60}.get(result.performance_metrics.performance_grade, 50),
                    100 if result.performance_metrics.app_size_mb < 50 else 80 if result.performance_metrics.app_size_mb < 100 else 60,
                    100 if result.performance_metrics.launch_time_cold < 2000 else 80 if result.performance_metrics.launch_time_cold < 3000 else 60,
                    (result.performance_metrics.frame_rate_avg / 60) * 100,
                    100 if result.performance_metrics.memory_usage_mb < 100 else 80 if result.performance_metrics.memory_usage_mb < 150 else 60
                ],
                'Status': []
            }

            # Determine status for each metric
            for score in metrics_data['Score']:
                if score >= 85:
                    metrics_data['Status'].append('✅ Pass')
                elif score >= 70:
                    metrics_data['Status'].append('⚠️ Warning')
                else:
                    metrics_data['Status'].append('❌ Fail')

            # Display metrics table
            df_metrics = pd.DataFrame(metrics_data)
            st.dataframe(df_metrics, use_container_width=True)

            # Radar Chart for Overall Performance
            st.markdown("#### 🕸️ Performance Radar")

            fig_radar = go.Figure()

            fig_radar.add_trace(go.Scatterpolar(
                r=metrics_data['Score'],
                theta=metrics_data['Category'],
                fill='toself',
                name='RC Performance'
            ))

            fig_radar.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="RC Performance Overview"
            )

            st.plotly_chart(fig_radar, use_container_width=True)

            # Key Recommendations
            st.markdown("#### 💡 Key Recommendations")

            for i, recommendation in enumerate(result.recommendations, 1):
                priority = "🔴 High" if i <= 2 else "🟡 Medium" if i <= 4 else "🟢 Low"
                st.markdown(f"**{priority} Priority:** {recommendation}")

            # Test Summary
            st.markdown("#### 📋 Test Summary")

            summary_cols = st.columns(2)

            with summary_cols[0]:
                st.markdown("**Test Information**")
                st.write(f"• **Test ID:** {result.test_id}")
                st.write(f"• **Test Date:** {result.test_timestamp[:19]}")
                st.write(f"• **Duration:** {result.test_duration:.1f} seconds")
                st.write(f"• **APK Version:** {result.apk_metadata.version_name}")

            with summary_cols[1]:
                st.markdown("**Quality Gates**")

                # Define quality gates
                quality_gates = [
                    ("Security Risk", result.security_analysis.risk_level != "HIGH"),
                    ("Performance Grade", result.performance_metrics.performance_grade in ["A+", "A", "B"]),
                    ("App Size", result.performance_metrics.app_size_mb < 100),
                    ("Launch Time", result.performance_metrics.launch_time_cold < 3000),
                    ("Frame Rate", result.performance_metrics.frame_rate_avg > 55)
                ]

                for gate_name, passed in quality_gates:
                    status_icon = "✅" if passed else "❌"
                    st.write(f"• {status_icon} {gate_name}")

        else:
            st.info("👆 Please run RC analysis first to view unified results")

    # Tab 5: RC Reports
    with rc_tabs[4]:
        st.markdown("### 📋 RC Testing Reports")

        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']

            # Report Generation Options
            st.markdown("#### 📄 Generate RC Reports")

            report_cols = st.columns(3)

            with report_cols[0]:
                if st.button("📊 Executive Summary", use_container_width=True):
                    executive_report = generate_executive_summary(result)
                    st.download_button(
                        label="💾 Download Executive Summary",
                        data=executive_report,
                        file_name=f"RC_Executive_Summary_{result.test_id}.json",
                        mime="application/json"
                    )

            with report_cols[1]:
                if st.button("🔍 Detailed Technical Report", use_container_width=True):
                    technical_report = rc_engine.export_test_result(result.test_id, "json")
                    st.download_button(
                        label="💾 Download Technical Report",
                        data=technical_report,
                        file_name=f"RC_Technical_Report_{result.test_id}.json",
                        mime="application/json"
                    )

            with report_cols[2]:
                if st.button("📈 Performance Report", use_container_width=True):
                    performance_report = generate_performance_report(result)
                    st.download_button(
                        label="💾 Download Performance Report",
                        data=performance_report,
                        file_name=f"RC_Performance_Report_{result.test_id}.json",
                        mime="application/json"
                    )

            # Report Preview
            st.markdown("#### 👀 Report Preview")

            report_type = st.selectbox(
                "Select report type to preview:",
                ["Executive Summary", "Security Analysis", "Performance Metrics", "Complete Report"]
            )

            if report_type == "Executive Summary":
                display_executive_summary(result)
            elif report_type == "Security Analysis":
                display_security_summary(result)
            elif report_type == "Performance Metrics":
                display_performance_summary(result)
            else:
                display_complete_report(result)

            # Historical Results
            st.markdown("#### 📊 Historical RC Results")

            if len(rc_engine.test_results) > 1:
                # Create historical comparison
                historical_data = []
                for test_id, test_result in rc_engine.test_results.items():
                    historical_data.append({
                        'Test ID': test_id,
                        'Date': test_result.test_timestamp[:10],
                        'Version': test_result.apk_metadata.version_name,
                        'Status': test_result.test_status,
                        'Overall Score': test_result.overall_score,
                        'Security Risk': test_result.security_analysis.risk_level,
                        'Performance Grade': test_result.performance_metrics.performance_grade
                    })

                df_historical = pd.DataFrame(historical_data)
                st.dataframe(df_historical, use_container_width=True)

                # Trend Analysis
                if len(historical_data) > 1:
                    fig_trend = px.line(
                        df_historical,
                        x='Date',
                        y='Overall Score',
                        title='RC Quality Trend',
                        markers=True
                    )
                    st.plotly_chart(fig_trend, use_container_width=True)
            else:
                st.info("Run multiple RC tests to see historical trends")

        else:
            st.info("👆 Please run RC analysis first to generate reports")

def generate_executive_summary(result: RCTestResult) -> str:
    """Generate executive summary report"""
    summary = {
        "rc_approval_status": result.test_status,
        "overall_score": result.overall_score,
        "test_date": result.test_timestamp,
        "app_version": result.apk_metadata.version_name,
        "key_findings": {
            "security_risk_level": result.security_analysis.risk_level,
            "performance_grade": result.performance_metrics.performance_grade,
            "app_size_mb": result.performance_metrics.app_size_mb,
            "launch_time_ms": result.performance_metrics.launch_time_cold
        },
        "recommendations": result.recommendations[:5],
        "quality_gates": {
            "security_passed": result.security_analysis.risk_level != "HIGH",
            "performance_passed": result.performance_metrics.performance_grade in ["A+", "A", "B"],
            "size_passed": result.performance_metrics.app_size_mb < 100
        }
    }

    return json.dumps(summary, indent=2)

def generate_performance_report(result: RCTestResult) -> str:
    """Generate detailed performance report"""
    performance_report = {
        "performance_overview": {
            "grade": result.performance_metrics.performance_grade,
            "app_size_mb": result.performance_metrics.app_size_mb,
            "memory_usage_mb": result.performance_metrics.memory_usage_mb
        },
        "launch_performance": {
            "cold_start_ms": result.performance_metrics.launch_time_cold,
            "warm_start_ms": result.performance_metrics.launch_time_warm,
            "hot_start_ms": result.performance_metrics.launch_time_hot
        },
        "rendering_performance": {
            "average_fps": result.performance_metrics.frame_rate_avg,
            "dropped_frames": result.performance_metrics.dropped_frames,
            "jank_score": result.performance_metrics.jank_score,
            "ui_thread_blocking_percent": result.performance_metrics.ui_thread_blocking
        },
        "size_analysis": result.performance_metrics.size_comparison,
        "recommendations": [
            rec for rec in result.recommendations
            if any(keyword in rec.lower() for keyword in ['performance', 'speed', 'memory', 'size'])
        ]
    }

    return json.dumps(performance_report, indent=2)

def display_executive_summary(result: RCTestResult):
    """Display executive summary in the UI"""
    st.markdown("##### 📊 Executive Summary")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**RC Approval Decision**")
        status_color = {"PASS": "green", "WARNING": "orange", "FAIL": "red"}
        st.markdown(f"<h3 style='color: {status_color.get(result.test_status, 'gray')}'>{result.test_status}</h3>", unsafe_allow_html=True)

        st.markdown("**Key Metrics**")
        st.write(f"• Overall Score: {result.overall_score:.1f}/100")
        st.write(f"• Security Risk: {result.security_analysis.risk_level}")
        st.write(f"• Performance Grade: {result.performance_metrics.performance_grade}")

    with col2:
        st.markdown("**Quality Gates**")
        gates = [
            ("Security", result.security_analysis.risk_level != "HIGH"),
            ("Performance", result.performance_metrics.performance_grade in ["A+", "A", "B"]),
            ("App Size", result.performance_metrics.app_size_mb < 100)
        ]

        for gate_name, passed in gates:
            icon = "✅" if passed else "❌"
            st.write(f"{icon} {gate_name}")

def display_security_summary(result: RCTestResult):
    """Display security analysis summary"""
    st.markdown("##### 🔒 Security Analysis Summary")

    security = result.security_analysis

    col1, col2 = st.columns(2)

    with col1:
        st.write(f"**Vulnerability Score:** {security.vulnerability_score:.1f}/10")
        st.write(f"**Risk Level:** {security.risk_level}")
        st.write(f"**Certificate Valid:** {'Yes' if security.certificate_valid else 'No'}")
        st.write(f"**High-Risk Permissions:** {len(security.permission_risks)}")

    with col2:
        st.markdown("**Compliance Status**")
        for standard, compliant in security.compliance_status.items():
            icon = "✅" if compliant else "❌"
            st.write(f"{icon} {standard}")

def display_performance_summary(result: RCTestResult):
    """Display performance metrics summary"""
    st.markdown("##### ⚡ Performance Summary")

    performance = result.performance_metrics

    col1, col2 = st.columns(2)

    with col1:
        st.write(f"**Performance Grade:** {performance.performance_grade}")
        st.write(f"**App Size:** {performance.app_size_mb:.1f} MB")
        st.write(f"**Cold Start:** {performance.launch_time_cold:.0f} ms")
        st.write(f"**Average FPS:** {performance.frame_rate_avg:.1f}")

    with col2:
        st.write(f"**Memory Usage:** {performance.memory_usage_mb:.1f} MB")
        st.write(f"**Jank Score:** {performance.jank_score:.3f}")
        st.write(f"**Dropped Frames:** {performance.dropped_frames}")
        st.write(f"**UI Blocking:** {performance.ui_thread_blocking:.1f}%")

def display_complete_report(result: RCTestResult):
    """Display complete test report"""
    st.markdown("##### 📋 Complete RC Test Report")

    # Test Overview
    st.markdown("**Test Information**")
    st.write(f"• Test ID: {result.test_id}")
    st.write(f"• Test Date: {result.test_timestamp[:19]}")
    st.write(f"• Duration: {result.test_duration:.1f} seconds")
    st.write(f"• APK Version: {result.apk_metadata.version_name}")
    st.write(f"• Overall Score: {result.overall_score:.1f}/100")
    st.write(f"• Status: {result.test_status}")

    # Key Recommendations
    st.markdown("**Top Recommendations**")
    for i, rec in enumerate(result.recommendations[:5], 1):
        st.write(f"{i}. {rec}")

    # Summary Statistics
    st.markdown("**Summary Statistics**")
    st.write(f"• Security Vulnerability Score: {result.security_analysis.vulnerability_score:.1f}/10")
    st.write(f"• Performance Grade: {result.performance_metrics.performance_grade}")
    st.write(f"• App Size: {result.performance_metrics.app_size_mb:.1f} MB")
    st.write(f"• Launch Time (Cold): {result.performance_metrics.launch_time_cold:.0f} ms")
