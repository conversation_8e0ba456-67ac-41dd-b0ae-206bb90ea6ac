"""
RC Testing Dashboard Tab
Comprehensive Release Candidate testing interface with APK upload and automated analysis
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any
from rc_testing_engine import RCTestingEngine, RCTestResult

def render_rc_testing_tab():
    """Render the RC Testing dashboard tab"""

    # Professional header with enterprise styling
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    ">
        <div style="display: flex; align-items: center; gap: 1rem;">
            <div style="
                background: rgba(255, 255, 255, 0.2);
                padding: 1rem;
                border-radius: 50%;
                backdrop-filter: blur(10px);
            ">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                    <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21C5,22.11 5.89,23 7,23H17C18.11,23 19,22.11 19,21V3C19,1.89 18.11,1 17,1Z"/>
                </svg>
            </div>
            <div>
                <h1 style="color: white; margin: 0; font-size: 2.5rem; font-weight: 700;">
                    Release Candidate Testing
                </h1>
                <p style="color: rgba(255, 255, 255, 0.9); margin: 0.5rem 0 0 0; font-size: 1.1rem;">
                    Enterprise-grade APK analysis and automated validation
                </p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize RC testing engine
    if 'rc_engine' not in st.session_state:
        st.session_state['rc_engine'] = RCTestingEngine()
    
    rc_engine = st.session_state['rc_engine']
    
    # Professional sub-tabs with enterprise icons
    rc_tabs = st.tabs([
        "⬆️ Upload & Analysis",
        "🛡️ Security Assessment",
        "⚡ Performance Metrics",
        "📈 Unified Results",
        "📄 Reports & Export"
    ])
    
    # Tab 1: APK Upload & Analysis
    with rc_tabs[0]:
        # Professional section header
        st.markdown("""
        <div style="
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin-bottom: 2rem;
        ">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="#007bff">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                <div>
                    <h3 style="margin: 0; color: #212529;">APK Upload & Analysis</h3>
                    <p style="margin: 0; color: #6c757d;">Upload Android Release Candidate for automated validation</p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Professional APK Upload Section
        st.markdown("""
        <div style="
            background: white;
            padding: 2rem;
            border-radius: 12px;
            border: 2px dashed #dee2e6;
            text-align: center;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="#6c757d" style="margin-bottom: 1rem;">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            <h4 style="color: #495057; margin: 0.5rem 0;">Upload APK File</h4>
            <p style="color: #6c757d; margin: 0;">Drag and drop your Android Release Candidate</p>
        </div>
        """, unsafe_allow_html=True)

        apk_file = st.file_uploader(
            "Select APK File",
            type=['apk'],
            help="Upload your Android Release Candidate APK file",
            label_visibility="collapsed"
        )

        if apk_file:
            # Professional file info display
            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("""
                <div style="
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    text-align: center;
                ">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="white" style="margin-bottom: 0.5rem;">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                    </svg>
                    <div style="font-weight: 600;">Upload Successful</div>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown(f"""
                <div style="
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    padding: 1rem;
                    border-radius: 8px;
                    text-align: center;
                ">
                    <div style="font-weight: 600; color: #495057;">File Name</div>
                    <div style="color: #6c757d; font-size: 0.9rem;">{apk_file.name}</div>
                </div>
                """, unsafe_allow_html=True)

            with col3:
                file_size_mb = len(apk_file.getvalue()) / (1024*1024)
                st.markdown(f"""
                <div style="
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    padding: 1rem;
                    border-radius: 8px;
                    text-align: center;
                ">
                    <div style="font-weight: 600; color: #495057;">File Size</div>
                    <div style="color: #6c757d; font-size: 0.9rem;">{file_size_mb:.1f} MB</div>
                </div>
                """, unsafe_allow_html=True)
        
        # Professional Analysis Section
        if apk_file:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                padding: 2rem;
                border-radius: 12px;
                margin: 2rem 0;
                text-align: center;
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            ">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="white" style="margin-bottom: 1rem;">
                    <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
                <h3 style="color: white; margin: 0.5rem 0;">Comprehensive RC Analysis</h3>
                <p style="color: rgba(255,255,255,0.9); margin: 0;">Execute complete automated testing suite</p>
            </div>
            """, unsafe_allow_html=True)

            if st.button("▶️ Execute Complete Analysis", type="primary", use_container_width=True):
                with st.spinner("Performing comprehensive RC analysis..."):
                    
                    # Progress tracking
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # Update progress
                    progress_bar.progress(20)
                    status_text.text("🔍 Extracting APK metadata...")
                    
                    # Perform complete analysis
                    result = rc_engine.perform_complete_rc_analysis(apk_file)
                    
                    progress_bar.progress(100)
                    status_text.text("✅ Analysis complete!")
                    
                    if result:
                        st.session_state['current_rc_result'] = result

                        # Professional success notification
                        st.markdown("""
                        <div style="
                            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                            color: white;
                            padding: 1.5rem;
                            border-radius: 8px;
                            margin: 1rem 0;
                            text-align: center;
                        ">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="white" style="margin-bottom: 0.5rem;">
                                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                            </svg>
                            <div style="font-weight: 600;">Analysis Complete</div>
                            <div style="font-size: 0.9rem; opacity: 0.9;">Test ID: {}</div>
                        </div>
                        """.format(result.test_id), unsafe_allow_html=True)

                        # Professional metrics dashboard
                        st.markdown("#### 📊 Executive Summary")

                        # Status indicator
                        status_colors = {"PASS": "#28a745", "WARNING": "#ffc107", "FAIL": "#dc3545"}
                        status_color = status_colors.get(result.test_status, "#6c757d")

                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            st.markdown(f"""
                            <div style="
                                background: {status_color};
                                color: white;
                                padding: 1.5rem;
                                border-radius: 8px;
                                text-align: center;
                            ">
                                <div style="font-size: 1.5rem; font-weight: 700;">{result.test_status}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">Test Status</div>
                            </div>
                            """, unsafe_allow_html=True)

                        with col2:
                            score_color = "#28a745" if result.overall_score >= 85 else "#ffc107" if result.overall_score >= 70 else "#dc3545"
                            st.markdown(f"""
                            <div style="
                                background: {score_color};
                                color: white;
                                padding: 1.5rem;
                                border-radius: 8px;
                                text-align: center;
                            ">
                                <div style="font-size: 1.5rem; font-weight: 700;">{result.overall_score:.1f}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">Overall Score</div>
                            </div>
                            """, unsafe_allow_html=True)

                        with col3:
                            risk_colors = {"LOW": "#28a745", "MEDIUM": "#ffc107", "HIGH": "#dc3545"}
                            risk_color = risk_colors.get(result.security_analysis.risk_level, "#6c757d")
                            st.markdown(f"""
                            <div style="
                                background: {risk_color};
                                color: white;
                                padding: 1.5rem;
                                border-radius: 8px;
                                text-align: center;
                            ">
                                <div style="font-size: 1.5rem; font-weight: 700;">{result.security_analysis.risk_level}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">Security Risk</div>
                            </div>
                            """, unsafe_allow_html=True)

                        with col4:
                            grade_colors = {"A+": "#28a745", "A": "#28a745", "B": "#20c997", "C": "#ffc107", "D": "#dc3545"}
                            grade_color = grade_colors.get(result.performance_metrics.performance_grade, "#6c757d")
                            st.markdown(f"""
                            <div style="
                                background: {grade_color};
                                color: white;
                                padding: 1.5rem;
                                border-radius: 8px;
                                text-align: center;
                            ">
                                <div style="font-size: 1.5rem; font-weight: 700;">{result.performance_metrics.performance_grade}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">Performance</div>
                            </div>
                            """, unsafe_allow_html=True)
                        
                        # Professional APK Metadata Display
                        st.markdown("#### 📋 APK Metadata Analysis")

                        metadata_cols = st.columns(3)

                        with metadata_cols[0]:
                            st.markdown("""
                            <div style="
                                background: white;
                                border: 1px solid #dee2e6;
                                border-radius: 8px;
                                padding: 1.5rem;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                            ">
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                    margin-bottom: 1rem;
                                ">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="#007bff">
                                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                    </svg>
                                    <h5 style="margin: 0; color: #495057;">Build Information</h5>
                                </div>
                                <div style="color: #6c757d; line-height: 1.6;">
                                    <div><strong>Package:</strong> {}</div>
                                    <div><strong>Version:</strong> {} ({})</div>
                                    <div><strong>Size:</strong> {:.1f} MB</div>
                                    <div><strong>Target SDK:</strong> {}</div>
                                </div>
                            </div>
                            """.format(
                                result.apk_metadata.package_name,
                                result.apk_metadata.version_name,
                                result.apk_metadata.version_code,
                                result.apk_metadata.file_size / (1024*1024),
                                result.apk_metadata.target_sdk_version
                            ), unsafe_allow_html=True)

                        with metadata_cols[1]:
                            st.markdown("""
                            <div style="
                                background: white;
                                border: 1px solid #dee2e6;
                                border-radius: 8px;
                                padding: 1.5rem;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                            ">
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                    margin-bottom: 1rem;
                                ">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="#28a745">
                                        <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                                    </svg>
                                    <h5 style="margin: 0; color: #495057;">Components</h5>
                                </div>
                                <div style="color: #6c757d; line-height: 1.6;">
                                    <div><strong>Activities:</strong> {}</div>
                                    <div><strong>Services:</strong> {}</div>
                                    <div><strong>Receivers:</strong> {}</div>
                                    <div><strong>Permissions:</strong> {}</div>
                                </div>
                            </div>
                            """.format(
                                len(result.apk_metadata.activities),
                                len(result.apk_metadata.services),
                                len(result.apk_metadata.receivers),
                                len(result.apk_metadata.permissions)
                            ), unsafe_allow_html=True)

                        with metadata_cols[2]:
                            st.markdown("""
                            <div style="
                                background: white;
                                border: 1px solid #dee2e6;
                                border-radius: 8px;
                                padding: 1.5rem;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                            ">
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                    margin-bottom: 1rem;
                                ">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="#dc3545">
                                        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
                                    </svg>
                                    <h5 style="margin: 0; color: #495057;">Security</h5>
                                </div>
                                <div style="color: #6c757d; line-height: 1.6;">
                                    <div><strong>Certificate:</strong> {}</div>
                                    <div><strong>File Hash:</strong> {}...</div>
                                    <div><strong>Build Time:</strong> {}</div>
                                </div>
                            </div>
                            """.format(
                                result.apk_metadata.signing_certificate.get('issuer', 'Unknown'),
                                result.apk_metadata.file_hash[:16],
                                result.apk_metadata.build_timestamp[:19]
                            ), unsafe_allow_html=True)
                    
                    else:
                        st.error("❌ RC Analysis failed. Please try again.")
        
        else:
            # Professional empty state
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                padding: 3rem;
                border-radius: 12px;
                text-align: center;
                margin: 2rem 0;
                border: 2px dashed #dee2e6;
            ">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="#6c757d" style="margin-bottom: 1rem;">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
                <h4 style="color: #495057; margin: 1rem 0;">No APK File Selected</h4>
                <p style="color: #6c757d; margin: 0;">Upload an Android Release Candidate to begin comprehensive testing</p>
            </div>
            """, unsafe_allow_html=True)
    
    # Tab 2: Security Assessment
    with rc_tabs[1]:
        # Professional security header
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(220, 53, 69, 0.3);
        ">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <div style="
                    background: rgba(255, 255, 255, 0.2);
                    padding: 1rem;
                    border-radius: 50%;
                    backdrop-filter: blur(10px);
                ">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
                    </svg>
                </div>
                <div>
                    <h2 style="color: white; margin: 0; font-weight: 700;">Security Assessment</h2>
                    <p style="color: rgba(255, 255, 255, 0.9); margin: 0.5rem 0 0 0;">
                        Comprehensive vulnerability analysis and compliance validation
                    </p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            security = result.security_analysis
            
            # Security Overview
            col1, col2, col3 = st.columns(3)
            
            with col1:
                risk_colors = {"LOW": "green", "MEDIUM": "orange", "HIGH": "red"}
                st.markdown(f"""
                <div style="
                    background: linear-gradient(90deg, {risk_colors.get(security.risk_level, 'gray')} 0%, 
                    {risk_colors.get(security.risk_level, 'gray')}40 100%);
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    margin: 10px 0;
                ">
                    <h3 style="color: white; margin: 0;">Risk Level: {security.risk_level}</h3>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.metric("Vulnerability Score", f"{security.vulnerability_score:.1f}/10")
            
            with col3:
                cert_status = "✅ Valid" if security.certificate_valid else "❌ Invalid"
                st.metric("Certificate", cert_status)
            
            # Permission Risk Analysis
            st.markdown("#### 🔐 Permission Risk Analysis")
            
            if security.permission_risks:
                risk_data = []
                for risk in security.permission_risks:
                    risk_data.append({
                        "Permission": risk['permission'].split('.')[-1],
                        "Risk Level": risk['risk_level'],
                        "Description": risk['description']
                    })
                
                df_risks = pd.DataFrame(risk_data)
                st.dataframe(df_risks, use_container_width=True)
            else:
                st.success("✅ No high-risk permissions detected")
            
            # Compliance Status
            st.markdown("#### 📋 Security Compliance")
            
            compliance_cols = st.columns(3)
            
            for i, (standard, compliant) in enumerate(security.compliance_status.items()):
                with compliance_cols[i % 3]:
                    status_icon = "✅" if compliant else "❌"
                    st.write(f"{status_icon} **{standard}**")
            
            # Malware Scan Results
            st.markdown("#### 🛡️ Malware Scan Results")
            
            malware = security.malware_scan_result
            scan_status_color = "green" if malware['scan_status'] == 'CLEAN' else "red"
            
            st.markdown(f"""
            <div style="
                background-color: {scan_status_color}20;
                border-left: 4px solid {scan_status_color};
                padding: 15px;
                margin: 10px 0;
            ">
                <strong>Scan Status:</strong> {malware['scan_status']}<br>
                <strong>Threats Detected:</strong> {malware['threats_detected']}<br>
                <strong>Scan Engine:</strong> {malware['scan_engine']}<br>
                <strong>Scan Time:</strong> {malware['scan_time'][:19]}
            </div>
            """, unsafe_allow_html=True)
            
            # Security Recommendations
            st.markdown("#### 💡 Security Recommendations")
            
            for i, recommendation in enumerate(security.security_recommendations, 1):
                st.info(f"{i}. {recommendation}")
        
        else:
            st.info("👆 Please run RC analysis first to view security assessment")
    
    # Tab 3: Performance Metrics
    with rc_tabs[2]:
        # Professional performance header
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(255, 193, 7, 0.3);
        ">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <div style="
                    background: rgba(255, 255, 255, 0.2);
                    padding: 1rem;
                    border-radius: 50%;
                    backdrop-filter: blur(10px);
                ">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                        <path d="M13,2.05V5.08C16.39,5.57 19,8.47 19,12C19,12.9 18.82,13.75 18.5,14.54L21.12,16.07C21.68,14.83 22,13.45 22,12C22,6.82 18.05,2.55 13,2.05M12,19C8.47,19 5.57,16.39 5.08,13H2.05C2.55,18.05 6.82,22 12,22C13.45,22 14.83,21.68 16.07,21.12L14.54,18.5C13.75,18.82 12.9,19 12,19M2.05,11H5.08C5.57,7.61 8.47,5 12,5C12.9,5 13.75,5.18 14.54,5.5L16.07,2.88C14.83,2.32 13.45,2 12,2C6.82,2 2.55,5.95 2.05,11M15,12A3,3 0 0,0 12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12Z"/>
                    </svg>
                </div>
                <div>
                    <h2 style="color: white; margin: 0; font-weight: 700;">Performance Analysis</h2>
                    <p style="color: rgba(255, 255, 255, 0.9); margin: 0.5rem 0 0 0;">
                        Launch time, frame rate, and resource optimization metrics
                    </p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            performance = result.performance_metrics
            
            # Performance Overview
            st.markdown("#### 📊 Performance Overview")
            
            perf_cols = st.columns(4)
            
            with perf_cols[0]:
                st.metric("Performance Grade", performance.performance_grade)
            
            with perf_cols[1]:
                st.metric("App Size", f"{performance.app_size_mb:.1f} MB")
            
            with perf_cols[2]:
                st.metric("Avg Frame Rate", f"{performance.frame_rate_avg:.1f} FPS")
            
            with perf_cols[3]:
                st.metric("Memory Usage", f"{performance.memory_usage_mb:.1f} MB")
            
            # Launch Time Analysis
            st.markdown("#### 🚀 Launch Time Analysis")
            
            launch_data = {
                'Launch Type': ['Cold Start', 'Warm Start', 'Hot Start'],
                'Time (ms)': [
                    performance.launch_time_cold,
                    performance.launch_time_warm,
                    performance.launch_time_hot
                ],
                'Threshold': [3000, 1500, 500]
            }
            
            fig_launch = go.Figure()
            
            fig_launch.add_trace(go.Bar(
                name='Actual Time',
                x=launch_data['Launch Type'],
                y=launch_data['Time (ms)'],
                marker_color=['red' if actual > threshold else 'green' 
                             for actual, threshold in zip(launch_data['Time (ms)'], launch_data['Threshold'])]
            ))
            
            fig_launch.add_trace(go.Scatter(
                name='Threshold',
                x=launch_data['Launch Type'],
                y=launch_data['Threshold'],
                mode='markers+lines',
                line=dict(color='orange', dash='dash'),
                marker=dict(size=8)
            ))
            
            fig_launch.update_layout(
                title="Launch Time Performance",
                yaxis_title="Time (milliseconds)",
                showlegend=True
            )
            
            st.plotly_chart(fig_launch, use_container_width=True)
            
            # App Size Comparison
            st.markdown("#### 📦 App Size Analysis")
            
            size_comparison = performance.size_comparison
            
            size_cols = st.columns(2)
            
            with size_cols[0]:
                st.metric(
                    "Size Change",
                    f"{size_comparison['size_increase_mb']:+.1f} MB",
                    f"{size_comparison['size_increase_percent']:+.1f}%"
                )
                
                st.write(f"**Current:** {size_comparison['current_size_mb']:.1f} MB")
                st.write(f"**Previous:** {size_comparison['previous_size_mb']:.1f} MB")
                st.write(f"**Category:** {size_comparison['size_category']}")
            
            with size_cols[1]:
                # Size trend chart
                if 'size_trend' in size_comparison:
                    trend_data = size_comparison['size_trend']
                    trend_data.append({
                        'version': result.apk_metadata.version_name,
                        'size_mb': size_comparison['current_size_mb']
                    })
                    
                    df_trend = pd.DataFrame(trend_data)
                    
                    fig_size = px.line(
                        df_trend, 
                        x='version', 
                        y='size_mb',
                        title='App Size Trend',
                        markers=True
                    )
                    
                    fig_size.update_layout(
                        yaxis_title="Size (MB)",
                        xaxis_title="Version"
                    )
                    
                    st.plotly_chart(fig_size, use_container_width=True)
            
            # Frame Rate & Jank Analysis
            st.markdown("#### 🎮 Frame Rate & Jank Analysis")
            
            frame_cols = st.columns(3)
            
            with frame_cols[0]:
                st.metric("Average FPS", f"{performance.frame_rate_avg:.1f}")
                st.metric("Dropped Frames", performance.dropped_frames)
            
            with frame_cols[1]:
                st.metric("Jank Score", f"{performance.jank_score:.3f}")
                st.metric("UI Thread Blocking", f"{performance.ui_thread_blocking:.1f}%")
            
            with frame_cols[2]:
                # Frame rate gauge
                fig_fps = go.Figure(go.Indicator(
                    mode = "gauge+number",
                    value = performance.frame_rate_avg,
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "Frame Rate (FPS)"},
                    gauge = {
                        'axis': {'range': [None, 60]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 30], 'color': "lightgray"},
                            {'range': [30, 55], 'color': "yellow"},
                            {'range': [55, 60], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 55
                        }
                    }
                ))
                
                fig_fps.update_layout(height=300)
                st.plotly_chart(fig_fps, use_container_width=True)
        
        else:
            st.info("👆 Please run RC analysis first to view performance metrics")

    # Tab 4: Unified Results
    with rc_tabs[3]:
        st.markdown("### 📊 Unified RC Testing Results")

        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']

            # Overall Status Dashboard
            st.markdown("#### 🎯 RC Approval Status")

            status_colors = {"PASS": "green", "WARNING": "orange", "FAIL": "red"}
            status_color = status_colors.get(result.test_status, "gray")

            st.markdown(f"""
            <div style="
                background: linear-gradient(90deg, {status_color} 0%, {status_color}40 100%);
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin: 20px 0;
            ">
                <h1 style="color: white; margin: 0;">RC Status: {result.test_status}</h1>
                <h3 style="color: white; margin: 10px 0;">Overall Score: {result.overall_score:.1f}/100</h3>
            </div>
            """, unsafe_allow_html=True)

            # Comprehensive Metrics Dashboard
            st.markdown("#### 📈 Comprehensive Metrics")

            # Create metrics summary
            metrics_data = {
                'Category': ['Security', 'Performance', 'App Size', 'Launch Time', 'Frame Rate', 'Memory'],
                'Score': [
                    max(0, (10 - result.security_analysis.vulnerability_score) / 10 * 100),
                    {"A+": 100, "A": 90, "B": 80, "C": 70, "D": 60}.get(result.performance_metrics.performance_grade, 50),
                    100 if result.performance_metrics.app_size_mb < 50 else 80 if result.performance_metrics.app_size_mb < 100 else 60,
                    100 if result.performance_metrics.launch_time_cold < 2000 else 80 if result.performance_metrics.launch_time_cold < 3000 else 60,
                    (result.performance_metrics.frame_rate_avg / 60) * 100,
                    100 if result.performance_metrics.memory_usage_mb < 100 else 80 if result.performance_metrics.memory_usage_mb < 150 else 60
                ],
                'Status': []
            }

            # Determine status for each metric
            for score in metrics_data['Score']:
                if score >= 85:
                    metrics_data['Status'].append('✅ Pass')
                elif score >= 70:
                    metrics_data['Status'].append('⚠️ Warning')
                else:
                    metrics_data['Status'].append('❌ Fail')

            # Display metrics table
            df_metrics = pd.DataFrame(metrics_data)
            st.dataframe(df_metrics, use_container_width=True)

            # Radar Chart for Overall Performance
            st.markdown("#### 🕸️ Performance Radar")

            fig_radar = go.Figure()

            fig_radar.add_trace(go.Scatterpolar(
                r=metrics_data['Score'],
                theta=metrics_data['Category'],
                fill='toself',
                name='RC Performance'
            ))

            fig_radar.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="RC Performance Overview"
            )

            st.plotly_chart(fig_radar, use_container_width=True)

            # Key Recommendations
            st.markdown("#### 💡 Key Recommendations")

            for i, recommendation in enumerate(result.recommendations, 1):
                priority = "🔴 High" if i <= 2 else "🟡 Medium" if i <= 4 else "🟢 Low"
                st.markdown(f"**{priority} Priority:** {recommendation}")

            # Test Summary
            st.markdown("#### 📋 Test Summary")

            summary_cols = st.columns(2)

            with summary_cols[0]:
                st.markdown("**Test Information**")
                st.write(f"• **Test ID:** {result.test_id}")
                st.write(f"• **Test Date:** {result.test_timestamp[:19]}")
                st.write(f"• **Duration:** {result.test_duration:.1f} seconds")
                st.write(f"• **APK Version:** {result.apk_metadata.version_name}")

            with summary_cols[1]:
                st.markdown("**Quality Gates**")

                # Define quality gates
                quality_gates = [
                    ("Security Risk", result.security_analysis.risk_level != "HIGH"),
                    ("Performance Grade", result.performance_metrics.performance_grade in ["A+", "A", "B"]),
                    ("App Size", result.performance_metrics.app_size_mb < 100),
                    ("Launch Time", result.performance_metrics.launch_time_cold < 3000),
                    ("Frame Rate", result.performance_metrics.frame_rate_avg > 55)
                ]

                for gate_name, passed in quality_gates:
                    status_icon = "✅" if passed else "❌"
                    st.write(f"• {status_icon} {gate_name}")

        else:
            st.info("👆 Please run RC analysis first to view unified results")

    # Tab 5: RC Reports
    with rc_tabs[4]:
        st.markdown("### 📋 RC Testing Reports")

        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']

            # Report Generation Options
            st.markdown("#### 📄 Generate RC Reports")

            report_cols = st.columns(3)

            with report_cols[0]:
                if st.button("📊 Executive Summary", use_container_width=True):
                    executive_report = generate_executive_summary(result)
                    st.download_button(
                        label="💾 Download Executive Summary",
                        data=executive_report,
                        file_name=f"RC_Executive_Summary_{result.test_id}.json",
                        mime="application/json"
                    )

            with report_cols[1]:
                if st.button("🔍 Detailed Technical Report", use_container_width=True):
                    technical_report = rc_engine.export_test_result(result.test_id, "json")
                    st.download_button(
                        label="💾 Download Technical Report",
                        data=technical_report,
                        file_name=f"RC_Technical_Report_{result.test_id}.json",
                        mime="application/json"
                    )

            with report_cols[2]:
                if st.button("📈 Performance Report", use_container_width=True):
                    performance_report = generate_performance_report(result)
                    st.download_button(
                        label="💾 Download Performance Report",
                        data=performance_report,
                        file_name=f"RC_Performance_Report_{result.test_id}.json",
                        mime="application/json"
                    )

            # Report Preview
            st.markdown("#### 👀 Report Preview")

            report_type = st.selectbox(
                "Select report type to preview:",
                ["Executive Summary", "Security Analysis", "Performance Metrics", "Complete Report"]
            )

            if report_type == "Executive Summary":
                display_executive_summary(result)
            elif report_type == "Security Analysis":
                display_security_summary(result)
            elif report_type == "Performance Metrics":
                display_performance_summary(result)
            else:
                display_complete_report(result)

            # Historical Results
            st.markdown("#### 📊 Historical RC Results")

            if len(rc_engine.test_results) > 1:
                # Create historical comparison
                historical_data = []
                for test_id, test_result in rc_engine.test_results.items():
                    historical_data.append({
                        'Test ID': test_id,
                        'Date': test_result.test_timestamp[:10],
                        'Version': test_result.apk_metadata.version_name,
                        'Status': test_result.test_status,
                        'Overall Score': test_result.overall_score,
                        'Security Risk': test_result.security_analysis.risk_level,
                        'Performance Grade': test_result.performance_metrics.performance_grade
                    })

                df_historical = pd.DataFrame(historical_data)
                st.dataframe(df_historical, use_container_width=True)

                # Trend Analysis
                if len(historical_data) > 1:
                    fig_trend = px.line(
                        df_historical,
                        x='Date',
                        y='Overall Score',
                        title='RC Quality Trend',
                        markers=True
                    )
                    st.plotly_chart(fig_trend, use_container_width=True)
            else:
                st.info("Run multiple RC tests to see historical trends")

        else:
            st.info("👆 Please run RC analysis first to generate reports")

def generate_executive_summary(result: RCTestResult) -> str:
    """Generate executive summary report"""
    summary = {
        "rc_approval_status": result.test_status,
        "overall_score": result.overall_score,
        "test_date": result.test_timestamp,
        "app_version": result.apk_metadata.version_name,
        "key_findings": {
            "security_risk_level": result.security_analysis.risk_level,
            "performance_grade": result.performance_metrics.performance_grade,
            "app_size_mb": result.performance_metrics.app_size_mb,
            "launch_time_ms": result.performance_metrics.launch_time_cold
        },
        "recommendations": result.recommendations[:5],
        "quality_gates": {
            "security_passed": result.security_analysis.risk_level != "HIGH",
            "performance_passed": result.performance_metrics.performance_grade in ["A+", "A", "B"],
            "size_passed": result.performance_metrics.app_size_mb < 100
        }
    }

    return json.dumps(summary, indent=2)

def generate_performance_report(result: RCTestResult) -> str:
    """Generate detailed performance report"""
    performance_report = {
        "performance_overview": {
            "grade": result.performance_metrics.performance_grade,
            "app_size_mb": result.performance_metrics.app_size_mb,
            "memory_usage_mb": result.performance_metrics.memory_usage_mb
        },
        "launch_performance": {
            "cold_start_ms": result.performance_metrics.launch_time_cold,
            "warm_start_ms": result.performance_metrics.launch_time_warm,
            "hot_start_ms": result.performance_metrics.launch_time_hot
        },
        "rendering_performance": {
            "average_fps": result.performance_metrics.frame_rate_avg,
            "dropped_frames": result.performance_metrics.dropped_frames,
            "jank_score": result.performance_metrics.jank_score,
            "ui_thread_blocking_percent": result.performance_metrics.ui_thread_blocking
        },
        "size_analysis": result.performance_metrics.size_comparison,
        "recommendations": [
            rec for rec in result.recommendations
            if any(keyword in rec.lower() for keyword in ['performance', 'speed', 'memory', 'size'])
        ]
    }

    return json.dumps(performance_report, indent=2)

def display_executive_summary(result: RCTestResult):
    """Display executive summary in the UI"""
    st.markdown("##### 📊 Executive Summary")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**RC Approval Decision**")
        status_color = {"PASS": "green", "WARNING": "orange", "FAIL": "red"}
        st.markdown(f"<h3 style='color: {status_color.get(result.test_status, 'gray')}'>{result.test_status}</h3>", unsafe_allow_html=True)

        st.markdown("**Key Metrics**")
        st.write(f"• Overall Score: {result.overall_score:.1f}/100")
        st.write(f"• Security Risk: {result.security_analysis.risk_level}")
        st.write(f"• Performance Grade: {result.performance_metrics.performance_grade}")

    with col2:
        st.markdown("**Quality Gates**")
        gates = [
            ("Security", result.security_analysis.risk_level != "HIGH"),
            ("Performance", result.performance_metrics.performance_grade in ["A+", "A", "B"]),
            ("App Size", result.performance_metrics.app_size_mb < 100)
        ]

        for gate_name, passed in gates:
            icon = "✅" if passed else "❌"
            st.write(f"{icon} {gate_name}")

def display_security_summary(result: RCTestResult):
    """Display security analysis summary"""
    st.markdown("##### 🔒 Security Analysis Summary")

    security = result.security_analysis

    col1, col2 = st.columns(2)

    with col1:
        st.write(f"**Vulnerability Score:** {security.vulnerability_score:.1f}/10")
        st.write(f"**Risk Level:** {security.risk_level}")
        st.write(f"**Certificate Valid:** {'Yes' if security.certificate_valid else 'No'}")
        st.write(f"**High-Risk Permissions:** {len(security.permission_risks)}")

    with col2:
        st.markdown("**Compliance Status**")
        for standard, compliant in security.compliance_status.items():
            icon = "✅" if compliant else "❌"
            st.write(f"{icon} {standard}")

def display_performance_summary(result: RCTestResult):
    """Display performance metrics summary"""
    st.markdown("##### ⚡ Performance Summary")

    performance = result.performance_metrics

    col1, col2 = st.columns(2)

    with col1:
        st.write(f"**Performance Grade:** {performance.performance_grade}")
        st.write(f"**App Size:** {performance.app_size_mb:.1f} MB")
        st.write(f"**Cold Start:** {performance.launch_time_cold:.0f} ms")
        st.write(f"**Average FPS:** {performance.frame_rate_avg:.1f}")

    with col2:
        st.write(f"**Memory Usage:** {performance.memory_usage_mb:.1f} MB")
        st.write(f"**Jank Score:** {performance.jank_score:.3f}")
        st.write(f"**Dropped Frames:** {performance.dropped_frames}")
        st.write(f"**UI Blocking:** {performance.ui_thread_blocking:.1f}%")

def display_complete_report(result: RCTestResult):
    """Display complete test report"""
    st.markdown("##### 📋 Complete RC Test Report")

    # Test Overview
    st.markdown("**Test Information**")
    st.write(f"• Test ID: {result.test_id}")
    st.write(f"• Test Date: {result.test_timestamp[:19]}")
    st.write(f"• Duration: {result.test_duration:.1f} seconds")
    st.write(f"• APK Version: {result.apk_metadata.version_name}")
    st.write(f"• Overall Score: {result.overall_score:.1f}/100")
    st.write(f"• Status: {result.test_status}")

    # Key Recommendations
    st.markdown("**Top Recommendations**")
    for i, rec in enumerate(result.recommendations[:5], 1):
        st.write(f"{i}. {rec}")

    # Summary Statistics
    st.markdown("**Summary Statistics**")
    st.write(f"• Security Vulnerability Score: {result.security_analysis.vulnerability_score:.1f}/10")
    st.write(f"• Performance Grade: {result.performance_metrics.performance_grade}")
    st.write(f"• App Size: {result.performance_metrics.app_size_mb:.1f} MB")
    st.write(f"• Launch Time (Cold): {result.performance_metrics.launch_time_cold:.0f} ms")
