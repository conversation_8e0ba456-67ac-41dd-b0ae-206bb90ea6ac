#!/usr/bin/env python3
"""
Test script to verify emulator detection is working correctly
"""

from emulator_manager import EmulatorManager
import subprocess

def main():
    print("🔍 Testing Emulator Detection")
    print("=" * 40)
    
    # Initialize emulator manager
    manager = EmulatorManager()
    
    print(f"ADB Available: {'✅' if manager.is_adb_available() else '❌'}")
    print(f"Emulator Available: {'✅' if manager.is_emulator_available() else '❌'}")
    
    # Check ADB devices
    print("\n📱 ADB Devices:")
    adb_devices = manager.get_adb_devices()
    if adb_devices:
        for device_id, status in adb_devices.items():
            print(f"   • {device_id}: {status}")
    else:
        print("   No ADB devices found")
    
    # Test emulator discovery
    print("\n🔍 Discovering Emulators...")
    emulators = manager.discover_emulators()
    
    if emulators:
        print(f"Found {len(emulators)} emulator(s):")
        for emulator in emulators:
            status = "🟢 Running" if emulator.running else "🔴 Stopped"
            print(f"\n   📱 {emulator.name}")
            print(f"      Status: {status}")
            print(f"      Device: {emulator.device_name}")
            print(f"      API Level: {emulator.api_level}")
            print(f"      Resolution: {emulator.resolution}")
            print(f"      Architecture: {emulator.arch}")
            
            if emulator.running:
                print(f"      ADB ID: {emulator.adb_id}")
                print(f"      Port: {emulator.port}")
                print(f"      PID: {emulator.pid}")
    else:
        print("❌ No emulators found")
    
    # Test running emulator detection specifically
    print("\n🏃 Running Emulator Detection:")
    running_emulators = manager._get_running_emulators()
    
    if running_emulators:
        for avd_name, info in running_emulators.items():
            print(f"   • {avd_name}:")
            print(f"     ADB ID: {info.get('adb_id')}")
            print(f"     Port: {info.get('port')}")
            print(f"     PID: {info.get('pid')}")
    else:
        print("   No running emulators detected")
    
    print("\n" + "=" * 40)
    print("✅ Detection test complete!")

if __name__ == "__main__":
    main()
