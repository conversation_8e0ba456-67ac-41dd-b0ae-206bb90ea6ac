
#!/usr/bin/env python3
"""
Simple working test for app functionality
"""

from appium import webdriver
from appium.options.android import UiAutomator2Options
import time

def test_basic_functionality():
    """Test basic app functionality"""
    print("🧪 Testing Basic App Functionality")
    print("=" * 35)
    
    try:
        # Setup
        print("1️⃣ Setting up connection...")
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.no_reset = True
        
        # Connect
        driver = webdriver.Remote("http://localhost:4723", options=options)
        print("✅ Connected to emulator")
        
        # Test 1: Screenshot
        print("2️⃣ Testing screenshot...")
        screenshot = driver.get_screenshot_as_base64()
        print(f"✅ Screenshot: {len(screenshot)} chars")
        
        # Test 2: Current activity
        print("3️⃣ Testing current activity...")
        activity = driver.current_activity
        print(f"✅ Current activity: {activity}")
        
        # Test 3: Launch Settings
        print("4️⃣ Testing app launch...")
        driver.activate_app("com.android.settings")
        time.sleep(3)
        
        new_activity = driver.current_activity
        print(f"✅ Settings launched: {new_activity}")
        
        # Test 4: Take screenshot of Settings
        print("5️⃣ Testing screenshot in app...")
        app_screenshot = driver.get_screenshot_as_base64()
        print(f"✅ App screenshot: {len(app_screenshot)} chars")
        
        # Test 5: Home button
        print("6️⃣ Testing home button...")
        driver.press_keycode(3)
        time.sleep(2)
        
        home_activity = driver.current_activity
        print(f"✅ Home button: {home_activity}")
        
        # Cleanup
        driver.quit()
        print("✅ Test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Details: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_basic_functionality()
