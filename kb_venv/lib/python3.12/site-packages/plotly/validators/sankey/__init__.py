import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._visible.VisibleValidator",
        "._valuesuffix.ValuesuffixValidator",
        "._valueformat.ValueformatValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._textfont.TextfontValidator",
        "._stream.StreamValidator",
        "._selectedpoints.SelectedpointsValidator",
        "._orientation.OrientationValidator",
        "._node.NodeValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._link.LinkValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legend.LegendValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfo.HoverinfoValidator",
        "._domain.DomainValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._arrangement.ArrangementValidator",
    ],
)
