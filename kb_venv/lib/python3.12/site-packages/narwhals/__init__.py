from __future__ import annotations

import typing as _t

from narwhals import dependencies
from narwhals import dtypes
from narwhals import exceptions
from narwhals import selectors
from narwhals.dataframe import DataFrame
from narwhals.dataframe import LazyFrame
from narwhals.dtypes import Array
from narwhals.dtypes import Binary
from narwhals.dtypes import Boolean
from narwhals.dtypes import Categorical
from narwhals.dtypes import Date
from narwhals.dtypes import Datetime
from narwhals.dtypes import Decimal
from narwhals.dtypes import Duration
from narwhals.dtypes import Enum
from narwhals.dtypes import Field
from narwhals.dtypes import Float32
from narwhals.dtypes import Float64
from narwhals.dtypes import Int8
from narwhals.dtypes import Int16
from narwhals.dtypes import Int32
from narwhals.dtypes import Int64
from narwhals.dtypes import Int128
from narwhals.dtypes import List
from narwhals.dtypes import Object
from narwhals.dtypes import String
from narwhals.dtypes import Struct
from narwhals.dtypes import Time
from narwhals.dtypes import UInt8
from narwhals.dtypes import UInt16
from narwhals.dtypes import UInt32
from narwhals.dtypes import UInt64
from narwhals.dtypes import UInt128
from narwhals.dtypes import Unknown
from narwhals.expr import Expr
from narwhals.functions import all_ as all
from narwhals.functions import all_horizontal
from narwhals.functions import any_horizontal
from narwhals.functions import col
from narwhals.functions import concat
from narwhals.functions import concat_str
from narwhals.functions import exclude
from narwhals.functions import from_arrow
from narwhals.functions import from_dict
from narwhals.functions import from_numpy
from narwhals.functions import get_level
from narwhals.functions import len_ as len
from narwhals.functions import lit
from narwhals.functions import max
from narwhals.functions import max_horizontal
from narwhals.functions import mean
from narwhals.functions import mean_horizontal
from narwhals.functions import median
from narwhals.functions import min
from narwhals.functions import min_horizontal
from narwhals.functions import new_series
from narwhals.functions import nth
from narwhals.functions import read_csv
from narwhals.functions import read_parquet
from narwhals.functions import scan_csv
from narwhals.functions import scan_parquet
from narwhals.functions import show_versions
from narwhals.functions import sum
from narwhals.functions import sum_horizontal
from narwhals.functions import when
from narwhals.schema import Schema
from narwhals.series import Series
from narwhals.translate import from_native
from narwhals.translate import get_native_namespace
from narwhals.translate import narwhalify
from narwhals.translate import to_native
from narwhals.translate import to_py_scalar
from narwhals.utils import Implementation
from narwhals.utils import generate_temporary_column_name
from narwhals.utils import is_ordered_categorical
from narwhals.utils import maybe_align_index
from narwhals.utils import maybe_convert_dtypes
from narwhals.utils import maybe_get_index
from narwhals.utils import maybe_reset_index
from narwhals.utils import maybe_set_index

__version__: str

__all__ = [
    "Array",
    "Binary",
    "Boolean",
    "Categorical",
    "DataFrame",
    "Date",
    "Datetime",
    "Decimal",
    "Duration",
    "Enum",
    "Expr",
    "Field",
    "Float32",
    "Float64",
    "Implementation",
    "Int8",
    "Int16",
    "Int32",
    "Int64",
    "Int128",
    "LazyFrame",
    "List",
    "Object",
    "Schema",
    "Series",
    "String",
    "Struct",
    "Time",
    "UInt8",
    "UInt16",
    "UInt32",
    "UInt64",
    "UInt128",
    "Unknown",
    "all",
    "all_horizontal",
    "any_horizontal",
    "col",
    "concat",
    "concat_str",
    "dependencies",
    "dtypes",
    "exceptions",
    "exclude",
    "from_arrow",
    "from_dict",
    "from_native",
    "from_numpy",
    "generate_temporary_column_name",
    "get_level",
    "get_native_namespace",
    "is_ordered_categorical",
    "len",
    "lit",
    "max",
    "max_horizontal",
    "maybe_align_index",
    "maybe_convert_dtypes",
    "maybe_get_index",
    "maybe_reset_index",
    "maybe_set_index",
    "mean",
    "mean_horizontal",
    "median",
    "min",
    "min_horizontal",
    "narwhalify",
    "new_series",
    "nth",
    "read_csv",
    "read_parquet",
    "scan_csv",
    "scan_parquet",
    "selectors",
    "show_versions",
    "sum",
    "sum_horizontal",
    "to_native",
    "to_py_scalar",
    "when",
]


def __getattr__(name: _t.Literal["__version__"]) -> str:  # type: ignore[misc]
    if name == "__version__":
        global __version__  # noqa: PLW0603

        from importlib import metadata

        __version__ = metadata.version(__name__)
        return __version__
    else:
        msg = f"module {__name__!r} has no attribute {name!r}"
        raise AttributeError(msg)
