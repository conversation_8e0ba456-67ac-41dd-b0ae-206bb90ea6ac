"""
AI-Powered Test Agent
Interprets natural language test instructions and executes them autonomously
"""

import re
import json
import time
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
import base64
from datetime import datetime

from appium_driver import AppiumDriverWrapper, TestAction, ElementInfo

@dataclass
class TestStep:
    """Represents a single test step"""
    instruction: str
    action: TestAction
    expected_result: str
    screenshot_before: Optional[str] = None
    screenshot_after: Optional[str] = None
    execution_time: Optional[float] = None
    success: bool = False
    error_message: str = ""

@dataclass
class TestScenario:
    """Represents a complete test scenario"""
    name: str
    description: str
    steps: List[TestStep]
    app_package: str
    app_activity: str = ""
    setup_steps: List[str] = None
    teardown_steps: List[str] = None

class NaturalLanguageProcessor:
    """Processes natural language test instructions"""
    
    def __init__(self):
        self.action_patterns = {
            'tap': [
                r'tap (?:on )?(.+)',
                r'click (?:on )?(.+)',
                r'press (?:on )?(.+)',
                r'touch (?:on )?(.+)'
            ],
            'type': [
                r'type "([^"]+)"(?: (?:in|into) (.+))?',
                r'enter "([^"]+)"(?: (?:in|into) (.+))?',
                r'input "([^"]+)"(?: (?:in|into) (.+))?'
            ],
            'swipe': [
                r'swipe (up|down|left|right)',
                r'scroll (up|down)',
                r'drag from (.+) to (.+)'
            ],
            'wait': [
                r'wait (?:for )?(\d+) seconds?',
                r'wait (?:for )?(.+) (?:to appear|to be visible)'
            ],
            'verify': [
                r'verify (?:that )?(.+) (?:is visible|appears|exists)',
                r'check (?:that )?(.+) (?:is visible|appears|exists)',
                r'assert (?:that )?(.+) (?:is visible|appears|exists)'
            ],
            'navigate': [
                r'(?:go to|navigate to|open) (.+)',
                r'launch (.+)',
                r'start (.+)'
            ]
        }
        
        self.element_selectors = {
            'button': ['button', 'btn'],
            'text_field': ['text field', 'input', 'edit text', 'textbox'],
            'label': ['label', 'text', 'title'],
            'image': ['image', 'icon', 'picture'],
            'list': ['list', 'recycler view'],
            'menu': ['menu', 'navigation', 'hamburger']
        }
    
    def parse_instruction(self, instruction: str) -> Optional[TestAction]:
        """Parse natural language instruction into TestAction"""
        instruction = instruction.lower().strip()
        
        for action_type, patterns in self.action_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, instruction)
                if match:
                    return self._create_action(action_type, match.groups(), instruction)
        
        return None
    
    def _create_action(self, action_type: str, groups: Tuple, original_instruction: str) -> TestAction:
        """Create TestAction from parsed groups"""
        if action_type == 'tap':
            element_desc = groups[0] if groups else ""
            return TestAction(
                action_type='tap',
                element_locator=self._element_to_xpath(element_desc),
                description=original_instruction
            )
        
        elif action_type == 'type':
            text = groups[0] if groups else ""
            element_desc = groups[1] if len(groups) > 1 and groups[1] else ""
            return TestAction(
                action_type='type',
                text_input=text,
                element_locator=self._element_to_xpath(element_desc) if element_desc else None,
                description=original_instruction
            )
        
        elif action_type == 'swipe':
            direction = groups[0] if groups else "down"
            if direction in ['up', 'down']:
                return TestAction(
                    action_type=f'scroll_{direction}',
                    description=original_instruction
                )
            else:
                return TestAction(
                    action_type='swipe',
                    description=original_instruction
                )
        
        elif action_type == 'wait':
            if groups[0].isdigit():
                return TestAction(
                    action_type='wait',
                    duration=int(groups[0]),
                    description=original_instruction
                )
            else:
                return TestAction(
                    action_type='wait_for_element',
                    element_locator=self._element_to_xpath(groups[0]),
                    description=original_instruction
                )
        
        elif action_type == 'verify':
            element_desc = groups[0] if groups else ""
            return TestAction(
                action_type='verify',
                element_locator=self._element_to_xpath(element_desc),
                description=original_instruction
            )
        
        elif action_type == 'navigate':
            target = groups[0] if groups else ""
            return TestAction(
                action_type='navigate',
                text_input=target,
                description=original_instruction
            )
        
        return TestAction(action_type='unknown', description=original_instruction)
    
    def _element_to_xpath(self, element_desc: str) -> str:
        """Convert element description to XPath"""
        if not element_desc:
            return ""
        
        element_desc = element_desc.lower().strip()
        
        # Try exact text match first
        xpath_options = [
            f"//*[@text='{element_desc}']",
            f"//*[contains(@text, '{element_desc}')]",
            f"//*[@content-desc='{element_desc}']",
            f"//*[contains(@content-desc, '{element_desc}')]"
        ]
        
        # Check for element type keywords
        for element_type, keywords in self.element_selectors.items():
            for keyword in keywords:
                if keyword in element_desc:
                    text_part = element_desc.replace(keyword, '').strip()
                    if text_part:
                        xpath_options.extend([
                            f"//*[contains(@class, '{element_type.title()}') and contains(@text, '{text_part}')]",
                            f"//*[contains(@class, '{element_type.title()}') and contains(@content-desc, '{text_part}')]"
                        ])
                    break
        
        # Return the first (most specific) XPath
        return xpath_options[0] if xpath_options else f"//*[contains(@text, '{element_desc}')]"

class AITestAgent:
    """AI-powered test execution agent"""
    
    def __init__(self, appium_driver: AppiumDriverWrapper):
        self.driver = appium_driver
        self.nlp = NaturalLanguageProcessor()
        self.current_scenario: Optional[TestScenario] = None
        self.execution_history: List[TestStep] = []
        self.screenshot_history: List[Tuple[str, str]] = []  # (timestamp, base64_image)
    
    def create_test_scenario(self, name: str, description: str, instructions: List[str], 
                           app_package: str, app_activity: str = "") -> TestScenario:
        """Create a test scenario from natural language instructions"""
        steps = []
        
        for instruction in instructions:
            action = self.nlp.parse_instruction(instruction)
            if action:
                step = TestStep(
                    instruction=instruction,
                    action=action,
                    expected_result=""  # Could be enhanced with AI prediction
                )
                steps.append(step)
        
        scenario = TestScenario(
            name=name,
            description=description,
            steps=steps,
            app_package=app_package,
            app_activity=app_activity
        )
        
        return scenario
    
    def execute_scenario(self, scenario: TestScenario, device_id: str) -> Dict[str, Any]:
        """Execute a complete test scenario"""
        self.current_scenario = scenario
        results = {
            'scenario_name': scenario.name,
            'start_time': datetime.now().isoformat(),
            'device_id': device_id,
            'steps': [],
            'overall_success': True,
            'total_steps': len(scenario.steps),
            'passed_steps': 0,
            'failed_steps': 0,
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            # Connect to device
            if not self.driver.connect_to_device(device_id, scenario.app_package, scenario.app_activity):
                results['overall_success'] = False
                results['error'] = "Failed to connect to device"
                return results
            
            # Execute setup steps if any
            if scenario.setup_steps:
                for setup_instruction in scenario.setup_steps:
                    self._execute_instruction(setup_instruction)
            
            # Execute main test steps
            for step in scenario.steps:
                step_result = self._execute_step(step)
                results['steps'].append(step_result)
                
                if step_result['success']:
                    results['passed_steps'] += 1
                else:
                    results['failed_steps'] += 1
                    if step_result.get('critical', False):
                        results['overall_success'] = False
                        break
            
            # Execute teardown steps if any
            if scenario.teardown_steps:
                for teardown_instruction in scenario.teardown_steps:
                    self._execute_instruction(teardown_instruction)
        
        except Exception as e:
            results['overall_success'] = False
            results['error'] = str(e)
        
        finally:
            results['execution_time'] = time.time() - start_time
            results['end_time'] = datetime.now().isoformat()
            
            # Disconnect from device
            self.driver.disconnect()
        
        return results
    
    def _execute_step(self, step: TestStep) -> Dict[str, Any]:
        """Execute a single test step"""
        step_start_time = time.time()
        
        # Take screenshot before action
        step.screenshot_before = self.driver.take_screenshot()
        
        step_result = {
            'instruction': step.instruction,
            'action_type': step.action.action_type,
            'success': False,
            'execution_time': 0,
            'screenshot_before': step.screenshot_before,
            'screenshot_after': None,
            'error_message': ""
        }
        
        try:
            # Execute the action
            if step.action.action_type == 'wait':
                if step.action.duration:
                    time.sleep(step.action.duration)
                    step.success = True
                elif step.action.element_locator:
                    element = self.driver.wait_for_element('xpath', step.action.element_locator, 10)
                    step.success = element is not None
            
            elif step.action.action_type == 'verify':
                element = self.driver.find_element_by_xpath(step.action.element_locator)
                step.success = element is not None and element.is_displayed()
            
            elif step.action.action_type == 'navigate':
                # Handle app navigation
                if 'home' in step.action.text_input.lower():
                    step.success = self.driver.press_home()
                elif 'back' in step.action.text_input.lower():
                    step.success = self.driver.press_back()
                else:
                    # Try to find and tap navigation element
                    element = self.driver.find_element_by_xpath(self.nlp._element_to_xpath(step.action.text_input))
                    step.success = self.driver.tap_element(element)
            
            else:
                # Execute standard actions
                step.success = self.driver.execute_test_action(step.action)
            
            # Small delay after action
            time.sleep(0.5)
            
        except Exception as e:
            step.error_message = str(e)
            step.success = False
        
        # Take screenshot after action
        step.screenshot_after = self.driver.take_screenshot()
        step.execution_time = time.time() - step_start_time
        
        # Update step result
        step_result.update({
            'success': step.success,
            'execution_time': step.execution_time,
            'screenshot_after': step.screenshot_after,
            'error_message': step.error_message
        })
        
        # Add to execution history
        self.execution_history.append(step)
        
        return step_result
    
    def _execute_instruction(self, instruction: str) -> bool:
        """Execute a single instruction"""
        action = self.nlp.parse_instruction(instruction)
        if action:
            return self.driver.execute_test_action(action)
        return False
    
    def analyze_screen(self) -> Dict[str, Any]:
        """Analyze current screen and provide insights"""
        if not self.driver.is_connected():
            return {}
        
        try:
            # Get page source
            page_source = self.driver.get_page_source()
            screenshot = self.driver.take_screenshot()
            device_info = self.driver.get_device_info()
            
            # Basic analysis
            analysis = {
                'timestamp': datetime.now().isoformat(),
                'device_info': device_info,
                'screenshot': screenshot,
                'elements_found': 0,
                'clickable_elements': 0,
                'text_fields': 0,
                'buttons': 0,
                'interactive_elements': []
            }
            
            if page_source:
                # Parse XML and count elements
                import xml.etree.ElementTree as ET
                try:
                    root = ET.fromstring(page_source)
                    
                    for elem in root.iter():
                        analysis['elements_found'] += 1
                        
                        if elem.get('clickable') == 'true':
                            analysis['clickable_elements'] += 1
                            
                            # Collect interactive element info
                            element_info = {
                                'class': elem.get('class', ''),
                                'text': elem.get('text', ''),
                                'content_desc': elem.get('content-desc', ''),
                                'bounds': elem.get('bounds', ''),
                                'resource_id': elem.get('resource-id', '')
                            }
                            
                            if any(element_info.values()):
                                analysis['interactive_elements'].append(element_info)
                        
                        if 'EditText' in elem.get('class', ''):
                            analysis['text_fields'] += 1
                        
                        if 'Button' in elem.get('class', ''):
                            analysis['buttons'] += 1
                
                except ET.ParseError:
                    pass
            
            return analysis
        
        except Exception as e:
            return {'error': str(e)}
    
    def get_execution_report(self) -> Dict[str, Any]:
        """Generate comprehensive execution report"""
        if not self.execution_history:
            return {}
        
        total_steps = len(self.execution_history)
        passed_steps = sum(1 for step in self.execution_history if step.success)
        failed_steps = total_steps - passed_steps
        
        total_time = sum(step.execution_time or 0 for step in self.execution_history)
        
        report = {
            'summary': {
                'total_steps': total_steps,
                'passed_steps': passed_steps,
                'failed_steps': failed_steps,
                'success_rate': (passed_steps / total_steps * 100) if total_steps > 0 else 0,
                'total_execution_time': total_time
            },
            'steps': [
                {
                    'instruction': step.instruction,
                    'action_type': step.action.action_type,
                    'success': step.success,
                    'execution_time': step.execution_time,
                    'error_message': step.error_message,
                    'has_screenshots': bool(step.screenshot_before and step.screenshot_after)
                }
                for step in self.execution_history
            ],
            'screenshots': [
                {
                    'step_index': i,
                    'before': step.screenshot_before,
                    'after': step.screenshot_after
                }
                for i, step in enumerate(self.execution_history)
                if step.screenshot_before or step.screenshot_after
            ]
        }
        
        return report
