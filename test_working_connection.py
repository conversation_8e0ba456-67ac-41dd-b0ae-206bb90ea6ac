#!/usr/bin/env python3
"""
Test the working emulator connection for dashboard use
"""

from appium_driver_enhanced import App<PERSON><PERSON><PERSON>Enhanced

def test_dashboard_ready_connection():
    print("🎯 Testing Dashboard-Ready Connection")
    print("=" * 40)
    
    driver = AppiumDriverEnhanced()
    
    print("📱 Testing emulator-5554 (Pixel_6_API_34)...")
    
    try:
        # Connect
        print("1️⃣ Connecting...")
        success, diagnostics = driver.connect_to_device("emulator-5554")
        
        if not success:
            print(f"❌ Connection failed: {diagnostics.error_message}")
            return False
        
        print("✅ Connected successfully!")
        
        # Test all dashboard operations
        operations = []
        
        # Test screenshot
        print("2️⃣ Testing screenshot...")
        screenshot = driver.take_screenshot()
        if screenshot:
            print(f"✅ Screenshot: {len(screenshot)} chars")
            operations.append("Screenshot ✅")
        else:
            print("❌ Screenshot failed")
            operations.append("Screenshot ❌")
        
        # Test device info
        print("3️⃣ Testing device info...")
        if driver.driver:
            try:
                window_size = driver.driver.get_window_size()
                print(f"✅ Window size: {window_size['width']}x{window_size['height']}")
                operations.append("Device Info ✅")
            except Exception as e:
                print(f"❌ Device info failed: {e}")
                operations.append("Device Info ❌")
        
        # Test page source
        print("4️⃣ Testing page source...")
        try:
            page_source = driver.driver.page_source
            lines = len(page_source.split('\n'))
            print(f"✅ Page source: {lines} lines")
            operations.append("Page Source ✅")
        except Exception as e:
            print(f"❌ Page source failed: {e}")
            operations.append("Page Source ❌")
        
        # Test navigation
        print("5️⃣ Testing navigation...")
        try:
            driver.press_home()
            print("✅ Home button works")
            operations.append("Navigation ✅")
        except Exception as e:
            print(f"❌ Navigation failed: {e}")
            operations.append("Navigation ❌")
        
        # Disconnect
        print("6️⃣ Disconnecting...")
        driver.disconnect()
        print("✅ Disconnected")
        
        # Summary
        print("\n" + "=" * 40)
        print("📊 DASHBOARD READINESS TEST RESULTS")
        print("=" * 40)
        
        for operation in operations:
            print(f"   {operation}")
        
        successful = len([op for op in operations if "✅" in op])
        total = len(operations)
        
        print(f"\n✅ Success Rate: {successful}/{total} ({successful/total*100:.1f}%)")
        
        if successful >= 3:
            print("\n🎉 DASHBOARD READY!")
            print("🌐 Your emulator is ready for dashboard use!")
            return True
        else:
            print("\n⚠️ Some operations need attention")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    success = test_dashboard_ready_connection()
    
    if success:
        print("\n🌟 CONNECTION ISSUE RESOLVED!")
        print("🎯 SOLUTION:")
        print("   • Use emulator-5554 (Pixel_6_API_34)")
        print("   • Avoid emulator-5556 (Copy_of_Pixel_6_API_34)")
        
        print("\n🚀 DASHBOARD INSTRUCTIONS:")
        print("   1. Go to: http://localhost:8501")
        print("   2. Click: 📱 Emulator Testing")
        print("   3. Click: 🔍 Discover Emulators")
        print("   4. Select: Pixel_6_API_34 (emulator-5554)")
        print("   5. Select: Connect to Device")
        print("   6. Click: 🔗 Connect")
        
        print("\n✅ Your dashboard will now connect successfully!")
    else:
        print("\n❌ Connection still has issues")
        print("💡 Try restarting the emulator")

if __name__ == "__main__":
    main()
