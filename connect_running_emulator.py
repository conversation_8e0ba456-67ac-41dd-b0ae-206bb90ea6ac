#!/usr/bin/env python3
"""
Quick script to connect to already-running emulators
Helps resolve connection issues when emulators are running but not detected properly
"""

import subprocess
import time
from appium_driver_enhanced import AppiumDriverEnhanced
from emulator_manager import EmulatorManager

def check_adb_devices():
    """Check what devices are connected via ADB"""
    print("🔍 Checking ADB devices...")
    
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("📱 ADB Devices:")
            lines = result.stdout.strip().split('\n')
            
            devices = []
            for line in lines[1:]:  # Skip header
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        device_id = parts[0]
                        status = parts[1]
                        devices.append((device_id, status))
                        print(f"   • {device_id}: {status}")
            
            return devices
        else:
            print("❌ Failed to get ADB devices")
            return []
    
    except Exception as e:
        print(f"❌ Error checking ADB devices: {str(e)}")
        return []

def test_appium_connection(device_id):
    """Test Appium connection to a specific device"""
    print(f"\n🔌 Testing Appium connection to {device_id}...")
    
    driver = AppiumDriverEnhanced()
    
    # Test connection
    success, diagnostics = driver.connect_to_device(device_id)
    
    if success:
        print(f"✅ Successfully connected to {device_id}")
        
        # Test basic functionality
        print("🧪 Testing basic functionality...")
        tests = driver.test_basic_functionality()
        
        for test_name, result in tests.items():
            status = "✅" if result else "❌"
            print(f"   {status} {test_name.replace('_', ' ').title()}")
        
        # Take a test screenshot
        print("📸 Taking test screenshot...")
        screenshot = driver.take_screenshot()
        if screenshot:
            print("✅ Screenshot captured successfully")
        else:
            print("❌ Screenshot failed")
        
        # Disconnect
        driver.disconnect()
        print("🔌 Disconnected from device")
        
        return True
    
    else:
        print(f"❌ Failed to connect to {device_id}")
        print(f"   Error: {diagnostics.error_message}")
        
        if diagnostics.suggestions:
            print("   💡 Suggestions:")
            for suggestion in diagnostics.suggestions:
                print(f"   • {suggestion}")
        
        return False

def restart_adb():
    """Restart ADB server"""
    print("\n🔄 Restarting ADB server...")
    
    try:
        # Kill ADB server
        subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=10)
        print("   Killed ADB server")
        
        # Start ADB server
        result = subprocess.run(['adb', 'start-server'], capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("   ✅ ADB server restarted successfully")
            return True
        else:
            print(f"   ❌ Failed to start ADB server: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"   ❌ Error restarting ADB: {str(e)}")
        return False

def check_emulator_manager():
    """Check what the emulator manager detects"""
    print("\n📱 Checking Emulator Manager...")
    
    manager = EmulatorManager()
    
    print(f"   ADB Available: {'✅' if manager.is_adb_available() else '❌'}")
    print(f"   Emulator Available: {'✅' if manager.is_emulator_available() else '❌'}")
    
    print("   Discovering emulators...")
    emulators = manager.discover_emulators()
    
    if emulators:
        print(f"   Found {len(emulators)} emulator(s):")
        for emulator in emulators:
            status = "🟢 Running" if emulator.running else "🔴 Stopped"
            print(f"   • {emulator.name} ({emulator.device_name}) - {status}")
            if emulator.running:
                print(f"     ADB ID: {emulator.adb_id}")
                print(f"     Port: {emulator.port}")
    else:
        print("   ❌ No emulators found")
    
    return emulators

def main():
    """Main function"""
    print("🚀 Running Emulator Connection Helper")
    print("=" * 50)
    
    # Step 1: Check ADB devices
    adb_devices = check_adb_devices()
    
    if not adb_devices:
        print("\n❌ No ADB devices found!")
        print("💡 Make sure your emulator is running and try:")
        print("   1. Restart ADB: adb kill-server && adb start-server")
        print("   2. Check emulator status in Android Studio")
        print("   3. Try starting emulator from command line")
        return
    
    # Step 2: Check emulator manager
    emulators = check_emulator_manager()
    
    # Step 3: Test Appium connections
    print("\n🔌 Testing Appium Connections")
    print("=" * 30)
    
    successful_connections = []
    
    for device_id, status in adb_devices:
        if status == 'device':
            if test_appium_connection(device_id):
                successful_connections.append(device_id)
        else:
            print(f"⚠️ Skipping {device_id} (status: {status})")
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 20)
    print(f"ADB Devices Found: {len(adb_devices)}")
    print(f"Emulators Detected: {len(emulators)}")
    print(f"Successful Appium Connections: {len(successful_connections)}")
    
    if successful_connections:
        print("\n✅ Ready for Dashboard Connection!")
        print("You can now connect to these devices in the dashboard:")
        for device_id in successful_connections:
            print(f"   • {device_id}")
        
        print("\n🌐 Open the dashboard: http://localhost:8501")
        print("📱 Go to: Emulator Testing → Emulator Connection")
        print("🔄 Click 'Refresh List' to see your running emulators")
    
    else:
        print("\n❌ No successful connections")
        print("💡 Try these troubleshooting steps:")
        print("   1. Restart ADB server")
        print("   2. Restart your emulator")
        print("   3. Check Appium server is running: appium --port 4723")
        print("   4. Install UiAutomator2 driver: appium driver install uiautomator2")
        
        # Offer to restart ADB
        try:
            response = input("\n🔄 Would you like to restart ADB server? (y/n): ").lower()
            if response == 'y':
                if restart_adb():
                    print("✅ ADB restarted. Try running this script again.")
                else:
                    print("❌ Failed to restart ADB")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
