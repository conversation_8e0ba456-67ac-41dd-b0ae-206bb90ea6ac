"""
RC Testing Dashboard Tab - Simplified Professional Version
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import json
from typing import Dict, List, Any
from rc_testing_engine import RCTestingEngine, RCTestResult

def render_rc_testing_tab():
    """Render the RC Testing dashboard tab"""
    
    # Professional header
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        text-align: center;
    ">
        <h1 style="color: white; margin: 0; font-size: 2.5rem; font-weight: 800;">
            📱 Release Candidate Testing
        </h1>
        <p style="color: rgba(255, 255, 255, 0.9); margin: 0.5rem 0 0 0; font-size: 1.2rem;">
            Enterprise-grade APK analysis and automated validation
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize RC testing engine
    if 'rc_engine' not in st.session_state:
        st.session_state['rc_engine'] = RCTestingEngine()
    
    rc_engine = st.session_state['rc_engine']
    
    # Professional tabs
    rc_tabs = st.tabs([
        "🚀 Upload & Analysis",
        "🛡️ Security Assessment", 
        "⚡ Performance Metrics",
        "📈 Unified Results",
        "📄 Reports & Export"
    ])
    
    # Tab 1: Upload & Analysis
    with rc_tabs[0]:
        st.markdown("## 🚀 APK Upload & Analysis")
        st.markdown("Upload your Android Release Candidate for comprehensive testing")
        
        # APK Upload
        apk_file = st.file_uploader(
            "Choose APK File",
            type=['apk'],
            help="Upload your Android Release Candidate APK file"
        )
        
        if apk_file:
            # File info
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.success("✅ Upload Successful")
            
            with col2:
                st.info(f"📄 **File:** {apk_file.name}")
            
            with col3:
                file_size_mb = len(apk_file.getvalue()) / (1024*1024)
                st.info(f"📦 **Size:** {file_size_mb:.1f} MB")
            
            # Analysis button
            if st.button("🚀 Run Complete RC Analysis", type="primary", use_container_width=True):
                with st.spinner("Performing comprehensive RC analysis..."):
                    # Progress tracking
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # Update progress
                    progress_bar.progress(20)
                    status_text.text("🔍 Extracting APK metadata...")
                    
                    # Perform complete analysis
                    result = rc_engine.perform_complete_rc_analysis(apk_file)
                    
                    progress_bar.progress(100)
                    status_text.text("✅ Analysis complete!")
                    
                    if result:
                        st.session_state['current_rc_result'] = result
                        st.success(f"✅ RC Analysis completed! Test ID: {result.test_id}")
                        
                        # Quick summary
                        col1, col2, col3, col4 = st.columns(4)
                        
                        with col1:
                            status_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌"}
                            st.metric("Test Status", f"{status_emoji.get(result.test_status, '⚪')} {result.test_status}")
                        
                        with col2:
                            st.metric("Overall Score", f"{result.overall_score:.1f}/100")
                        
                        with col3:
                            st.metric("Security Risk", result.security_analysis.risk_level)
                        
                        with col4:
                            st.metric("Performance Grade", result.performance_metrics.performance_grade)
                        
                        # APK Metadata
                        st.markdown("#### 📋 APK Metadata")
                        
                        metadata_cols = st.columns(3)
                        
                        with metadata_cols[0]:
                            st.markdown("**Build Information**")
                            st.write(f"• **Package:** {result.apk_metadata.package_name}")
                            st.write(f"• **Version:** {result.apk_metadata.version_name} ({result.apk_metadata.version_code})")
                            st.write(f"• **Size:** {result.apk_metadata.file_size / (1024*1024):.1f} MB")
                            st.write(f"• **Target SDK:** {result.apk_metadata.target_sdk_version}")
                        
                        with metadata_cols[1]:
                            st.markdown("**Components**")
                            st.write(f"• **Activities:** {len(result.apk_metadata.activities)}")
                            st.write(f"• **Services:** {len(result.apk_metadata.services)}")
                            st.write(f"• **Receivers:** {len(result.apk_metadata.receivers)}")
                            st.write(f"• **Permissions:** {len(result.apk_metadata.permissions)}")
                        
                        with metadata_cols[2]:
                            st.markdown("**Security**")
                            st.write(f"• **Certificate:** {result.apk_metadata.signing_certificate.get('issuer', 'Unknown')}")
                            st.write(f"• **File Hash:** {result.apk_metadata.file_hash[:16]}...")
                            st.write(f"• **Build Time:** {result.apk_metadata.build_timestamp[:19]}")
                    
                    else:
                        st.error("❌ RC Analysis failed. Please try again.")
        
        else:
            st.info("👆 Please upload an APK file to begin RC testing")
    
    # Tab 2: Security Assessment
    with rc_tabs[1]:
        st.markdown("## 🛡️ Security Assessment")
        st.markdown("Comprehensive vulnerability analysis and compliance validation")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            security = result.security_analysis
            
            # Security Overview
            col1, col2, col3 = st.columns(3)
            
            with col1:
                risk_colors = {"LOW": "green", "MEDIUM": "orange", "HIGH": "red"}
                st.markdown(f"""
                <div style="
                    background: {risk_colors.get(security.risk_level, 'gray')};
                    color: white;
                    padding: 1.5rem;
                    border-radius: 10px;
                    text-align: center;
                ">
                    <h3 style="margin: 0;">Risk Level: {security.risk_level}</h3>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                st.metric("Vulnerability Score", f"{security.vulnerability_score:.1f}/10")
            
            with col3:
                cert_status = "✅ Valid" if security.certificate_valid else "❌ Invalid"
                st.metric("Certificate", cert_status)
            
            # Permission Risk Analysis
            st.markdown("#### 🔐 Permission Risk Analysis")
            
            if security.permission_risks:
                risk_data = []
                for risk in security.permission_risks:
                    risk_data.append({
                        "Permission": risk['permission'].split('.')[-1],
                        "Risk Level": risk['risk_level'],
                        "Description": risk['description']
                    })
                
                df_risks = pd.DataFrame(risk_data)
                st.dataframe(df_risks, use_container_width=True)
            else:
                st.success("✅ No high-risk permissions detected")
            
            # Compliance Status
            st.markdown("#### 📋 Security Compliance")
            
            compliance_cols = st.columns(3)
            
            for i, (standard, compliant) in enumerate(security.compliance_status.items()):
                with compliance_cols[i % 3]:
                    status_icon = "✅" if compliant else "❌"
                    st.write(f"{status_icon} **{standard}**")
            
            # Security Recommendations
            st.markdown("#### 💡 Security Recommendations")
            
            for i, recommendation in enumerate(security.security_recommendations, 1):
                st.info(f"{i}. {recommendation}")
        
        else:
            st.info("👆 Please run RC analysis first to view security assessment")
    
    # Tab 3: Performance Metrics
    with rc_tabs[2]:
        st.markdown("## ⚡ Performance Analysis")
        st.markdown("Launch time, frame rate, and resource optimization metrics")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            performance = result.performance_metrics
            
            # Performance Overview
            st.markdown("#### 📊 Performance Overview")
            
            perf_cols = st.columns(4)
            
            with perf_cols[0]:
                st.metric("Performance Grade", performance.performance_grade)
            
            with perf_cols[1]:
                st.metric("App Size", f"{performance.app_size_mb:.1f} MB")
            
            with perf_cols[2]:
                st.metric("Avg Frame Rate", f"{performance.frame_rate_avg:.1f} FPS")
            
            with perf_cols[3]:
                st.metric("Memory Usage", f"{performance.memory_usage_mb:.1f} MB")
            
            # Launch Time Analysis
            st.markdown("#### 🚀 Launch Time Analysis")
            
            launch_data = {
                'Launch Type': ['Cold Start', 'Warm Start', 'Hot Start'],
                'Time (ms)': [
                    performance.launch_time_cold,
                    performance.launch_time_warm,
                    performance.launch_time_hot
                ],
                'Threshold': [3000, 1500, 500]
            }
            
            fig_launch = go.Figure()
            
            fig_launch.add_trace(go.Bar(
                name='Actual Time',
                x=launch_data['Launch Type'],
                y=launch_data['Time (ms)'],
                marker_color=['red' if actual > threshold else 'green' 
                             for actual, threshold in zip(launch_data['Time (ms)'], launch_data['Threshold'])]
            ))
            
            fig_launch.add_trace(go.Scatter(
                name='Threshold',
                x=launch_data['Launch Type'],
                y=launch_data['Threshold'],
                mode='markers+lines',
                line=dict(color='orange', dash='dash'),
                marker=dict(size=8)
            ))
            
            fig_launch.update_layout(
                title="Launch Time Performance",
                yaxis_title="Time (milliseconds)",
                showlegend=True
            )
            
            st.plotly_chart(fig_launch, use_container_width=True)
        
        else:
            st.info("👆 Please run RC analysis first to view performance metrics")
    
    # Tab 4: Unified Results
    with rc_tabs[3]:
        st.markdown("## 📈 Unified RC Testing Results")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            
            # Overall Status
            status_colors = {"PASS": "green", "WARNING": "orange", "FAIL": "red"}
            status_color = status_colors.get(result.test_status, "gray")
            
            st.markdown(f"""
            <div style="
                background: {status_color};
                color: white;
                padding: 2rem;
                border-radius: 12px;
                text-align: center;
                margin: 1rem 0;
            ">
                <h1 style="margin: 0;">RC Status: {result.test_status}</h1>
                <h3 style="margin: 0.5rem 0 0 0;">Overall Score: {result.overall_score:.1f}/100</h3>
            </div>
            """, unsafe_allow_html=True)
            
            # Key Recommendations
            st.markdown("#### 💡 Key Recommendations")
            
            for i, recommendation in enumerate(result.recommendations, 1):
                priority = "🔴 High" if i <= 2 else "🟡 Medium" if i <= 4 else "🟢 Low"
                st.markdown(f"**{priority} Priority:** {recommendation}")
        
        else:
            st.info("👆 Please run RC analysis first to view unified results")
    
    # Tab 5: Reports & Export
    with rc_tabs[4]:
        st.markdown("## 📄 RC Testing Reports")
        
        if 'current_rc_result' in st.session_state:
            result = st.session_state['current_rc_result']
            
            # Report Generation
            st.markdown("#### 📄 Generate RC Reports")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("📊 Executive Summary", use_container_width=True):
                    summary = {
                        "test_status": result.test_status,
                        "overall_score": result.overall_score,
                        "security_risk": result.security_analysis.risk_level,
                        "performance_grade": result.performance_metrics.performance_grade
                    }
                    st.download_button(
                        label="💾 Download Summary",
                        data=json.dumps(summary, indent=2),
                        file_name=f"RC_Summary_{result.test_id}.json",
                        mime="application/json"
                    )
            
            with col2:
                if st.button("🔍 Technical Report", use_container_width=True):
                    technical_report = rc_engine.export_test_result(result.test_id, "json")
                    st.download_button(
                        label="💾 Download Report",
                        data=technical_report,
                        file_name=f"RC_Technical_{result.test_id}.json",
                        mime="application/json"
                    )
            
            with col3:
                if st.button("📈 Performance Report", use_container_width=True):
                    perf_report = {
                        "performance_grade": result.performance_metrics.performance_grade,
                        "app_size_mb": result.performance_metrics.app_size_mb,
                        "launch_times": {
                            "cold": result.performance_metrics.launch_time_cold,
                            "warm": result.performance_metrics.launch_time_warm,
                            "hot": result.performance_metrics.launch_time_hot
                        }
                    }
                    st.download_button(
                        label="💾 Download Performance",
                        data=json.dumps(perf_report, indent=2),
                        file_name=f"RC_Performance_{result.test_id}.json",
                        mime="application/json"
                    )
        
        else:
            st.info("👆 Please run RC analysis first to generate reports")
