#!/usr/bin/env python3
"""
Setup script for Android Emulator Testing Environment
Checks prerequisites and installs required components
"""

import subprocess
import sys
import os
import platform
from pathlib import Path
import json

class EmulatorTestingSetup:
    """Setup and configuration for emulator testing environment"""
    
    def __init__(self):
        self.system = platform.system()
        self.requirements_met = {}
        
    def check_prerequisites(self):
        """Check all prerequisites for emulator testing"""
        print("🔍 Checking prerequisites for Android Emulator Testing...")
        print("=" * 60)
        
        # Check Python version
        self._check_python_version()
        
        # Check Android SDK
        self._check_android_sdk()
        
        # Check ADB
        self._check_adb()
        
        # Check Android Emulator
        self._check_emulator()
        
        # Check Appium
        self._check_appium()
        
        # Check Java
        self._check_java()
        
        # Summary
        self._print_summary()
        
        return all(self.requirements_met.values())
    
    def _check_python_version(self):
        """Check Python version"""
        version = sys.version_info
        required_version = (3, 8)
        
        if version >= required_version:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
            self.requirements_met['python'] = True
        else:
            print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
            self.requirements_met['python'] = False
    
    def _check_android_sdk(self):
        """Check Android SDK installation"""
        sdk_paths = [
            os.environ.get('ANDROID_HOME'),
            os.environ.get('ANDROID_SDK_ROOT'),
            os.path.expanduser('~/Library/Android/sdk'),  # macOS
            os.path.expanduser('~/Android/Sdk'),  # Linux
            'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk'  # Windows
        ]
        
        sdk_found = False
        for path in sdk_paths:
            if path and os.path.exists(path):
                print(f"✅ Android SDK found at: {path}")
                self.requirements_met['android_sdk'] = True
                sdk_found = True
                break
        
        if not sdk_found:
            print("❌ Android SDK not found")
            print("   Please install Android Studio and set ANDROID_HOME environment variable")
            self.requirements_met['android_sdk'] = False
    
    def _check_adb(self):
        """Check ADB availability"""
        try:
            result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                print(f"✅ ADB available: {version}")
                self.requirements_met['adb'] = True
            else:
                print("❌ ADB not working properly")
                self.requirements_met['adb'] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ ADB not found in PATH")
            print("   Please add Android SDK platform-tools to your PATH")
            self.requirements_met['adb'] = False
    
    def _check_emulator(self):
        """Check Android Emulator availability"""
        try:
            result = subprocess.run(['emulator', '-help'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0 or "Android Emulator" in result.stderr:
                print("✅ Android Emulator available")
                self.requirements_met['emulator'] = True
            else:
                print("❌ Android Emulator not working properly")
                self.requirements_met['emulator'] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Android Emulator not found in PATH")
            print("   Please add Android SDK emulator to your PATH")
            self.requirements_met['emulator'] = False
    
    def _check_appium(self):
        """Check Appium server availability"""
        try:
            result = subprocess.run(['appium', '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ Appium server available: {version}")
                self.requirements_met['appium'] = True
            else:
                print("❌ Appium server not working properly")
                self.requirements_met['appium'] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Appium server not found")
            print("   Install with: npm install -g appium")
            print("   Or download from: https://appium.io/")
            self.requirements_met['appium'] = False
    
    def _check_java(self):
        """Check Java availability"""
        try:
            result = subprocess.run(['java', '-version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version_line = result.stderr.split('\n')[0]
                print(f"✅ Java available: {version_line}")
                self.requirements_met['java'] = True
            else:
                print("❌ Java not working properly")
                self.requirements_met['java'] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Java not found")
            print("   Please install Java JDK 8 or higher")
            self.requirements_met['java'] = False
    
    def _print_summary(self):
        """Print setup summary"""
        print("\n" + "=" * 60)
        print("📋 SETUP SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for met in self.requirements_met.values() if met)
        total = len(self.requirements_met)
        
        print(f"Requirements met: {passed}/{total}")
        
        if passed == total:
            print("🎉 All prerequisites are met! You can use emulator testing.")
        else:
            print("⚠️  Some prerequisites are missing. Please install them before using emulator testing.")
            print("\n📝 Installation Guide:")
            self._print_installation_guide()
    
    def _print_installation_guide(self):
        """Print installation guide for missing components"""
        if not self.requirements_met.get('android_sdk', True):
            print("\n🔧 Android SDK Installation:")
            print("   1. Download Android Studio from https://developer.android.com/studio")
            print("   2. Install Android Studio and SDK")
            print("   3. Set ANDROID_HOME environment variable")
        
        if not self.requirements_met.get('appium', True):
            print("\n🔧 Appium Installation:")
            print("   1. Install Node.js from https://nodejs.org/")
            print("   2. Run: npm install -g appium")
            print("   3. Install UiAutomator2 driver: appium driver install uiautomator2")
        
        if not self.requirements_met.get('java', True):
            print("\n🔧 Java Installation:")
            if self.system == "Darwin":  # macOS
                print("   Run: brew install openjdk@11")
            elif self.system == "Linux":
                print("   Run: sudo apt-get install openjdk-11-jdk")
            else:  # Windows
                print("   Download from: https://adoptopenjdk.net/")
    
    def install_python_dependencies(self):
        """Install Python dependencies for emulator testing"""
        print("\n📦 Installing Python dependencies...")
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements_emulator.txt'
            ], check=True)
            print("✅ Python dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Python dependencies: {e}")
            return False
        except FileNotFoundError:
            print("❌ requirements_emulator.txt not found")
            return False
    
    def create_sample_avd(self):
        """Create a sample Android Virtual Device"""
        print("\n📱 Creating sample AVD...")
        
        try:
            # Check if avdmanager is available
            result = subprocess.run(['avdmanager', 'list', 'target'], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print("❌ avdmanager not found. Please install Android SDK command-line tools.")
                return False
            
            # Create AVD
            avd_name = "QA_Test_Emulator"
            target = "android-30"  # Android 11
            
            create_cmd = [
                'avdmanager', 'create', 'avd',
                '-n', avd_name,
                '-k', f'system-images;{target};google_apis;x86_64',
                '--force'
            ]
            
            result = subprocess.run(create_cmd, input='no\n', text=True, capture_output=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ Sample AVD '{avd_name}' created successfully")
                return True
            else:
                print(f"❌ Failed to create AVD: {result.stderr}")
                return False
        
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"❌ Error creating AVD: {e}")
            return False
    
    def start_appium_server(self):
        """Start Appium server in background"""
        print("\n🚀 Starting Appium server...")
        
        try:
            # Check if Appium is already running
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', 4723))
            sock.close()
            
            if result == 0:
                print("✅ Appium server is already running on port 4723")
                return True
            
            # Start Appium server
            subprocess.Popen([
                'appium',
                '--port', '4723',
                '--log-level', 'info'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Wait a moment for server to start
            import time
            time.sleep(3)
            
            # Check if server started
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', 4723))
            sock.close()
            
            if result == 0:
                print("✅ Appium server started successfully on port 4723")
                return True
            else:
                print("❌ Failed to start Appium server")
                return False
        
        except Exception as e:
            print(f"❌ Error starting Appium server: {e}")
            return False
    
    def run_full_setup(self):
        """Run complete setup process"""
        print("🚀 Android Emulator Testing Setup")
        print("=" * 60)
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("\n❌ Setup incomplete. Please resolve the issues above.")
            return False
        
        # Install Python dependencies
        if not self.install_python_dependencies():
            print("\n❌ Failed to install Python dependencies.")
            return False
        
        # Start Appium server
        if not self.start_appium_server():
            print("\n⚠️  Appium server not started. You may need to start it manually.")
        
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Open the QA Analytics Dashboard")
        print("   2. Go to the 'Emulator Testing' tab")
        print("   3. Discover and connect to emulators")
        print("   4. Start testing with AI-powered automation!")
        
        return True

def main():
    """Main setup function"""
    setup = EmulatorTestingSetup()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "check":
            setup.check_prerequisites()
        elif command == "install":
            setup.install_python_dependencies()
        elif command == "avd":
            setup.create_sample_avd()
        elif command == "appium":
            setup.start_appium_server()
        else:
            print("Usage: python setup_emulator_testing.py [check|install|avd|appium]")
    else:
        setup.run_full_setup()

if __name__ == "__main__":
    main()
