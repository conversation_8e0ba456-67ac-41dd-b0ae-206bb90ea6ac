<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Advanced HAR File Comparison Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .upload-box {
            border: 3px dashed #3498db;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .upload-box:hover {
            border-color: #2980b9;
            background: #e3f2fd;
            transform: translateY(-2px);
        }

        .upload-box.active {
            border-color: #27ae60;
            background: #e8f5e8;
        }

        .upload-box input[type="file"] {
            display: none;
        }

        .upload-label {
            cursor: pointer;
            display: block;
            font-size: 1.2rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #3498db;
        }

        .file-info {
            margin-top: 15px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 10px;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .controls-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .control-group {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .control-group h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .filter-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            background: #e9ecef;
        }

        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }

        .compare-button {
            width: 100%;
            padding: 20px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.3rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 30px 0;
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }

        .compare-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(39, 174, 96, 0.4);
        }

        .compare-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-section {
            display: none;
            margin-top: 40px;
        }

        .results-section.show {
            display: block;
        }

        .results-tabs {
            display: flex;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 30px;
        }

        .tab-button {
            flex: 1;
            padding: 15px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #2c3e50;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #3498db;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .metric-change {
            font-size: 0.9rem;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .metric-change.positive {
            background: #d5f4e6;
            color: #27ae60;
        }

        .metric-change.negative {
            background: #ffeaa7;
            color: #e17055;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .comparison-table th {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-success {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-error {
            background: #ffeaa7;
            color: #e17055;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .loading {
            text-align: center;
            padding: 60px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #ecf0f1;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .export-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .export-btn.json {
            background: #3498db;
            color: white;
        }

        .export-btn.csv {
            background: #27ae60;
            color: white;
        }

        .export-btn.pdf {
            background: #e74c3c;
            color: white;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .upload-section {
                grid-template-columns: 1fr;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Advanced HAR File Comparison</h1>
            <p>Compare Charles Proxy logs and HAR files with comprehensive analysis and filtering</p>
        </div>

        <div class="main-content">
            <!-- File Upload Section -->
            <div class="upload-section">
                <div class="upload-box" id="upload1">
                    <div class="upload-icon">📁</div>
                    <label for="file1" class="upload-label">
                        <strong>Upload First HAR File</strong><br>
                        <small>Supports .har, .json, .chlsj formats</small>
                    </label>
                    <input type="file" id="file1" accept=".har,.json,.chlsj">
                    <div class="file-info" id="info1">
                        <strong id="name1"></strong><br>
                        <small id="details1"></small>
                    </div>
                </div>

                <div class="upload-box" id="upload2">
                    <div class="upload-icon">📁</div>
                    <label for="file2" class="upload-label">
                        <strong>Upload Second HAR File</strong><br>
                        <small>Supports .har, .json, .chlsj formats</small>
                    </label>
                    <input type="file" id="file2" accept=".har,.json,.chlsj">
                    <div class="file-info" id="info2">
                        <strong id="name2"></strong><br>
                        <small id="details2"></small>
                    </div>
                </div>
            </div>
