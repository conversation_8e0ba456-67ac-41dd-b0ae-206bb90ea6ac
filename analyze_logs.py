#!/usr/bin/env python3
import os
import sys
import json
import argparse
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime

class LogAnalyzer:
    """Local log analyzer for Charles Proxy logs"""
    
    def __init__(self):
        """Initialize the analyzer"""
        pass
    
    def load_data(self, file_path: str) -> Dict:
        """Load JSON data from file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"Error loading file: {str(e)}")
            return {}
    
    def create_dataframe(self, data: Dict) -> pd.DataFrame:
        """Create a DataFrame from log entries"""
        # Handle different data formats
        entries = []
        
        if "entries" in data:
            # Detailed or raw format
            entries = data.get("entries", [])
        elif isinstance(data, dict) and "total_entries" in data:
            # Summary format - no entries to display
            print("This is a summary file. It contains statistics but no detailed entries.")
            return pd.DataFrame()
        elif isinstance(data, list):
            # Direct list of entries
            entries = data
        
        if not entries:
            return pd.DataFrame()
        
        # Convert entries to DataFrame
        df = pd.DataFrame(entries)
        
        # Ensure required columns exist
        for col in ["host", "status", "duration"]:
            if col not in df.columns:
                df[col] = None
        
        # If duration is present, add duration categories
        if "duration" in df.columns:
            df["duration_ms"] = pd.to_numeric(df["duration"], errors="coerce")
            # Create duration categories
            bins = [0, 100, 500, 1000, 5000, float('inf')]
            labels = ['<100ms', '100-500ms', '500ms-1s', '1s-5s', '>5s']
            df["duration_category"] = pd.cut(df["duration_ms"], bins=bins, labels=labels)
        
        return df
    
    def analyze(self, df: pd.DataFrame) -> List[str]:
        """Analyze log data and return insights"""
        insights = []
        
        # Skip analysis if dataframe is empty
        if df.empty:
            return ["No data available for analysis."]
        
        # Basic statistics
        insights.append(f"📊 Total Requests: {len(df)}")
        
        # Analyze hosts
        if "host" in df.columns:
            unique_hosts = df["host"].nunique()
            insights.append(f"🌐 Unique Hosts: {unique_hosts}")
            
            # Top hosts
            top_hosts = df["host"].value_counts().head(5)
            insights.append("\n🔝 Top 5 Hosts:")
            for host, count in top_hosts.items():
                insights.append(f"  - {host}: {count} requests ({count/len(df)*100:.1f}%)")
        
        # Analyze status codes
        if "status" in df.columns:
            status_counts = df["status"].value_counts()
            
            # Success rate
            success_count = sum(status_counts.get(code, 0) for code in range(200, 300))
            success_rate = success_count / len(df) * 100 if len(df) > 0 else 0
            insights.append(f"\n✅ Success Rate: {success_rate:.1f}%")
            
            # Error analysis
            client_errors = sum(status_counts.get(code, 0) for code in range(400, 500))
            server_errors = sum(status_counts.get(code, 0) for code in range(500, 600))
            
            if client_errors > 0:
                insights.append(f"⚠️ Client Errors (4xx): {client_errors} requests ({client_errors/len(df)*100:.1f}%)")
            
            if server_errors > 0:
                insights.append(f"❌ Server Errors (5xx): {server_errors} requests ({server_errors/len(df)*100:.1f}%)")
            
            # Most common status codes
            insights.append("\n📋 Status Code Distribution:")
            for status, count in status_counts.head(10).items():
                insights.append(f"  - {status}: {count} requests ({count/len(df)*100:.1f}%)")
        
        # Analyze performance
        if "duration" in df.columns and "duration_ms" in df.columns:
            avg_duration = df["duration_ms"].mean()
            max_duration = df["duration_ms"].max()
            min_duration = df["duration_ms"].min()
            
            insights.append(f"\n⏱️ Performance Metrics:")
            insights.append(f"  - Average Duration: {avg_duration:.2f} ms")
            insights.append(f"  - Maximum Duration: {max_duration:.2f} ms")
            insights.append(f"  - Minimum Duration: {min_duration:.2f} ms")
            
            # Slow requests
            slow_threshold = 1000  # 1 second
            slow_requests = df[df["duration_ms"] > slow_threshold]
            slow_count = len(slow_requests)
            
            if slow_count > 0:
                slow_pct = (slow_count / len(df)) * 100
                insights.append(f"  - Slow Requests (>1s): {slow_count} ({slow_pct:.1f}%)")
                
                if not slow_requests.empty and "host" in slow_requests.columns:
                    # Find hosts with slow requests
                    slow_hosts = slow_requests["host"].value_counts().head(3)
                    insights.append("  - Top Hosts with Slow Requests:")
                    for host, count in slow_hosts.items():
                        insights.append(f"    * {host}: {count} slow requests")
        
        # Analyze request methods
        if "method" in df.columns:
            method_counts = df["method"].value_counts()
            
            insights.append(f"\n🔄 Request Methods:")
            for method, count in method_counts.items():
                insights.append(f"  - {method}: {count} requests ({count/len(df)*100:.1f}%)")
        
        # Analyze content types
        if "response_headers" in df.columns:
            # Extract content types from response headers
            content_types = []
            for headers in df["response_headers"].dropna():
                if isinstance(headers, dict) and "Content-Type" in headers:
                    content_type = headers["Content-Type"].split(";")[0].strip()
                    content_types.append(content_type)
            
            if content_types:
                content_type_counts = pd.Series(content_types).value_counts().head(5)
                
                insights.append(f"\n📦 Top Content Types:")
                for content_type, count in content_type_counts.items():
                    insights.append(f"  - {content_type}: {count} responses")
        
        return insights

def main():
    parser = argparse.ArgumentParser(description="Analyze Charles Proxy logs")
    parser.add_argument("file_path", help="Path to the Charles log JSON file")
    parser.add_argument("--output", "-o", help="Output file for analysis results")
    
    args = parser.parse_args()
    
    # Check if file exists
    if not os.path.exists(args.file_path):
        print(f"Error: File '{args.file_path}' not found")
        sys.exit(1)
    
    # Create analyzer
    analyzer = LogAnalyzer()
    
    # Load data
    print(f"Loading data from {args.file_path}...")
    data = analyzer.load_data(args.file_path)
    
    if not data:
        print("Error: No data found or invalid JSON format")
        sys.exit(1)
    
    # Create DataFrame
    df = analyzer.create_dataframe(data)
    
    if df.empty:
        print("Error: No entries found in the log file or unsupported format")
        sys.exit(1)
    
    # Analyze data
    print("Analyzing log data...")
    insights = analyzer.analyze(df)
    
    # Output results
    if args.output:
        try:
            with open(args.output, "w") as f:
                f.write(f"# Charles Log Analysis\n")
                f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"File: {os.path.basename(args.file_path)}\n\n")
                f.write("\n".join(insights))
            print(f"Analysis saved to {args.output}")
        except Exception as e:
            print(f"Error saving analysis to file: {str(e)}")
            print("\nAnalysis Results:")
            for insight in insights:
                print(insight)
    else:
        print("\nAnalysis Results:")
        print("=" * 80)
        for insight in insights:
            print(insight)
        print("=" * 80)

if __name__ == "__main__":
    main()
