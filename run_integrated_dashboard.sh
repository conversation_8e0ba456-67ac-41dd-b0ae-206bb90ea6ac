#!/bin/bash

# Set API key if provided as argument
if [ ! -z "$1" ]; then
    export ANTHROPIC_API_KEY="$1"
    echo "API key set from argument"
elif [ -z "$ANTHROPIC_API_KEY" ]; then
    echo "Warning: ANTHROPIC_API_KEY environment variable not set."
    echo "You will need to enter your API key in the dashboard."
fi

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "Creating virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
fi

# Install dependencies
echo "Installing required packages..."
pip install streamlit pandas plotly numpy

# Run the dashboard
echo "Starting Integrated Claude Log Analysis Dashboard..."
streamlit run integrated_claude_dashboard.py
