#!/usr/bin/env python3
"""
Test connection to specific emulator-5554 which we know was working
"""

from appium_driver_enhanced import AppiumDriverEnhanced

def main():
    print("🔌 Testing Connection to emulator-5554")
    print("=" * 40)
    
    driver = AppiumDriverEnhanced()
    
    # Test connection to emulator-5554 specifically
    print("Connecting to emulator-5554...")
    success, diagnostics = driver.connect_to_device("emulator-5554")
    
    if success:
        print("✅ Connection successful!")
        
        # Test functionality
        print("Testing functionality...")
        tests = driver.test_basic_functionality()
        
        for test_name, result in tests.items():
            status = "✅" if result else "❌"
            print(f"   {status} {test_name.replace('_', ' ').title()}")
        
        # Take screenshot
        print("Taking screenshot...")
        screenshot = driver.take_screenshot()
        if screenshot:
            print(f"✅ Screenshot captured ({len(screenshot)} chars)")
        else:
            print("❌ Screenshot failed")
        
        # Disconnect
        driver.disconnect()
        print("✅ Disconnected")
        
        print("\n🎉 EMULATOR CONNECTION WORKING!")
        print("Your dashboard should now work properly.")
        
    else:
        print("❌ Connection failed!")
        print(f"Error: {diagnostics.error_message}")
        
        if diagnostics.suggestions:
            print("Suggestions:")
            for suggestion in diagnostics.suggestions:
                print(f"• {suggestion}")

if __name__ == "__main__":
    main()
