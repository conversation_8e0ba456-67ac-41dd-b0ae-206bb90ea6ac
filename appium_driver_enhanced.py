"""
Enhanced Appium WebDriver Wrapper with Comprehensive Connection Diagnostics
Handles all mobile automation actions with robust error handling and user feedback
"""

import time
import base64
import socket
import subprocess
import requests
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import json

try:
    from appium import webdriver
    from appium.webdriver.common.touch_action import TouchAction
    from appium.webdriver.common.multi_action import MultiAction
    APPIUM_AVAILABLE = True
except ImportError:
    APPIUM_AVAILABLE = False
    # Mock classes for when Appium is not available
    class webdriver:
        class Remote:
            def __init__(self, *args, **kwargs):
                pass
    
    class TouchAction:
        def __init__(self, *args, **kwargs):
            pass
    
    class MultiAction:
        def __init__(self, *args, **kwargs):
            pass

@dataclass
class ConnectionDiagnostics:
    """Diagnostics information for connection troubleshooting"""
    appium_server_running: bool
    appium_server_url: str
    adb_available: bool
    device_connected: bool
    device_id: str
    error_message: str
    suggestions: List[str]

@dataclass
class ElementInfo:
    """Information about a UI element"""
    id: str
    class_name: str
    text: str
    content_desc: str
    bounds: Dict[str, int]
    clickable: bool
    enabled: bool
    visible: bool

@dataclass
class TestAction:
    """Represents a test action"""
    action_type: str
    element_locator: Optional[str] = None
    text_input: Optional[str] = None
    coordinates: Optional[Tuple[int, int]] = None
    duration: Optional[int] = None
    description: str = ""

class AppiumDriverEnhanced:
    """Enhanced Appium WebDriver wrapper with comprehensive diagnostics and error handling"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Remote] = None
        self.wait: Optional[WebDriverWait] = None
        self.connected_device: Optional[str] = None
        self.capabilities: Dict[str, Any] = {}
        self.appium_server_url = 'http://localhost:4723'
        self.last_error: Optional[str] = None
        
    def check_appium_server(self) -> bool:
        """Check if Appium server is running"""
        try:
            response = requests.get(f"{self.appium_server_url}/status", timeout=5)
            return response.status_code == 200
        except (requests.RequestException, requests.ConnectionError):
            return False
    
    def check_adb_connectivity(self, device_id: str) -> bool:
        """Check if device is connected via ADB"""
        try:
            result = subprocess.run(
                ['adb', 'devices'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                devices = result.stdout
                return device_id in devices and 'device' in devices
            return False
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
    
    def diagnose_connection(self, device_id: str) -> ConnectionDiagnostics:
        """Perform comprehensive connection diagnostics"""
        diagnostics = ConnectionDiagnostics(
            appium_server_running=False,
            appium_server_url=self.appium_server_url,
            adb_available=False,
            device_connected=False,
            device_id=device_id,
            error_message="",
            suggestions=[]
        )
        
        # Check Appium server
        diagnostics.appium_server_running = self.check_appium_server()
        if not diagnostics.appium_server_running:
            diagnostics.error_message = "Appium server is not running"
            diagnostics.suggestions.extend([
                "Start Appium server: 'appium --port 4723'",
                "Install Appium: 'npm install -g appium'",
                "Install UiAutomator2 driver: 'appium driver install uiautomator2'"
            ])
        
        # Check ADB availability
        try:
            subprocess.run(['adb', 'version'], capture_output=True, timeout=5)
            diagnostics.adb_available = True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            diagnostics.adb_available = False
            diagnostics.error_message = "ADB is not available"
            diagnostics.suggestions.extend([
                "Install Android SDK",
                "Add platform-tools to PATH",
                "Verify ADB with: 'adb version'"
            ])
        
        # Check device connectivity
        if diagnostics.adb_available:
            diagnostics.device_connected = self.check_adb_connectivity(device_id)
            if not diagnostics.device_connected:
                diagnostics.error_message = f"Device {device_id} is not connected via ADB"
                diagnostics.suggestions.extend([
                    f"Check if emulator {device_id} is running",
                    "Verify device with: 'adb devices'",
                    "Restart ADB: 'adb kill-server && adb start-server'"
                ])
        
        return diagnostics
    
    def connect_to_device(self, device_id: str, app_package: str = None, 
                         app_activity: str = None, additional_caps: Dict[str, Any] = None) -> Tuple[bool, ConnectionDiagnostics]:
        """Connect to an Android device/emulator with comprehensive diagnostics"""
        
        # Reset previous state
        self.last_error = None
        
        # Check if Appium is available
        if not APPIUM_AVAILABLE:
            diagnostics = ConnectionDiagnostics(
                appium_server_running=False,
                appium_server_url=self.appium_server_url,
                adb_available=False,
                device_connected=False,
                device_id=device_id,
                error_message="Appium Python client is not installed",
                suggestions=[
                    "Install Appium client: 'pip install Appium-Python-Client'",
                    "Install Selenium: 'pip install selenium'"
                ]
            )
            return False, diagnostics
        
        # Perform diagnostics
        diagnostics = self.diagnose_connection(device_id)
        
        # If diagnostics fail, return early
        if not diagnostics.appium_server_running or not diagnostics.adb_available or not diagnostics.device_connected:
            return False, diagnostics
        
        try:
            # Default capabilities for Android
            desired_caps = {
                'platformName': 'Android',
                'deviceName': device_id,
                'udid': device_id,
                'automationName': 'UiAutomator2',
                'newCommandTimeout': 300,
                'noReset': True,
                'fullReset': False,
                'unicodeKeyboard': True,
                'resetKeyboard': True,
                'skipServerInstallation': True,  # Skip server installation for faster connection
                'skipDeviceInitialization': False,
                'ignoreHiddenApiPolicyError': True  # Ignore hidden API policy errors
            }
            
            # Add app-specific capabilities
            if app_package:
                desired_caps['appPackage'] = app_package
            if app_activity:
                desired_caps['appActivity'] = app_activity
            else:
                # Don't launch any app by default
                desired_caps['autoLaunch'] = False
            
            # Add additional capabilities
            if additional_caps:
                desired_caps.update(additional_caps)
            
            # Connect to Appium server (using new options format)
            from appium.options.android import UiAutomator2Options

            options = UiAutomator2Options()
            options.platform_name = desired_caps['platformName']
            options.device_name = desired_caps['deviceName']
            options.udid = desired_caps['udid']
            options.automation_name = desired_caps['automationName']
            options.new_command_timeout = desired_caps['newCommandTimeout']
            options.no_reset = desired_caps['noReset']
            options.full_reset = desired_caps['fullReset']
            options.unicode_keyboard = desired_caps['unicodeKeyboard']
            options.reset_keyboard = desired_caps['resetKeyboard']
            options.skip_server_installation = desired_caps['skipServerInstallation']
            options.skip_device_initialization = desired_caps['skipDeviceInitialization']
            options.ignore_hidden_api_policy_error = desired_caps['ignoreHiddenApiPolicyError']

            if 'appPackage' in desired_caps:
                options.app_package = desired_caps['appPackage']
            if 'appActivity' in desired_caps:
                options.app_activity = desired_caps['appActivity']
            if 'autoLaunch' in desired_caps:
                options.auto_launch = desired_caps['autoLaunch']

            self.driver = webdriver.Remote(
                command_executor=self.appium_server_url,  # Appium 2.x doesn't need /wd/hub
                options=options
            )
            
            # Test the connection by getting device info
            try:
                device_info = self.driver.get_window_size()
                if device_info:
                    self.wait = WebDriverWait(self.driver, 10)
                    self.connected_device = device_id
                    self.capabilities = desired_caps
                    
                    # Update diagnostics for successful connection
                    diagnostics.error_message = ""
                    diagnostics.suggestions = ["Connection successful!"]
                    
                    return True, diagnostics
            except Exception as test_error:
                self.last_error = f"Connection test failed: {str(test_error)}"
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                
                diagnostics.error_message = self.last_error
                diagnostics.suggestions.extend([
                    "Device may be locked or in sleep mode",
                    "Try unlocking the device",
                    "Restart the emulator"
                ])
                return False, diagnostics
        
        except Exception as e:
            self.last_error = f"Failed to connect to device {device_id}: {str(e)}"
            
            # Enhanced error analysis
            error_str = str(e).lower()
            if "connection refused" in error_str:
                diagnostics.error_message = "Connection refused - Appium server may not be running"
                diagnostics.suggestions = [
                    "Start Appium server: 'appium --port 4723'",
                    "Check if port 4723 is available",
                    "Try restarting Appium server"
                ]
            elif "timeout" in error_str:
                diagnostics.error_message = "Connection timeout - Device may be unresponsive"
                diagnostics.suggestions = [
                    "Check if emulator is fully booted",
                    "Increase connection timeout",
                    "Restart the emulator"
                ]
            elif "session not created" in error_str:
                diagnostics.error_message = "Session creation failed - Driver compatibility issue"
                diagnostics.suggestions = [
                    "Install UiAutomator2 driver: 'appium driver install uiautomator2'",
                    "Update Appium server",
                    "Check device API level compatibility"
                ]
            else:
                diagnostics.error_message = self.last_error
                diagnostics.suggestions = [
                    "Check Appium server logs for details",
                    "Verify device capabilities",
                    "Try connecting manually with Appium Inspector"
                ]
            
            return False, diagnostics
    
    def disconnect(self):
        """Disconnect from the device"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
            finally:
                self.driver = None
                self.wait = None
                self.connected_device = None
    
    def is_connected(self) -> bool:
        """Check if connected to a device"""
        if not self.driver:
            return False
        
        try:
            # Test connection by getting window size
            self.driver.get_window_size()
            return True
        except Exception:
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get detailed connection status"""
        return {
            'connected': self.is_connected(),
            'device_id': self.connected_device,
            'appium_available': APPIUM_AVAILABLE,
            'appium_server_running': self.check_appium_server(),
            'last_error': self.last_error,
            'capabilities': self.capabilities
        }
    
    def test_basic_functionality(self) -> Dict[str, bool]:
        """Test basic device functionality"""
        if not self.is_connected():
            return {'connected': False}
        
        tests = {
            'connected': True,
            'screenshot': False,
            'window_size': False,
            'page_source': False,
            'device_info': False
        }
        
        try:
            # Test screenshot
            screenshot = self.driver.get_screenshot_as_base64()
            tests['screenshot'] = bool(screenshot)
        except:
            pass
        
        try:
            # Test window size
            size = self.driver.get_window_size()
            tests['window_size'] = bool(size)
        except:
            pass
        
        try:
            # Test page source
            source = self.driver.page_source
            tests['page_source'] = bool(source)
        except:
            pass
        
        try:
            # Test device info
            info = self.driver.capabilities
            tests['device_info'] = bool(info)
        except:
            pass
        
        return tests
    
    # Basic Actions (simplified versions for testing)
    def take_screenshot(self) -> Optional[str]:
        """Take screenshot and return as base64 string"""
        if not self.driver:
            return None
        
        try:
            return self.driver.get_screenshot_as_base64()
        except Exception as e:
            self.last_error = f"Screenshot failed: {str(e)}"
            return None
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get device information"""
        if not self.driver:
            return {}
        
        try:
            return {
                'platform_name': self.driver.capabilities.get('platformName'),
                'platform_version': self.driver.capabilities.get('platformVersion'),
                'device_name': self.driver.capabilities.get('deviceName'),
                'udid': self.driver.capabilities.get('udid'),
                'screen_size': self.driver.get_window_size(),
                'orientation': getattr(self.driver, 'orientation', 'unknown')
            }
        except Exception as e:
            self.last_error = f"Device info failed: {str(e)}"
            return {}
    
    def tap(self, x: int, y: int, duration: int = 100) -> bool:
        """Tap at specific coordinates"""
        if not self.driver:
            return False
        
        try:
            TouchAction(self.driver).tap(x=x, y=y).wait(duration).perform()
            return True
        except Exception as e:
            self.last_error = f"Tap failed: {str(e)}"
            return False
    
    def press_home(self) -> bool:
        """Press device home button"""
        if not self.driver:
            return False
        
        try:
            self.driver.press_keycode(3)  # HOME key
            return True
        except Exception as e:
            self.last_error = f"Home button failed: {str(e)}"
            return False
    
    def press_back(self) -> bool:
        """Press device back button"""
        if not self.driver:
            return False
        
        try:
            self.driver.back()
            return True
        except Exception as e:
            self.last_error = f"Back button failed: {str(e)}"
            return False
