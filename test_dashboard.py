import streamlit as st

st.set_page_config(
    page_title="QA Analytics Dashboard",
    page_icon="📊",
    layout="wide"
)

st.title("QA Analytics Dashboard - Professional UI")
st.write("Testing basic functionality...")

# Test tabs
tabs = st.tabs(["Dashboard", "Mobile Testing", "Test Generator"])

with tabs[0]:
    st.header("Dashboard")
    st.write("Main dashboard content")
    
with tabs[1]:
    st.header("Mobile Testing Platform")
    st.write("Professional mobile testing interface")
    
with tabs[2]:
    st.header("Test Generator")
    st.write("Professional test generation tools")

st.success("Dashboard is working correctly!")
