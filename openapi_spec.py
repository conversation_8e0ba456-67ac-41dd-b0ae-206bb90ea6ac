"""
OpenAPI/Swagger Specification Generator for QA Analytics Dashboard
Generates comprehensive API documentation with interactive Swagger UI
"""

import json
import yaml
from datetime import datetime
from typing import Dict, Any, List
import streamlit as st

class OpenAPISpecGenerator:
    """Generate OpenAPI 3.0 specification for QA Analytics Dashboard"""

    def __init__(self):
        self.spec_version = "3.0.3"
        self.api_version = "1.0.0"
        self.title = "QA Analytics Dashboard API"
        self.description = "Comprehensive API for QA automation, log analysis, and AI-powered test generation"

    def generate_openapi_spec(self) -> Dict[str, Any]:
        """Generate complete OpenAPI 3.0 specification"""

        spec = {
            "openapi": self.spec_version,
            "info": {
                "title": self.title,
                "description": self.description,
                "version": self.api_version,
                "contact": {
                    "name": "QA Analytics Dashboard",
                    "url": "http://localhost:8501",
                    "email": "<EMAIL>"
                },
                "license": {
                    "name": "MIT",
                    "url": "https://opensource.org/licenses/MIT"
                }
            },
            "servers": [
                {
                    "url": "http://localhost:8501/api/v1",
                    "description": "Local development server"
                },
                {
                    "url": "https://qa-dashboard.company.com/api/v1",
                    "description": "Production server"
                }
            ],
            "paths": self._generate_paths(),
            "components": self._generate_components(),
            "tags": self._generate_tags()
        }

        return spec

    def _generate_paths(self) -> Dict[str, Any]:
        """Generate API endpoint paths"""

        return {
            "/logs/upload": {
                "post": {
                    "tags": ["Log Management"],
                    "summary": "Upload Charles Proxy log files",
                    "description": "Upload and process Charles Proxy log files for analysis",
                    "operationId": "uploadLogFiles",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "multipart/form-data": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "files": {
                                            "type": "array",
                                            "items": {
                                                "type": "string",
                                                "format": "binary"
                                            },
                                            "description": "Charles Proxy log files (.chlsj, .har, .json)"
                                        },
                                        "domain_filter": {
                                            "type": "string",
                                            "description": "Filter logs by specific domain"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Files uploaded and processed successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/LogUploadResponse"}
                                }
                            }
                        },
                        "400": {"$ref": "#/components/responses/BadRequest"},
                        "500": {"$ref": "#/components/responses/InternalError"}
                    }
                }
            },
            "/logs/analyze": {
                "post": {
                    "tags": ["Log Analysis"],
                    "summary": "Analyze uploaded log data",
                    "description": "Perform comprehensive analysis on uploaded log files",
                    "operationId": "analyzeLogs",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/AnalysisRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Analysis completed successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/AnalysisResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/logs/compare": {
                "post": {
                    "tags": ["Log Analysis"],
                    "summary": "Compare two log files",
                    "description": "Side-by-side comparison of two Charles Proxy log files",
                    "operationId": "compareLogs",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ComparisonRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Comparison completed successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/ComparisonResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/karate/analyze": {
                "post": {
                    "tags": ["Karate Test Generator"],
                    "summary": "Analyze APIs for test opportunities",
                    "description": "Identify test case opportunities from API log data",
                    "operationId": "analyzeForTests",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/TestAnalysisRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Test opportunities identified",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/TestOpportunitiesResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/karate/generate": {
                "post": {
                    "tags": ["Karate Test Generator"],
                    "summary": "Generate Karate test cases",
                    "description": "Generate AI-powered Karate framework test cases",
                    "operationId": "generateKarateTests",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/TestGenerationRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Test cases generated successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/TestGenerationResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/karate/project/scan": {
                "post": {
                    "tags": ["Karate Test Generator"],
                    "summary": "Scan IntelliJ project for patterns",
                    "description": "Analyze existing Karate tests to learn patterns",
                    "operationId": "scanProject",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ProjectScanRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Project scanned successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/ProjectScanResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/security/scan": {
                "post": {
                    "tags": ["Security Testing"],
                    "summary": "Perform security vulnerability scan",
                    "description": "Analyze API logs for security vulnerabilities",
                    "operationId": "securityScan",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/SecurityScanRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Security scan completed",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/SecurityScanResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/reports/generate": {
                "post": {
                    "tags": ["QA Reports"],
                    "summary": "Generate comprehensive QA report",
                    "description": "Create professional QA reports with charts and analysis",
                    "operationId": "generateReport",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ReportGenerationRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Report generated successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/ReportResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/reports/export": {
                "get": {
                    "tags": ["QA Reports"],
                    "summary": "Export report in various formats",
                    "description": "Download reports as PDF, Excel, or other formats",
                    "operationId": "exportReport",
                    "parameters": [
                        {
                            "name": "report_id",
                            "in": "query",
                            "required": True,
                            "schema": {"type": "string"},
                            "description": "Report identifier"
                        },
                        {
                            "name": "format",
                            "in": "query",
                            "required": True,
                            "schema": {
                                "type": "string",
                                "enum": ["pdf", "excel", "json"]
                            },
                            "description": "Export format"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Report exported successfully",
                            "content": {
                                "application/pdf": {"schema": {"type": "string", "format": "binary"}},
                                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                                    "schema": {"type": "string", "format": "binary"}
                                },
                                "application/json": {"schema": {"$ref": "#/components/schemas/ReportData"}}
                            }
                        }
                    }
                }
            },
            "/knowledge-base/sessions": {
                "get": {
                    "tags": ["Knowledge Base"],
                    "summary": "List analysis sessions",
                    "description": "Retrieve all stored analysis sessions",
                    "operationId": "listSessions",
                    "responses": {
                        "200": {
                            "description": "Sessions retrieved successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/SessionsResponse"}
                                }
                            }
                        }
                    }
                },
                "post": {
                    "tags": ["Knowledge Base"],
                    "summary": "Create new analysis session",
                    "description": "Store a new analysis session with patterns and insights",
                    "operationId": "createSession",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/SessionCreateRequest"}
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "description": "Session created successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/SessionResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/ai/analyze": {
                "post": {
                    "tags": ["AI Analysis"],
                    "summary": "AI-powered log analysis",
                    "description": "Use Claude AI for intelligent log analysis and insights",
                    "operationId": "aiAnalysis",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/AIAnalysisRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "AI analysis completed",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/AIAnalysisResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/figma/validate": {
                "post": {
                    "tags": ["Figma Design Validation"],
                    "summary": "Validate Figma design against screenshot",
                    "description": "Perform pixel-perfect comparison and design validation",
                    "operationId": "validateFigmaDesign",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/FigmaValidationRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Validation completed successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/FigmaValidationResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/figma/upload": {
                "post": {
                    "tags": ["Figma Design Validation"],
                    "summary": "Upload Figma design file",
                    "description": "Upload and parse Figma design files for validation",
                    "operationId": "uploadFigmaFile",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "multipart/form-data": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "file": {
                                            "type": "string",
                                            "format": "binary",
                                            "description": "Figma design file (.fig, .json)"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "File uploaded and parsed successfully",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/FigmaFileResponse"}
                                }
                            }
                        }
                    }
                }
            },
            "/figma/design-system/validate": {
                "post": {
                    "tags": ["Figma Design Validation"],
                    "summary": "Validate design system compliance",
                    "description": "Check compliance with design system standards",
                    "operationId": "validateDesignSystem",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/DesignSystemValidationRequest"}
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Design system validation completed",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": "#/components/schemas/DesignSystemValidationResponse"}
                                }
                            }
                        }
                    }
                }
            }
        }

    def _generate_components(self) -> Dict[str, Any]:
        """Generate OpenAPI components (schemas, responses, etc.)"""

        return {
            "schemas": {
                "LogUploadResponse": {
                    "type": "object",
                    "properties": {
                        "success": {"type": "boolean"},
                        "message": {"type": "string"},
                        "files_processed": {"type": "integer"},
                        "total_requests": {"type": "integer"},
                        "processing_time": {"type": "number"}
                    }
                },
                "AnalysisRequest": {
                    "type": "object",
                    "properties": {
                        "log_data_id": {"type": "string"},
                        "analysis_type": {
                            "type": "string",
                            "enum": ["basic", "detailed", "security", "performance"]
                        },
                        "filters": {
                            "type": "object",
                            "properties": {
                                "domain": {"type": "string"},
                                "status_codes": {"type": "array", "items": {"type": "integer"}},
                                "date_range": {
                                    "type": "object",
                                    "properties": {
                                        "start": {"type": "string", "format": "date-time"},
                                        "end": {"type": "string", "format": "date-time"}
                                    }
                                }
                            }
                        }
                    },
                    "required": ["log_data_id", "analysis_type"]
                },
                "AnalysisResponse": {
                    "type": "object",
                    "properties": {
                        "analysis_id": {"type": "string"},
                        "summary": {
                            "type": "object",
                            "properties": {
                                "total_requests": {"type": "integer"},
                                "unique_endpoints": {"type": "integer"},
                                "error_rate": {"type": "number"},
                                "avg_response_time": {"type": "number"}
                            }
                        },
                        "detailed_metrics": {"type": "object"},
                        "recommendations": {"type": "array", "items": {"type": "string"}}
                    }
                },
                "ComparisonRequest": {
                    "type": "object",
                    "properties": {
                        "file1_id": {"type": "string"},
                        "file2_id": {"type": "string"},
                        "comparison_type": {
                            "type": "string",
                            "enum": ["endpoints", "responses", "performance", "full"]
                        }
                    },
                    "required": ["file1_id", "file2_id"]
                },
                "ComparisonResponse": {
                    "type": "object",
                    "properties": {
                        "comparison_id": {"type": "string"},
                        "differences": {"type": "array", "items": {"type": "object"}},
                        "similarities": {"type": "array", "items": {"type": "object"}},
                        "summary": {"type": "object"}
                    }
                },
                "TestAnalysisRequest": {
                    "type": "object",
                    "properties": {
                        "log_data_id": {"type": "string"},
                        "priority_threshold": {"type": "number", "minimum": 0, "maximum": 1},
                        "test_types": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["functional", "performance", "security", "error_handling"]
                            }
                        }
                    },
                    "required": ["log_data_id"]
                },
                "TestOpportunitiesResponse": {
                    "type": "object",
                    "properties": {
                        "opportunities": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "endpoint": {"type": "string"},
                                    "methods": {"type": "array", "items": {"type": "string"}},
                                    "priority": {"type": "number"},
                                    "complexity": {"type": "string"},
                                    "test_scenarios": {"type": "array", "items": {"type": "string"}}
                                }
                            }
                        },
                        "total_opportunities": {"type": "integer"},
                        "high_priority_count": {"type": "integer"}
                    }
                },
                "TestGenerationRequest": {
                    "type": "object",
                    "properties": {
                        "endpoint": {"type": "string"},
                        "methods": {"type": "array", "items": {"type": "string"}},
                        "use_ai": {"type": "boolean", "default": True},
                        "custom_requirements": {"type": "string"},
                        "project_path": {"type": "string"},
                        "test_name": {"type": "string"}
                    },
                    "required": ["endpoint", "methods"]
                },
                "TestGenerationResponse": {
                    "type": "object",
                    "properties": {
                        "test_content": {"type": "string"},
                        "file_path": {"type": "string"},
                        "test_scenarios": {"type": "array", "items": {"type": "string"}},
                        "generation_method": {"type": "string", "enum": ["ai", "local"]},
                        "success": {"type": "boolean"}
                    }
                },
                "ProjectScanRequest": {
                    "type": "object",
                    "properties": {
                        "project_path": {"type": "string"},
                        "test_directory": {"type": "string", "default": "src/test/java"}
                    },
                    "required": ["project_path"]
                },
                "ProjectScanResponse": {
                    "type": "object",
                    "properties": {
                        "patterns_found": {"type": "object"},
                        "test_files_count": {"type": "integer"},
                        "naming_conventions": {"type": "array", "items": {"type": "string"}},
                        "common_patterns": {"type": "object"}
                    }
                },
                "SecurityScanRequest": {
                    "type": "object",
                    "properties": {
                        "log_data_id": {"type": "string"},
                        "scan_depth": {"type": "string", "enum": ["basic", "comprehensive"]},
                        "vulnerability_types": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["log_data_id"]
                },
                "SecurityScanResponse": {
                    "type": "object",
                    "properties": {
                        "vulnerabilities": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {"type": "string"},
                                    "severity": {"type": "string"},
                                    "description": {"type": "string"},
                                    "recommendation": {"type": "string"}
                                }
                            }
                        },
                        "risk_score": {"type": "number"},
                        "compliance_status": {"type": "object"}
                    }
                },
                "ReportGenerationRequest": {
                    "type": "object",
                    "properties": {
                        "analysis_id": {"type": "string"},
                        "report_type": {"type": "string", "enum": ["executive", "technical", "security"]},
                        "include_charts": {"type": "boolean", "default": True},
                        "company_name": {"type": "string"},
                        "project_name": {"type": "string"}
                    },
                    "required": ["analysis_id", "report_type"]
                },
                "ReportResponse": {
                    "type": "object",
                    "properties": {
                        "report_id": {"type": "string"},
                        "download_url": {"type": "string"},
                        "format": {"type": "string"},
                        "generated_at": {"type": "string", "format": "date-time"}
                    }
                },
                "ReportData": {
                    "type": "object",
                    "properties": {
                        "metadata": {"type": "object"},
                        "summary": {"type": "object"},
                        "detailed_analysis": {"type": "object"},
                        "charts_data": {"type": "object"}
                    }
                },
                "SessionsResponse": {
                    "type": "object",
                    "properties": {
                        "sessions": {
                            "type": "array",
                            "items": {"$ref": "#/components/schemas/SessionResponse"}
                        },
                        "total_count": {"type": "integer"}
                    }
                },
                "SessionCreateRequest": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "analysis_data": {"type": "object"},
                        "patterns": {"type": "object"}
                    },
                    "required": ["name"]
                },
                "SessionResponse": {
                    "type": "object",
                    "properties": {
                        "session_id": {"type": "string"},
                        "name": {"type": "string"},
                        "created_at": {"type": "string", "format": "date-time"},
                        "last_modified": {"type": "string", "format": "date-time"},
                        "status": {"type": "string"}
                    }
                },
                "AIAnalysisRequest": {
                    "type": "object",
                    "properties": {
                        "log_data": {"type": "object"},
                        "analysis_prompt": {"type": "string"},
                        "focus_areas": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["log_data"]
                },
                "AIAnalysisResponse": {
                    "type": "object",
                    "properties": {
                        "insights": {"type": "array", "items": {"type": "string"}},
                        "recommendations": {"type": "array", "items": {"type": "string"}},
                        "patterns_identified": {"type": "object"},
                        "confidence_score": {"type": "number"}
                    }
                },
                "FigmaValidationRequest": {
                    "type": "object",
                    "properties": {
                        "figma_file_data": {"type": "string", "description": "Base64 encoded Figma file data"},
                        "screenshot_data": {"type": "string", "description": "Base64 encoded screenshot data"},
                        "validation_settings": {
                            "type": "object",
                            "properties": {
                                "color_tolerance": {"type": "integer", "minimum": 1, "maximum": 50, "default": 10},
                                "pixel_threshold": {"type": "number", "minimum": 0.8, "maximum": 1.0, "default": 0.95},
                                "accessibility_level": {"type": "string", "enum": ["AA", "AAA"], "default": "AA"}
                            }
                        }
                    },
                    "required": ["screenshot_data"]
                },
                "FigmaValidationResponse": {
                    "type": "object",
                    "properties": {
                        "validation_id": {"type": "string"},
                        "overall_score": {"type": "number", "minimum": 0, "maximum": 1},
                        "pixel_accuracy": {"type": "number", "minimum": 0, "maximum": 1},
                        "color_accuracy": {"type": "number", "minimum": 0, "maximum": 1},
                        "layout_accuracy": {"type": "number", "minimum": 0, "maximum": 1},
                        "accessibility_score": {"type": "number", "minimum": 0, "maximum": 1},
                        "differences_count": {"type": "integer"},
                        "color_mismatches": {"type": "integer"},
                        "recommendations": {"type": "array", "items": {"type": "string"}}
                    }
                },
                "FigmaFileResponse": {
                    "type": "object",
                    "properties": {
                        "file_id": {"type": "string"},
                        "filename": {"type": "string"},
                        "size": {"type": "integer"},
                        "design_tokens": {
                            "type": "object",
                            "properties": {
                                "colors": {"type": "integer"},
                                "typography_styles": {"type": "integer"},
                                "components": {"type": "integer"},
                                "spacing_tokens": {"type": "integer"}
                            }
                        },
                        "parsed_at": {"type": "string", "format": "date-time"}
                    }
                },
                "DesignSystemValidationRequest": {
                    "type": "object",
                    "properties": {
                        "figma_file_id": {"type": "string"},
                        "screenshot_data": {"type": "string"},
                        "design_system_rules": {"type": "object"}
                    },
                    "required": ["screenshot_data"]
                },
                "DesignSystemValidationResponse": {
                    "type": "object",
                    "properties": {
                        "component_consistency": {"type": "number", "minimum": 0, "maximum": 1},
                        "typography_compliance": {"type": "number", "minimum": 0, "maximum": 1},
                        "color_compliance": {"type": "number", "minimum": 0, "maximum": 1},
                        "spacing_compliance": {"type": "number", "minimum": 0, "maximum": 1},
                        "overall_score": {"type": "number", "minimum": 0, "maximum": 1},
                        "violations": {"type": "array", "items": {"type": "string"}},
                        "recommendations": {"type": "array", "items": {"type": "string"}},
                        "validated_at": {"type": "string", "format": "date-time"}
                    }
                },
                "Error": {
                    "type": "object",
                    "properties": {
                        "error": {"type": "string"},
                        "message": {"type": "string"},
                        "code": {"type": "integer"},
                        "timestamp": {"type": "string", "format": "date-time"}
                    }
                }
            },
            "responses": {
                "BadRequest": {
                    "description": "Bad request - invalid parameters",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                },
                "NotFound": {
                    "description": "Resource not found",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                },
                "InternalError": {
                    "description": "Internal server error",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/Error"}
                        }
                    }
                }
            },
            "securitySchemes": {
                "ApiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "X-API-Key"
                },
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT"
                }
            }
        }

    def _generate_tags(self) -> List[Dict[str, str]]:
        """Generate API tags for organization"""

        return [
            {
                "name": "Log Management",
                "description": "Upload and manage Charles Proxy log files"
            },
            {
                "name": "Log Analysis",
                "description": "Analyze and compare API log data"
            },
            {
                "name": "Karate Test Generator",
                "description": "AI-powered Karate framework test generation"
            },
            {
                "name": "Security Testing",
                "description": "Security vulnerability scanning and assessment"
            },
            {
                "name": "QA Reports",
                "description": "Generate and export comprehensive QA reports"
            },
            {
                "name": "Knowledge Base",
                "description": "Manage analysis sessions and patterns"
            },
            {
                "name": "AI Analysis",
                "description": "Claude AI-powered intelligent analysis"
            },
            {
                "name": "Figma Design Validation",
                "description": "Pixel-perfect design validation and comparison"
            }
        ]

    def export_as_json(self) -> str:
        """Export OpenAPI specification as JSON string"""
        spec = self.generate_openapi_spec()
        return json.dumps(spec, indent=2)

    def export_as_yaml(self) -> str:
        """Export OpenAPI specification as YAML string"""
        spec = self.generate_openapi_spec()
        return yaml.dump(spec, default_flow_style=False, sort_keys=False)

    def save_to_file(self, filepath: str, format: str = "json") -> bool:
        """Save OpenAPI specification to file"""
        try:
            if format.lower() == "json":
                content = self.export_as_json()
            elif format.lower() == "yaml":
                content = self.export_as_yaml()
            else:
                raise ValueError("Format must be 'json' or 'yaml'")

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            st.error(f"Error saving OpenAPI spec: {str(e)}")
            return False

    def generate_postman_collection(self) -> Dict[str, Any]:
        """Generate Postman collection from OpenAPI spec"""
        spec = self.generate_openapi_spec()

        collection = {
            "info": {
                "name": spec["info"]["title"],
                "description": spec["info"]["description"],
                "version": spec["info"]["version"],
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "item": [],
            "variable": [
                {
                    "key": "baseUrl",
                    "value": "http://localhost:8501/api/v1",
                    "type": "string"
                }
            ]
        }

        # Convert OpenAPI paths to Postman requests
        for path, methods in spec["paths"].items():
            folder = {
                "name": path.replace("/", "_").strip("_"),
                "item": []
            }

            for method, details in methods.items():
                request_item = {
                    "name": details.get("summary", f"{method.upper()} {path}"),
                    "request": {
                        "method": method.upper(),
                        "header": [
                            {
                                "key": "Content-Type",
                                "value": "application/json"
                            }
                        ],
                        "url": {
                            "raw": "{{baseUrl}}" + path,
                            "host": ["{{baseUrl}}"],
                            "path": path.strip("/").split("/") if path != "/" else []
                        }
                    },
                    "response": []
                }

                # Add request body if present
                if "requestBody" in details:
                    request_item["request"]["body"] = {
                        "mode": "raw",
                        "raw": json.dumps({}, indent=2)
                    }

                # Add query parameters if present
                if "parameters" in details:
                    query_params = []
                    for param in details["parameters"]:
                        if param.get("in") == "query":
                            query_params.append({
                                "key": param["name"],
                                "value": "",
                                "description": param.get("description", "")
                            })
                    if query_params:
                        request_item["request"]["url"]["query"] = query_params

                folder["item"].append(request_item)

            collection["item"].append(folder)

        return collection

    def generate_sdk_info(self) -> Dict[str, Any]:
        """Generate SDK generation information"""
        return {
            "python": {
                "name": "qa-analytics-python-sdk",
                "description": "Python SDK for QA Analytics Dashboard API",
                "generator": "python",
                "package_name": "qa_analytics_sdk",
                "example_usage": '''
from qa_analytics_sdk import QAAnalyticsClient

client = QAAnalyticsClient(base_url="http://localhost:8501/api/v1")

# Upload log files
response = client.logs.upload(files=["log1.chlsj", "log2.har"])

# Generate Karate tests
tests = client.karate.generate(endpoint="/api/users", methods=["GET", "POST"])
'''
            },
            "javascript": {
                "name": "qa-analytics-js-sdk",
                "description": "JavaScript/Node.js SDK for QA Analytics Dashboard API",
                "generator": "javascript",
                "package_name": "qa-analytics-sdk",
                "example_usage": '''
const QAAnalyticsClient = require('qa-analytics-sdk');

const client = new QAAnalyticsClient({
  baseURL: 'http://localhost:8501/api/v1'
});

// Upload log files
const response = await client.logs.upload({
  files: ['log1.chlsj', 'log2.har']
});

// Generate Karate tests
const tests = await client.karate.generate({
  endpoint: '/api/users',
  methods: ['GET', 'POST']
});
'''
            },
            "java": {
                "name": "qa-analytics-java-sdk",
                "description": "Java SDK for QA Analytics Dashboard API",
                "generator": "java",
                "package_name": "com.qaanalytics.sdk",
                "example_usage": '''
import com.qaanalytics.sdk.QAAnalyticsClient;
import com.qaanalytics.sdk.model.*;

QAAnalyticsClient client = new QAAnalyticsClient("http://localhost:8501/api/v1");

// Upload log files
LogUploadResponse response = client.logs().upload(
    Arrays.asList("log1.chlsj", "log2.har")
);

// Generate Karate tests
TestGenerationResponse tests = client.karate().generate(
    new TestGenerationRequest()
        .endpoint("/api/users")
        .methods(Arrays.asList("GET", "POST"))
);
'''
            },
            "csharp": {
                "name": "qa-analytics-csharp-sdk",
                "description": "C# SDK for QA Analytics Dashboard API",
                "generator": "csharp-netcore",
                "package_name": "QAAnalytics.SDK",
                "example_usage": '''
using QAAnalytics.SDK;
using QAAnalytics.SDK.Model;

var client = new QAAnalyticsClient("http://localhost:8501/api/v1");

// Upload log files
var response = await client.Logs.UploadAsync(new[] { "log1.chlsj", "log2.har" });

// Generate Karate tests
var tests = await client.Karate.GenerateAsync(new TestGenerationRequest
{
    Endpoint = "/api/users",
    Methods = new[] { "GET", "POST" }
});
'''
            }
        }