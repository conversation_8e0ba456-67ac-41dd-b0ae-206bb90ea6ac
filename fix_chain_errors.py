#!/usr/bin/env python3
"""
Fix chain testing errors and ensure it works
"""

import sys
import traceback

def check_dashboard_status():
    """Check if dashboard is running"""
    print("🔍 Checking Dashboard Status...")
    
    try:
        import requests
        response = requests.get("http://localhost:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard is running at http://localhost:8501")
            return True
        else:
            print(f"❌ Dashboard returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Dashboard not running at http://localhost:8501")
        return False
    except Exception as e:
        print(f"❌ Error checking dashboard: {e}")
        return False

def check_imports():
    """Check if all required imports work"""
    print("\n🔍 Checking Imports...")
    
    imports_to_test = [
        ("streamlit", "st"),
        ("emulator_testing_tab", "render_emulator_testing_tab"),
        ("chain_testing_interface", "render_chain_testing_interface"),
        ("test_chain_system", "TestChainExecutor"),
        ("appium_driver_enhanced", "AppiumDriverEnhanced"),
        ("emulator_manager", "EmulatorManager")
    ]
    
    failed_imports = []
    
    for module, item in imports_to_test:
        try:
            if item:
                exec(f"from {module} import {item}")
            else:
                exec(f"import {module}")
            print(f"✅ {module}.{item if item else ''}")
        except Exception as e:
            print(f"❌ {module}.{item if item else ''}: {e}")
            failed_imports.append((module, item, str(e)))
    
    return failed_imports

def check_file_existence():
    """Check if all required files exist"""
    print("\n🔍 Checking Required Files...")
    
    import os
    required_files = [
        "emulator_testing_tab.py",
        "chain_testing_interface.py", 
        "test_chain_system.py",
        "appium_driver_enhanced.py",
        "emulator_manager.py"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    return missing_files

def test_chain_system():
    """Test chain system functionality"""
    print("\n🔍 Testing Chain System...")
    
    try:
        from test_chain_system import TestChainExecutor, ChainStepType
        from appium_driver_enhanced import AppiumDriverEnhanced
        
        # Test basic functionality
        driver = AppiumDriverEnhanced()
        executor = TestChainExecutor(driver)
        
        # Create test chain
        chain = executor.create_chain("Test", "Test chain")
        executor.add_step(ChainStepType.WAIT, "Test step", {"duration": 1})
        
        print("✅ Chain system working")
        return True
        
    except Exception as e:
        print(f"❌ Chain system error: {e}")
        print(f"   Details: {traceback.format_exc()}")
        return False

def start_dashboard():
    """Start the dashboard if not running"""
    print("\n🚀 Starting Dashboard...")
    
    import subprocess
    import time
    
    try:
        # Check if run_full_stack.sh exists
        import os
        if os.path.exists("run_full_stack.sh"):
            print("Starting with run_full_stack.sh...")
            subprocess.Popen(["./run_full_stack.sh"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        else:
            print("Starting with streamlit directly...")
            subprocess.Popen(["./kb_venv/bin/streamlit", "run", "dashboard.py", "--server.port", "8501"], 
                           stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print("⏳ Waiting for dashboard to start...")
        time.sleep(10)
        
        # Check if it started
        if check_dashboard_status():
            print("✅ Dashboard started successfully")
            return True
        else:
            print("❌ Dashboard failed to start")
            return False
            
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        return False

def create_simple_chain_test():
    """Create a simple test to verify chain functionality"""
    print("\n🧪 Creating Simple Chain Test...")
    
    test_code = '''
import streamlit as st
from test_chain_system import TestChainExecutor, ChainStepType
from appium_driver_enhanced import AppiumDriverEnhanced

st.title("🔗 Chain Testing Verification")

try:
    # Initialize components
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    st.success("✅ Components initialized successfully")
    
    # Create test chain
    if st.button("Create Test Chain"):
        chain = executor.create_chain("Test Chain", "Simple test")
        executor.add_step(ChainStepType.WAIT, "Wait 1 second", {"duration": 1})
        
        st.success(f"✅ Created chain: {chain.name}")
        st.write(f"Steps: {len(chain.steps)}")
        
        # Generate code
        code = executor._generate_appium_code(chain)
        st.code(code[:500] + "..." if len(code) > 500 else code, language="python")
        
except Exception as e:
    st.error(f"❌ Error: {str(e)}")
    st.code(str(e))
'''
    
    with open("test_chain_simple.py", "w") as f:
        f.write(test_code)
    
    print("✅ Created test_chain_simple.py")
    print("🌐 Run with: streamlit run test_chain_simple.py --server.port 8503")

def main():
    """Main diagnostic and fix function"""
    print("🔧 FIXING CHAIN TESTING ERRORS")
    print("=" * 35)
    
    # Step 1: Check dashboard
    dashboard_running = check_dashboard_status()
    
    # Step 2: Check imports
    failed_imports = check_imports()
    
    # Step 3: Check files
    missing_files = check_file_existence()
    
    # Step 4: Test chain system
    chain_working = test_chain_system()
    
    # Step 5: Create simple test
    create_simple_chain_test()
    
    # Summary and fixes
    print("\n" + "=" * 35)
    print("🎯 DIAGNOSIS SUMMARY")
    print("=" * 35)
    
    if not dashboard_running:
        print("❌ Dashboard not running")
        print("💡 Fix: Run './run_full_stack.sh' or start dashboard manually")
    
    if failed_imports:
        print("❌ Import errors found:")
        for module, item, error in failed_imports:
            print(f"   • {module}.{item}: {error}")
        print("💡 Fix: Check Python environment and file paths")
    
    if missing_files:
        print("❌ Missing files:")
        for file in missing_files:
            print(f"   • {file}")
        print("💡 Fix: Ensure all files are in current directory")
    
    if not chain_working:
        print("❌ Chain system not working")
        print("💡 Fix: Check error details above")
    
    # Provide solutions
    print("\n🔧 QUICK FIXES:")
    
    if not dashboard_running:
        print("\n1️⃣ Start Dashboard:")
        print("   ./run_full_stack.sh")
        print("   OR")
        print("   ./kb_venv/bin/streamlit run dashboard.py --server.port 8501")
    
    print("\n2️⃣ Test Simple Chain:")
    print("   ./kb_venv/bin/streamlit run test_chain_simple.py --server.port 8503")
    
    print("\n3️⃣ Access Chain Testing:")
    print("   1. Go to: http://localhost:8501")
    print("   2. Tab: 📱 Emulator Testing")
    print("   3. Discover emulators and select one")
    print("   4. Look for 'Testing Mode' section")
    print("   5. Select '🔗 Chain Testing'")
    
    if all([dashboard_running or not failed_imports, not missing_files, chain_working]):
        print("\n✅ CHAIN TESTING SHOULD BE WORKING!")
        print("Follow the access steps above.")
    else:
        print("\n⚠️ ISSUES FOUND - Fix the problems above first")

if __name__ == "__main__":
    main()
