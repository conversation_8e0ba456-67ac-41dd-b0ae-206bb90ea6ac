<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Paytm Dashboard API Comparison</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">
    <!-- html2pdf library for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>
    <style>
    body {
      background-color: #f4f7f9;
      font-family: 'Arial', sans-serif;
    }
    .container {
      max-width: 1200px;
    }
    .summary-card {
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      color: white;
      font-size: 18px;
      font-weight: bold;
    }
    .total {
      background: #007bff;
    }
    .match {
      background: #28a745;
    }
    .mismatch {
      background: #dc3545;
    }
    .api-card {
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 12px;
      box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: 0.3s;
    }
    .api-card:hover {
      transform: scale(1.02);
    }
    .api-header {
      padding: 10px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .api-details {
      display: none;
      padding: 10px;
      border-top: 2px solid #ddd;
      background: #fff;
      margin-top: 10px;
      border-radius: 5px;
      max-height: 400px;
      overflow-y: auto;
    }
    .response-container {
      display: flex;
      gap: 10px;
    }
    .response-box {
      width: 50%;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: auto;
      background: #fdfdfd;
      padding: 10px;
    }
    pre {
      white-space: pre-wrap;
      color: #333;
      overflow: auto;
    }
    .match-card {
      background-color: #28a745;
      color: white;
    }
    .mismatch-card {
      background-color: #ff7675;
      color: white;
    }
    .mismatch-keys {
      color: #dc3545;
      font-weight: bold;
    }
    .highlight {
      background-color: yellow;
      font-weight: bold;
    }
    #loading {
      display: none;
      text-align: center;
      font-size: 18px;
      color: #007bff;
      margin-top: 20px;
    }
    #resultSection {
      display: none;
    }
    /* Attractive Search Bar Styling */
    #searchBar {
      border: 2px solid #007bff;
      border-radius: 25px;
      padding: 10px 20px;
      font-size: 16px;
      transition: border-color 0.3s;
    }
    #searchBar:focus {
      border-color: #0056b3;
      outline: none;
    }
    /* Attractive Export JSON and Report Button Styling */
    .export-json-btn,
    .generate-report-btn {
      background: linear-gradient(45deg, #28a745, #218838);
      border: none;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      border-radius: 25px;
      padding: 10px 20px;
      transition: transform 0.3s;
      margin: 5px;
    }
    .export-json-btn:hover,
    .generate-report-btn:hover {
      transform: scale(1.05);
    }
    /* Report Container: Initially hidden but rendered for PDF generation */
    #reportContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 800px;
      opacity: 0;
      pointer-events: none;
    }
    /* Custom Modal Styles */
    .custom-modal {
      display: none;
      position: fixed;
      z-index: 1050;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0,0,0,0.4);
    }
    
    .custom-modal-content {
      background-color: #fefefe;
      margin: 10% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 80%;
      max-width: 800px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .custom-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 10px;
      margin-bottom: 15px;
    }
    
    .custom-close {
      color: #aaa;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
    
    .custom-close:hover {
      color: black;
    }
    
    .alert {
      padding: 15px;
      margin-bottom: 20px;
      border: 1px solid transparent;
      border-radius: 4px;
    }
    
    .alert-success {
      color: #155724;
      background-color: #d4edda;
      border-color: #c3e6cb;
    }
    
    .alert-warning {
      color: #856404;
      background-color: #fff3cd;
      border-color: #ffeeba;
    }
  </style>
</head>
<body>
<div class="container mt-4">
    <h1 class="text-center text-primary">Paytm Dashboard API Response Comparison</h1>

    <div class="row mt-4">
        <div class="col-md-6">
            <label class="form-label">Upload First HAR File:</label>
            <input type="file" id="harFile1" class="form-control" accept=".har" />
        </div>
        <div class="col-md-6">
            <label class="form-label">Upload Second HAR File:</label>
            <input type="file" id="harFile2" class="form-control" accept=".har" />
        </div>
    </div>

    <button class="btn btn-primary mt-3 w-100" onclick="compareHARFiles()">
      <i class="bi bi-arrow-repeat"></i> Compare
    </button>

    <div id="loading">Processing files, please wait...</div>

    <div id="resultSection">
        <!-- Search Bar -->
        <div class="row mt-4">
            <div class="col-md-12">
                <input type="text" id="searchBar" class="form-control" placeholder="Search by URL or text..." />
            </div>
        </div>

        <!-- Filter and Sort Options -->
        <div class="row mt-3">
            <div class="col-md-4">
                <select id="filterType" class="form-select">
                    <option value="all">All Requests</option>
                    <option value="passed">Passed Only</option>
                    <option value="failed">Failed Only</option>
                </select>
            </div>
            <div class="col-md-4">
                <select id="sortBy" class="form-select">
                    <option value="url">Sort by URL</option>
                    <option value="status">Sort by Status</option>
                </select>
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="applyFilters()">Apply Filters</button>
            </div>
        </div>

        <!-- Export and Report Buttons -->
        <div class="row mt-3">
            <div class="col-md-6 text-center">
                <button class="export-json-btn" onclick="exportToJSON()">
                  <i class="bi bi-file-earmark-code"></i> Export as JSON
                </button>
            </div>
            <div class="col-md-6 text-center">
                <button class="generate-report-btn" onclick="generateReport()">
                  <i class="bi bi-file-pdf"></i> Generate Report
                </button>
            </div>
        </div>

        <!-- Add a new button for AI analysis -->
        <div class="row mt-3">
            <div class="col-md-4 text-center">
                <button class="btn btn-info" onclick="analyzeWithAI()">
                  <i class="bi bi-robot"></i> AI Analysis
                </button>
            </div>
            <div class="col-md-4 text-center">
                <button class="btn btn-info" onclick="analyzeWithExternalAI()">
                  <i class="bi bi-robot"></i> AI Analysis
                </button>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="summary-card total">Total Requests: <span id="totalRequests">0</span></div>
            </div>
            <div class="col-md-4">
                <div class="summary-card match">✅ Passed Responses: <span id="matchedResponses">0</span></div>
            </div>
            <div class="col-md-4">
                <div class="summary-card mismatch">❌ Failed Responses: <span id="mismatchedResponses">0</span></div>
            </div>
        </div>

        <!-- API Comparison Sections -->
        <h3 class="mt-4 text-dark">✅ Passed Section</h3>
        <div id="passedResults"></div>

        <h3 class="mt-4 text-dark">❌ Failed Section</h3>
        <div id="failedResults"></div>
    </div>
</div>

<!-- Report Container (initially hidden with opacity 0) -->
<div id="reportContainer">
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1 style="text-align: center; color: #007bff;">Automated Test Report</h1>
        <p><strong>Date:</strong> <span id="reportDate"></span></p>
        <h3>Summary</h3>
        <table border="1" cellspacing="0" cellpadding="8" width="100%">
            <tr>
                <th>Total Requests</th>
                <th>Passed</th>
                <th>Failed</th>
            </tr>
            <tr>
                <td id="reportTotal"></td>
                <td id="reportPassed"></td>
                <td id="reportFailed"></td>
            </tr>
        </table>
        <h3 style="margin-top: 20px;">Detailed Results</h3>
        <table border="1" cellspacing="0" cellpadding="8" width="100%">
            <tr>
                <th>URL</th>
                <th>Status</th>
                <th>Discrepancies</th>
            </tr>
            <tbody id="reportDetails"></tbody>
        </table>
    </div>
</div>

<!-- Add a modal for AI analysis results -->
<div id="aiAnalysisModal" class="custom-modal">
  <div class="custom-modal-content">
    <div class="custom-modal-header">
      <h5>AI Analysis Results</h5>
      <span class="custom-close">&times;</span>
    </div>
    <div class="custom-modal-body">
      <div id="aiAnalysisResults">
        <p>Processing your comparison data...</p>
      </div>
    </div>
  </div>
</div>

<script>
    // Global array to store comparison results for export/report
    let comparisonResults = [];

    function compareHARFiles() {
      const file1 = document.getElementById('harFile1').files[0];
      const file2 = document.getElementById('harFile2').files[0];
      if (!file1 || !file2) {
        alert("Please upload both HAR files.");
        return;
      }
      // Reset comparison results on each new comparison
      comparisonResults = [];
      document.getElementById('loading').style.display = 'block';
      document.getElementById('resultSection').style.display = 'none';

      const reader1 = new FileReader();
      const reader2 = new FileReader();

      reader1.onload = function (event1) {
        reader2.onload = function (event2) {
          const harData1 = JSON.parse(event1.target.result);
          const harData2 = JSON.parse(event2.target.result);
          processComparison(harData1, harData2);
        };
        reader2.readAsText(file2);
      };
      reader1.readAsText(file1);
    }

    function processComparison(har1, har2) {
      const ignoredUrls = [
        "dashboard.paytm.com/next/p4b",
        "dashboard.paytm.com/next/micro/common",
        "google-analytics.com",
        "googletagmanager.com",
        "doubleclick.net",
        "facebook.com",
        "cdn.jsdelivr.net"
      ];

      const filterUrls = entry =>
        entry.request.url.includes("dashboard.paytm.com") &&
        !ignoredUrls.some(url => entry.request.url.includes(url));

      const paytmEntries1 = har1.log.entries.filter(filterUrls);
      const paytmEntries2 = har2.log.entries.filter(filterUrls);

      let totalRequests = 0;
      let matchedResponses = 0;
      let mismatchedResponses = 0;

      const passedResults = document.getElementById("passedResults");
      const failedResults = document.getElementById("failedResults");
      passedResults.innerHTML = "";
      failedResults.innerHTML = "";
      const seenUrls = new Set();

      paytmEntries1.forEach(entry1 => {
        const url = entry1.request.url;
        if (seenUrls.has(url)) return;
        seenUrls.add(url);
        totalRequests++;

        const matchingEntry2 = paytmEntries2.find(entry => entry.request.url === url);
        const response1 = decodeResponse(entry1.response.content.text);
        const response2 = matchingEntry2 ? decodeResponse(matchingEntry2.response.content.text) : "Not Found";

        const { mismatchKeys, highlightedResponse1, highlightedResponse2 } = getHighlightedMismatchedKeys(response1, response2);
        const schemaStatus = mismatchKeys.length === 0 ? "✅ Passed" : "❌ Failed";
        const schemaClass = mismatchKeys.length === 0 ? "match-card" : "mismatch-card";

        // Store comparison data for export/report
        comparisonResults.push({
          url: url,
          schemaStatus: schemaStatus,
          mismatchKeys: mismatchKeys,
          response1: response1,
          response2: response2
        });

        const apiBlock = `
          <div class='api-card ${schemaClass}' onclick='toggleDetails("details-${btoa(url)}")'>
            <div class='api-header'>
              ${url} <i>${schemaStatus}</i>
            </div>
            <div id='details-${btoa(url)}' class='api-details'>
              <div class='response-container'>
                <div class='response-box'>
                  <strong>Response 1:</strong>
                  <pre>${highlightedResponse1}</pre>
                </div>
                <div class='response-box'>
                  <strong>Response 2:</strong>
                  <pre>${highlightedResponse2}</pre>
                </div>
              </div>
              ${mismatchKeys.length > 0 ? `<strong>Mismatched Keys:</strong> <div class='mismatch-keys'>${mismatchKeys.join(', ')}</div>` : ''}
            </div>
          </div>
        `;

        if (mismatchKeys.length === 0) {
          matchedResponses++;
          passedResults.innerHTML += apiBlock;
        } else {
          mismatchedResponses++;
          failedResults.innerHTML += apiBlock;
        }
      });

      document.getElementById("totalRequests").textContent = totalRequests;
      document.getElementById("matchedResponses").textContent = matchedResponses;
      document.getElementById("mismatchedResponses").textContent = mismatchedResponses;

      document.getElementById('loading').style.display = 'none';
      document.getElementById('resultSection').style.display = 'block';
    }

    // Improve the diff visualization
    function getHighlightedMismatchedKeys(res1, res2) {
      try {
        const obj1 = JSON.parse(res1);
        const obj2 = JSON.parse(res2);
        const mismatches = [];
        const addedKeys = [];
        const removedKeys = [];

        function compareObjects(o1, o2, prefix = '') {
          // Find modified and removed keys
          Object.keys(o1).forEach(key => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            if (!(key in o2)) {
              removedKeys.push(fullKey);
            } else if (JSON.stringify(o1[key]) !== JSON.stringify(o2[key])) {
              mismatches.push(fullKey);
            }
            if (typeof o1[key] === 'object' && o2[key] && typeof o2[key] === 'object') {
              compareObjects(o1[key], o2[key], fullKey);
            }
          });

          // Find added keys
          Object.keys(o2).forEach(key => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            if (!(key in o1)) {
              addedKeys.push(fullKey);
            }
          });
        }

        compareObjects(obj1, obj2);

        // Create more detailed diff visualization
        const highlightedResponse1 = highlightMismatches(obj1, mismatches, removedKeys, []);
        const highlightedResponse2 = highlightMismatches(obj2, mismatches, [], addedKeys);

        return {
          mismatchKeys: mismatches,
          addedKeys: addedKeys,
          removedKeys: removedKeys,
          highlightedResponse1,
          highlightedResponse2
        };
      } catch (e) {
        return {
          mismatchKeys: ["Invalid JSON Response"],
          addedKeys: [],
          removedKeys: [],
          highlightedResponse1: res1,
          highlightedResponse2: res2
        };
      }
    }

    function highlightMismatches(obj, mismatches) {
      let jsonString = JSON.stringify(obj, null, 2);
      mismatches.forEach(key => {
        const keyRegex = key.split('.').pop();
        const regex = new RegExp(`("${keyRegex}"): (.*?)(,|\n)`, 'g');
        jsonString = jsonString.replace(regex, `<span class='highlight'>$1: $2</span>$3`);
      });
      return jsonString;
    }

    function decodeResponse(response) {
      try {
        return atob(response);
      } catch (e) {
        return response;
      }
    }

    function toggleDetails(id) {
      const details = document.getElementById(id);
      details.style.display = details.style.display === 'none' ? 'block' : 'none';
    }

    // Search functionality: Filter API cards based on the search query.
    document.getElementById("searchBar").addEventListener("input", function () {
      let query = this.value.toLowerCase();
      let cards = document.getElementsByClassName("api-card");
      Array.from(cards).forEach(card => {
        let cardText = card.textContent.toLowerCase();
        card.style.display = cardText.indexOf(query) > -1 ? "block" : "none";
      });
    });

    // Export comparison results as JSON
    function exportToJSON() {
      if (comparisonResults.length === 0) {
        alert("No data to export.");
        return;
      }
      const jsonData = JSON.stringify(comparisonResults, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "comparison_results.json");
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // Generate a detailed HTML report and convert it to PDF
    function generateReport() {
      if (comparisonResults.length === 0) {
        alert("No data available to generate report.");
        return;
      }
      // Fill report summary
      document.getElementById("reportDate").innerText = new Date().toLocaleString();
      document.getElementById("reportTotal").innerText = document.getElementById("totalRequests").innerText;
      document.getElementById("reportPassed").innerText = document.getElementById("matchedResponses").innerText;
      document.getElementById("reportFailed").innerText = document.getElementById("mismatchedResponses").innerText;

      // Fill detailed results
      const reportDetails = document.getElementById("reportDetails");
      reportDetails.innerHTML = "";
      comparisonResults.forEach(item => {
        let discrepancies = item.mismatchKeys.length ? item.mismatchKeys.join(", ") : "None";
        let row = `<tr>
                     <td>${item.url}</td>
                     <td>${item.schemaStatus}</td>
                     <td>${discrepancies}</td>
                   </tr>`;
        reportDetails.innerHTML += row;
      });

      // Temporarily make report container visible for html2pdf capture
      const reportContainer = document.getElementById("reportContainer");
      reportContainer.style.opacity = "1"; // make it visible
      reportContainer.style.pointerEvents = "auto";

      // Small delay to ensure rendering before PDF generation
      setTimeout(() => {
        const opt = {
          margin: 10,
          filename: 'Test_Report.pdf',
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2 },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };
        html2pdf().set(opt).from(reportContainer).save().then(() => {
          // After PDF generation, hide the report container again
          reportContainer.style.opacity = "0";
          reportContainer.style.pointerEvents = "none";
        });
      }, 500);
    }

    // Add AI analysis function
    function analyzeWithAI() {
      // Check if we have comparison data
      if (comparisonResults.length === 0) {
        alert("Please run a comparison first before using AI analysis.");
        return;
      }

      // Get the modal element
      const modal = document.getElementById('aiAnalysisModal');
      const closeBtn = modal.querySelector('.custom-close');
      const resultsDiv = document.getElementById('aiAnalysisResults');

      // Set up close button functionality
      closeBtn.onclick = function() {
        modal.style.display = "none";
      }

      // Close when clicking outside the modal
      window.onclick = function(event) {
        if (event.target == modal) {
          modal.style.display = "none";
        }
      }

      // Show loading message
      resultsDiv.innerHTML = '<p>Analyzing comparison data...</p>';

      // Show the modal
      modal.style.display = "block";

      // Simulate AI analysis with a timeout
      setTimeout(() => {
        // Count the types of issues
        const failedApis = comparisonResults.filter(r => r.schemaStatus.includes('Failed'));
        const totalMismatches = failedApis.reduce((sum, api) => sum + api.mismatchKeys.length, 0);
        const passRate = Math.round((parseInt(document.getElementById('matchedResponses').textContent) /
                                   parseInt(document.getElementById('totalRequests').textContent)) * 100);

        // Generate common patterns in mismatches
        const allMismatches = failedApis.flatMap(api => api.mismatchKeys);

        // Find common patterns in mismatched keys
        const keyFrequency = {};
        allMismatches.forEach(key => {
          const baseKey = key.split('.')[0];
          keyFrequency[baseKey] = (keyFrequency[baseKey] || 0) + 1;
        });

        // Get top 3 most common keys with issues
        const topIssueKeys = Object.entries(keyFrequency)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 3)
          .map(([key, count]) => `"${key}" (${count} occurrences)`);

        // Display analysis results
        resultsDiv.innerHTML = `
          <h4>Analysis Summary</h4>
          <div class="alert ${passRate > 80 ? 'alert-success' : 'alert-warning'}">
            <strong>API Compatibility Score: ${passRate}%</strong>
          </div>

          <h5>Key Findings:</h5>
          <ul>
            <li>Found ${failedApis.length} APIs with differences (${totalMismatches} total mismatches)</li>
            <li>Most common differences are in: ${topIssueKeys.length ? topIssueKeys.join(', ') : 'No common pattern found'}</li>
          </ul>

          <h5>Recommendations:</h5>
          <ol>
            <li>Review the failed endpoints, particularly those with critical functionality</li>
            <li>Check for version differences between environments</li>
            <li>Verify if the differences are expected due to environment configurations</li>
            <li>Consider updating documentation if the changes are intentional</li>
          </ol>

          ${failedApis.length > 0 ? `
            <h5>Top APIs to Investigate:</h5>
            <ul>
              ${failedApis.slice(0, 3).map(api =>
                `<li><strong>${api.url.split('?')[0]}</strong> - ${api.mismatchKeys.length} differences</li>`
              ).join('')}
            </ul>
          ` : ''}
        `;
      }, 1500);
    }
  </script>
</body>
</html>
