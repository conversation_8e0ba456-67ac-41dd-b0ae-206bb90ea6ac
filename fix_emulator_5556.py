#!/usr/bin/env python3
"""
Fix emulator-5556 connection issues
"""

import subprocess
import time

def fix_emulator_5556():
    print("🔧 Fixing emulator-5556 Connection Issues")
    print("=" * 45)
    
    print("1️⃣ Checking current status...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        print(f"ADB devices:\n{result.stdout}")
    except Exception as e:
        print(f"❌ ADB check failed: {e}")
        return
    
    print("\n2️⃣ Restarting ADB server...")
    try:
        subprocess.run(['adb', 'kill-server'], capture_output=True)
        time.sleep(2)
        subprocess.run(['adb', 'start-server'], capture_output=True)
        time.sleep(3)
        print("✅ ADB server restarted")
    except Exception as e:
        print(f"❌ ADB restart failed: {e}")
    
    print("\n3️⃣ Checking emulator-5556 responsiveness...")
    try:
        result = subprocess.run(
            ['adb', '-s', 'emulator-5556', 'shell', 'echo', 'test'],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            print("✅ emulator-5556 is responsive")
        else:
            print("❌ emulator-5556 is not responsive")
    except subprocess.TimeoutExpired:
        print("❌ emulator-5556 connection timeout")
    except Exception as e:
        print(f"❌ emulator-5556 test failed: {e}")
    
    print("\n4️⃣ Checking boot animation status...")
    try:
        result = subprocess.run(
            ['adb', '-s', 'emulator-5556', 'shell', 'getprop', 'service.bootanim.exit'],
            capture_output=True, text=True, timeout=10
        )
        bootanim_exit = result.stdout.strip() == '1'
        print(f"Boot animation finished: {'✅' if bootanim_exit else '❌'}")
        
        if not bootanim_exit:
            print("💡 Waiting for boot animation to finish...")
            for i in range(30):  # Wait up to 30 seconds
                time.sleep(1)
                result = subprocess.run(
                    ['adb', '-s', 'emulator-5556', 'shell', 'getprop', 'service.bootanim.exit'],
                    capture_output=True, text=True, timeout=5
                )
                if result.stdout.strip() == '1':
                    print("✅ Boot animation finished!")
                    break
                print(f"   Waiting... ({i+1}/30)")
            else:
                print("⚠️ Boot animation still not finished after 30 seconds")
    
    except Exception as e:
        print(f"❌ Boot animation check failed: {e}")
    
    print("\n5️⃣ Final status check...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        print(f"Final ADB devices:\n{result.stdout}")
    except Exception as e:
        print(f"❌ Final check failed: {e}")

def main():
    fix_emulator_5556()
    
    print("\n" + "=" * 45)
    print("🎯 RECOMMENDATIONS:")
    print("\n✅ IMMEDIATE ACTION:")
    print("   • Use emulator-5554 (Pixel_6_API_34) - it's working perfectly!")
    print("   • In dashboard: Select 'Pixel_6_API_34 (emulator-5554)'")
    print("   • Avoid 'Copy_of_Pixel_6_API_34 (emulator-5556)' for now")
    
    print("\n🔧 TO FIX emulator-5556:")
    print("   1. Cold boot the emulator from Android Studio")
    print("   2. Wait for complete boot (home screen visible)")
    print("   3. Enable USB debugging in emulator settings")
    print("   4. Try connection again")
    
    print("\n🌟 YOUR DASHBOARD IS READY!")
    print("🌐 URL: http://localhost:8501")
    print("📱 Tab: Emulator Testing")
    print("🎯 Select: Pixel_6_API_34 (emulator-5554)")

if __name__ == "__main__":
    main()
