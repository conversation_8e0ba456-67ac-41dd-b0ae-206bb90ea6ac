#!/usr/bin/env python3
"""
Comprehensive diagnosis of emulator connection issues
"""

import subprocess
import time
from emulator_manager import EmulatorManager
from appium_driver_enhanced import AppiumDriverEnhanced

def check_adb_devices():
    """Check ADB device status"""
    print("🔍 Checking ADB Devices...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        print(f"ADB devices output:\n{result.stdout}")
        
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        devices = {}
        for line in lines:
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    device_id = parts[0]
                    status = parts[1]
                    devices[device_id] = status
                    print(f"   • {device_id}: {status}")
        
        return devices
    except Exception as e:
        print(f"❌ ADB check failed: {e}")
        return {}

def check_emulator_processes():
    """Check running emulator processes"""
    print("\n🔍 Checking Emulator Processes...")
    try:
        import psutil
        emulator_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'emulator' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline'] or []
                    print(f"   • PID {proc.info['pid']}: {' '.join(cmdline[:3])}...")
                    emulator_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"Found {len(emulator_processes)} emulator process(es)")
        return emulator_processes
    except Exception as e:
        print(f"❌ Process check failed: {e}")
        return []

def check_appium_server():
    """Check Appium server status"""
    print("\n🔍 Checking Appium Server...")
    try:
        import requests
        response = requests.get("http://localhost:4723/status", timeout=5)
        if response.status_code == 200:
            print("✅ Appium server is running")
            data = response.json()
            print(f"   Build: {data.get('value', {}).get('build', {}).get('version', 'Unknown')}")
            return True
        else:
            print(f"❌ Appium server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Appium server check failed: {e}")
        return False

def test_individual_connections():
    """Test connection to each device individually"""
    print("\n🔍 Testing Individual Device Connections...")
    
    devices = check_adb_devices()
    driver = AppiumDriverEnhanced()
    
    for device_id, status in devices.items():
        if device_id.startswith('emulator-') and status == 'device':
            print(f"\n📱 Testing connection to {device_id}...")
            
            try:
                # Test ADB connection first
                result = subprocess.run(
                    ['adb', '-s', device_id, 'shell', 'echo', 'test'],
                    capture_output=True, text=True, timeout=10
                )
                
                if result.returncode == 0:
                    print(f"   ✅ ADB connection to {device_id} working")
                else:
                    print(f"   ❌ ADB connection to {device_id} failed")
                    continue
                
                # Test Appium connection
                print(f"   🔗 Testing Appium connection to {device_id}...")
                success, diagnostics = driver.connect_to_device(device_id)
                
                if success:
                    print(f"   ✅ Appium connection to {device_id} successful!")
                    
                    # Test basic functionality
                    tests = driver.test_basic_functionality()
                    for test_name, result in tests.items():
                        status_icon = "✅" if result else "❌"
                        print(f"      {status_icon} {test_name}")
                    
                    driver.disconnect()
                    print(f"   🔌 Disconnected from {device_id}")
                    
                else:
                    print(f"   ❌ Appium connection to {device_id} failed")
                    print(f"      Error: {diagnostics.error_message}")
                    if diagnostics.suggestions:
                        for suggestion in diagnostics.suggestions:
                            print(f"      💡 {suggestion}")
                
            except Exception as e:
                print(f"   ❌ Connection test failed: {e}")

def check_emulator_boot_status():
    """Check if emulators are fully booted"""
    print("\n🔍 Checking Emulator Boot Status...")
    
    devices = check_adb_devices()
    
    for device_id, status in devices.items():
        if device_id.startswith('emulator-') and status == 'device':
            print(f"\n📱 Checking boot status of {device_id}...")
            
            try:
                # Check boot completion
                result = subprocess.run(
                    ['adb', '-s', device_id, 'shell', 'getprop', 'sys.boot_completed'],
                    capture_output=True, text=True, timeout=10
                )
                
                boot_completed = result.stdout.strip() == '1'
                print(f"   Boot completed: {'✅' if boot_completed else '❌'}")
                
                # Check if ready for automation
                result = subprocess.run(
                    ['adb', '-s', device_id, 'shell', 'getprop', 'service.bootanim.exit'],
                    capture_output=True, text=True, timeout=10
                )
                
                bootanim_exit = result.stdout.strip() == '1'
                print(f"   Boot animation finished: {'✅' if bootanim_exit else '❌'}")
                
                if not boot_completed or not bootanim_exit:
                    print(f"   ⚠️ {device_id} may not be fully booted yet")
                
            except Exception as e:
                print(f"   ❌ Boot status check failed: {e}")

def main():
    print("🔧 COMPREHENSIVE CONNECTION DIAGNOSIS")
    print("=" * 50)
    
    # Check all components
    check_adb_devices()
    check_emulator_processes()
    appium_running = check_appium_server()
    check_emulator_boot_status()
    
    if appium_running:
        test_individual_connections()
    else:
        print("\n❌ Appium server not running - cannot test connections")
        print("💡 Start Appium server: appium --port 4723")
    
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSIS COMPLETE")
    print("\n💡 Common Solutions:")
    print("   1. Wait for emulators to fully boot (check boot status above)")
    print("   2. Restart ADB: adb kill-server && adb start-server")
    print("   3. Restart Appium server: appium --port 4723")
    print("   4. Cold boot emulators from Android Studio")
    print("   5. Check emulator settings (enable USB debugging)")

if __name__ == "__main__":
    main()
