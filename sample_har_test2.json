{"log": {"version": "1.2", "creator": {"name": "Test HAR Generator", "version": "1.0"}, "entries": [{"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/user/profile", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 160, "mimeType": "application/json", "text": "********************************************************************************************************************************************************************"}}, "time": 180, "startedDateTime": "2024-01-15T10:30:00.000Z"}, {"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/transactions/recent", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 280, "mimeType": "application/json", "text": "eyJ0cmFuc2FjdGlvbnMiOlt7ImlkIjoiVFhOMTIzIiwiYW1vdW50IjoxMjAwLCJzdGF0dXMiOiJzdWNjZXNzIiwiZGF0ZSI6IjIwMjQtMDEtMTUiLCJ0eXBlIjoiY3JlZGl0In0seyJpZCI6IlRYTjEyNCIsImFtb3VudCI6NTAwLCJzdGF0dXMiOiJjb21wbGV0ZWQiLCJkYXRlIjoiMjAyNC0wMS0xNCIsInR5cGUiOiJkZWJpdCJ9XX0="}}, "time": 220, "startedDateTime": "2024-01-15T10:30:01.000Z"}, {"request": {"method": "POST", "url": "https://dashboard.paytm.com/api/payment/initiate", "headers": [{"name": "Content-Type", "value": "application/json"}]}, "response": {"status": 201, "statusText": "Created", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 200, "mimeType": "application/json", "text": "eyJwYXltZW50SWQiOiJQWU0xMjM0NSIsInN0YXR1cyI6ImNyZWF0ZWQiLCJhbW91bnQiOjE1MDAsImN1cnJlbmN5IjoiSU5SIiwibWVyY2hhbnRJZCI6Ik1FUkNIMTIzIiwidGltZXN0YW1wIjoiMjAyNC0wMS0xNVQxMDozMDowMloifQ=="}}, "time": 350, "startedDateTime": "2024-01-15T10:30:02.000Z"}, {"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/analytics/dashboard", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 340, "mimeType": "application/json", "text": "eyJkYXNoYm9hcmQiOnsidG90YWxUcmFuc2FjdGlvbnMiOjEzMDAsInRvdGFsUmV2ZW51ZSI6MjcwMDAwLCJhY3RpdmVVc2VycyI6OTIwLCJwZW5kaW5nUGF5bWVudHMiOjEyLCJzdWNjZXNzUmF0ZSI6OTkuMiwiYXZlcmFnZVRyYW5zYWN0aW9uVmFsdWUiOjIwNzd9fQ=="}}, "time": 140, "startedDateTime": "2024-01-15T10:30:03.000Z"}, {"request": {"method": "GET", "url": "https://dashboard.paytm.com/api/notifications/unread", "headers": [{"name": "Accept", "value": "application/json"}]}, "response": {"status": 200, "statusText": "OK", "headers": [{"name": "Content-Type", "value": "application/json"}], "content": {"size": 120, "mimeType": "application/json", "text": "eyJub3RpZmljYXRpb25zIjpbeyJpZCI6Ik5PVElGMTIzIiwidGl0bGUiOiJOZXcgUGF5bWVudCIsIm1lc3NhZ2UiOiJZb3UgaGF2ZSBhIG5ldyBwYXltZW50IiwicmVhZCI6ZmFsc2V9XX0="}}, "time": 90, "startedDateTime": "2024-01-15T10:30:05.000Z"}]}}