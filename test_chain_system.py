"""
Chain-like Command System for Automated Mobile Testing
Allows sequencing multiple Appium operations together with validation and code generation
"""

import json
import time
import base64
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import re

class ChainStepType(Enum):
    """Types of operations available in test chains"""
    # App Management
    LAUNCH_APP = "launch_app"
    RESTART_APP = "restart_app"
    CLOSE_APP = "close_app"
    INSTALL_APP = "install_app"
    UNINSTALL_APP = "uninstall_app"
    RESET_APP = "reset_app"

    # Element Interactions
    TAP_ELEMENT = "tap_element"
    TAP_COORDINATES = "tap_coordinates"
    DOUBLE_TAP = "double_tap"
    LONG_PRESS = "long_press"
    TYPE_TEXT = "type_text"
    CLEAR_TEXT = "clear_text"
    GET_TEXT = "get_text"
    GET_ATTRIBUTE = "get_attribute"

    # Element Finding & Verification
    FIND_ELEMENT = "find_element"
    FIND_ELEMENTS = "find_elements"
    VERIFY_ELEMENT = "verify_element"
    VERIFY_ELEMENT_NOT_EXISTS = "verify_element_not_exists"
    VERIFY_TEXT = "verify_text"
    VERIFY_ELEMENT_TEXT = "verify_element_text"
    VERIFY_ELEMENT_ENABLED = "verify_element_enabled"
    VERIFY_ELEMENT_SELECTED = "verify_element_selected"

    # Gestures & Navigation
    SWIPE = "swipe"
    SCROLL = "scroll"
    SCROLL_TO_ELEMENT = "scroll_to_element"
    PINCH = "pinch"
    ZOOM = "zoom"
    ROTATE = "rotate"
    MULTI_TOUCH = "multi_touch"

    # Device Controls
    PRESS_HOME = "press_home"
    PRESS_BACK = "press_back"
    PRESS_MENU = "press_menu"
    PRESS_VOLUME_UP = "press_volume_up"
    PRESS_VOLUME_DOWN = "press_volume_down"
    PRESS_POWER = "press_power"
    LOCK_DEVICE = "lock_device"
    UNLOCK_DEVICE = "unlock_device"

    # Screen & Orientation
    TAKE_SCREENSHOT = "take_screenshot"
    ROTATE_PORTRAIT = "rotate_portrait"
    ROTATE_LANDSCAPE = "rotate_landscape"
    GET_SCREEN_SIZE = "get_screen_size"
    GET_ORIENTATION = "get_orientation"

    # Network & Connectivity
    TOGGLE_WIFI = "toggle_wifi"
    TOGGLE_MOBILE_DATA = "toggle_mobile_data"
    TOGGLE_AIRPLANE_MODE = "toggle_airplane_mode"
    SET_NETWORK_CONNECTION = "set_network_connection"

    # Notifications & System
    OPEN_NOTIFICATIONS = "open_notifications"
    OPEN_SETTINGS = "open_settings"
    GO_TO_HOME = "go_to_home"
    OPEN_RECENT_APPS = "open_recent_apps"

    # Wait & Timing
    WAIT = "wait"
    WAIT_FOR_ELEMENT = "wait_for_element"
    WAIT_FOR_ELEMENT_CLICKABLE = "wait_for_element_clickable"
    WAIT_FOR_TEXT = "wait_for_text"

    # Advanced Actions
    EXECUTE_SCRIPT = "execute_script"
    SWITCH_CONTEXT = "switch_context"
    SWITCH_TO_WINDOW = "switch_to_window"
    SET_IMPLICIT_WAIT = "set_implicit_wait"
    GET_PERFORMANCE_DATA = "get_performance_data"
    GET_BATTERY_INFO = "get_battery_info"

    # Legacy support
    PRESS_BUTTON = "press_button"

class ChainStepStatus(Enum):
    """Status of chain step execution"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class ChainStep:
    """Individual step in a test chain"""
    step_type: ChainStepType
    description: str
    parameters: Dict[str, Any]
    expected_result: Optional[str] = None
    timeout: int = 10
    continue_on_failure: bool = False
    status: ChainStepStatus = ChainStepStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    screenshot_data: Optional[str] = None

@dataclass
class TestChain:
    """Complete test chain with metadata"""
    name: str
    description: str
    app_package: Optional[str] = None
    steps: List[ChainStep] = None
    created_at: str = ""
    total_steps: int = 0
    completed_steps: int = 0
    failed_steps: int = 0
    total_execution_time: float = 0.0
    overall_status: ChainStepStatus = ChainStepStatus.PENDING
    generated_code: Optional[str] = None

    def __post_init__(self):
        if self.steps is None:
            self.steps = []
        self.total_steps = len(self.steps)

class TestChainExecutor:
    """Executes test chains with Appium driver"""
    
    def __init__(self, appium_driver):
        self.driver = appium_driver
        self.current_chain = None
        self.execution_log = []
    
    def create_chain(self, name: str, description: str, app_package: str = None) -> TestChain:
        """Create a new test chain"""
        chain = TestChain(
            name=name,
            description=description,
            app_package=app_package,
            created_at=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        self.current_chain = chain
        return chain
    
    def add_step(self, step_type: ChainStepType, description: str, 
                 parameters: Dict[str, Any], expected_result: str = None,
                 timeout: int = 10, continue_on_failure: bool = False) -> ChainStep:
        """Add a step to the current chain"""
        if not self.current_chain:
            raise ValueError("No active chain. Create a chain first.")
        
        step = ChainStep(
            step_type=step_type,
            description=description,
            parameters=parameters,
            expected_result=expected_result,
            timeout=timeout,
            continue_on_failure=continue_on_failure
        )
        
        self.current_chain.steps.append(step)
        self.current_chain.total_steps = len(self.current_chain.steps)
        return step
    
    def execute_chain(self, chain: TestChain = None) -> Dict[str, Any]:
        """Execute a complete test chain"""
        if chain:
            self.current_chain = chain
        
        if not self.current_chain:
            raise ValueError("No chain to execute")
        
        chain = self.current_chain
        chain.overall_status = ChainStepStatus.RUNNING
        start_time = time.time()
        
        execution_results = {
            "chain_name": chain.name,
            "total_steps": chain.total_steps,
            "step_results": [],
            "overall_success": True,
            "execution_time": 0.0,
            "generated_code": ""
        }
        
        try:
            for i, step in enumerate(chain.steps):
                print(f"Executing step {i+1}/{chain.total_steps}: {step.description}")
                
                step_result = self._execute_step(step)
                execution_results["step_results"].append(step_result)
                
                if step.status == ChainStepStatus.SUCCESS:
                    chain.completed_steps += 1
                elif step.status == ChainStepStatus.FAILED:
                    chain.failed_steps += 1
                    if not step.continue_on_failure:
                        execution_results["overall_success"] = False
                        break
            
            # Generate Appium test code
            chain.generated_code = self._generate_appium_code(chain)
            execution_results["generated_code"] = chain.generated_code
            
        except Exception as e:
            execution_results["overall_success"] = False
            execution_results["error"] = str(e)
        
        finally:
            chain.total_execution_time = time.time() - start_time
            execution_results["execution_time"] = chain.total_execution_time
            
            if execution_results["overall_success"] and chain.failed_steps == 0:
                chain.overall_status = ChainStepStatus.SUCCESS
            else:
                chain.overall_status = ChainStepStatus.FAILED
        
        return execution_results
    
    def _execute_step(self, step: ChainStep) -> Dict[str, Any]:
        """Execute a single step in the chain"""
        step.status = ChainStepStatus.RUNNING
        start_time = time.time()
        
        try:
            if step.step_type == ChainStepType.LAUNCH_APP:
                result = self._launch_app(step.parameters)
            elif step.step_type == ChainStepType.TAKE_SCREENSHOT:
                result = self._take_screenshot(step.parameters)
            elif step.step_type == ChainStepType.VERIFY_ELEMENT:
                result = self._verify_element(step.parameters)
            elif step.step_type == ChainStepType.VERIFY_TEXT:
                result = self._verify_text(step.parameters)
            elif step.step_type == ChainStepType.TAP_ELEMENT:
                result = self._tap_element(step.parameters)
            elif step.step_type == ChainStepType.TAP_COORDINATES:
                result = self._tap_coordinates(step.parameters)
            elif step.step_type == ChainStepType.TYPE_TEXT:
                result = self._type_text(step.parameters)
            elif step.step_type == ChainStepType.SWIPE:
                result = self._swipe(step.parameters)
            elif step.step_type == ChainStepType.PRESS_BUTTON:
                result = self._press_button(step.parameters)
            elif step.step_type == ChainStepType.WAIT:
                result = self._wait(step.parameters)
            elif step.step_type == ChainStepType.SCROLL:
                result = self._scroll(step.parameters)
            else:
                raise ValueError(f"Unknown step type: {step.step_type}")
            
            step.result = result
            step.status = ChainStepStatus.SUCCESS
            
            # Take screenshot after each step for documentation
            if step.step_type != ChainStepType.TAKE_SCREENSHOT:
                try:
                    screenshot = self.driver.take_screenshot()
                    if screenshot:
                        step.screenshot_data = screenshot
                except:
                    pass  # Screenshot is optional
            
        except Exception as e:
            step.status = ChainStepStatus.FAILED
            step.error_message = str(e)
            step.result = {"error": str(e)}
        
        finally:
            step.execution_time = time.time() - start_time
        
        return {
            "step_type": step.step_type.value,
            "description": step.description,
            "status": step.status.value,
            "result": step.result,
            "error": step.error_message,
            "execution_time": step.execution_time
        }
    
    def _launch_app(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Launch a mobile application"""
        package_name = params.get("package_name")
        activity = params.get("activity")

        if not package_name:
            raise ValueError("package_name is required for launch_app")

        if not self.driver.is_connected():
            raise ValueError("Driver not connected to device")

        # Launch app using Appium 2.x compatible methods
        try:
            if activity:
                # Use execute_script for start_activity in Appium 2.x
                self.driver.driver.execute_script('mobile: startActivity', {
                    'component': f"{package_name}/{activity}"
                })
            else:
                # Use activate_app for package-only launch
                self.driver.driver.activate_app(package_name)
        except Exception as e:
            # Fallback: try using ADB command
            import subprocess
            try:
                if activity:
                    subprocess.run(['adb', '-s', self.driver.device_id, 'shell', 'am', 'start',
                                  '-n', f"{package_name}/{activity}"],
                                  capture_output=True, timeout=10)
                else:
                    subprocess.run(['adb', '-s', self.driver.device_id, 'shell', 'monkey',
                                  '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'],
                                  capture_output=True, timeout=10)
            except Exception as adb_error:
                raise ValueError(f"Failed to launch app: {str(e)}. ADB fallback also failed: {str(adb_error)}")

        time.sleep(2)  # Wait for app to load

        return {
            "package_name": package_name,
            "activity": activity,
            "launched": True
        }
    
    def _take_screenshot(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Take a screenshot"""
        screenshot = self.driver.take_screenshot()
        if not screenshot:
            raise ValueError("Failed to capture screenshot")
        
        return {
            "screenshot_captured": True,
            "screenshot_size": len(screenshot),
            "screenshot_data": screenshot
        }
    
    def _verify_element(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Verify an element exists"""
        locator_type = params.get("locator_type", "id")
        locator_value = params.get("locator_value")
        
        if not locator_value:
            raise ValueError("locator_value is required for verify_element")
        
        from selenium.webdriver.common.by import By
        
        by_map = {
            "id": By.ID,
            "xpath": By.XPATH,
            "class": By.CLASS_NAME,
            "text": By.LINK_TEXT,
            "partial_text": By.PARTIAL_LINK_TEXT
        }
        
        by = by_map.get(locator_type, By.ID)
        
        try:
            element = self.driver.driver.find_element(by, locator_value)
            return {
                "element_found": True,
                "element_text": element.text if hasattr(element, 'text') else "",
                "element_displayed": element.is_displayed() if hasattr(element, 'is_displayed') else True
            }
        except:
            raise ValueError(f"Element not found: {locator_type}={locator_value}")
    
    def _verify_text(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Verify text exists on screen"""
        text_to_find = params.get("text")
        case_sensitive = params.get("case_sensitive", False)
        
        if not text_to_find:
            raise ValueError("text is required for verify_text")
        
        page_source = self.driver.driver.page_source
        
        if case_sensitive:
            found = text_to_find in page_source
        else:
            found = text_to_find.lower() in page_source.lower()
        
        if not found:
            raise ValueError(f"Text not found: '{text_to_find}'")
        
        return {
            "text_found": True,
            "search_text": text_to_find,
            "case_sensitive": case_sensitive
        }
    
    def _tap_element(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Tap on an element"""
        locator_type = params.get("locator_type", "id")
        locator_value = params.get("locator_value")
        
        if not locator_value:
            raise ValueError("locator_value is required for tap_element")
        
        from selenium.webdriver.common.by import By
        
        by_map = {
            "id": By.ID,
            "xpath": By.XPATH,
            "class": By.CLASS_NAME,
            "text": By.LINK_TEXT,
            "partial_text": By.PARTIAL_LINK_TEXT
        }
        
        by = by_map.get(locator_type, By.ID)
        
        try:
            element = self.driver.driver.find_element(by, locator_value)
            element.click()
            return {
                "element_tapped": True,
                "locator": f"{locator_type}={locator_value}"
            }
        except:
            raise ValueError(f"Failed to tap element: {locator_type}={locator_value}")
    
    def _tap_coordinates(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Tap at specific coordinates"""
        x = params.get("x")
        y = params.get("y")
        
        if x is None or y is None:
            raise ValueError("x and y coordinates are required for tap_coordinates")
        
        self.driver.tap(x, y)
        
        return {
            "coordinates_tapped": True,
            "x": x,
            "y": y
        }
    
    def _type_text(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Type text"""
        text = params.get("text")
        
        if not text:
            raise ValueError("text is required for type_text")
        
        self.driver.type_text(text)
        
        return {
            "text_typed": True,
            "text": text
        }
    
    def _swipe(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Perform swipe gesture"""
        start_x = params.get("start_x")
        start_y = params.get("start_y")
        end_x = params.get("end_x")
        end_y = params.get("end_y")
        duration = params.get("duration", 1000)
        
        if None in [start_x, start_y, end_x, end_y]:
            raise ValueError("start_x, start_y, end_x, end_y are required for swipe")
        
        self.driver.swipe(start_x, start_y, end_x, end_y, duration)
        
        return {
            "swipe_performed": True,
            "start": f"({start_x}, {start_y})",
            "end": f"({end_x}, {end_y})",
            "duration": duration
        }
    
    def _press_button(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Press device button"""
        button = params.get("button", "home")
        
        if button == "home":
            self.driver.press_home()
        elif button == "back":
            self.driver.press_back()
        elif button == "menu":
            self.driver.press_menu()
        else:
            raise ValueError(f"Unknown button: {button}")
        
        return {
            "button_pressed": True,
            "button": button
        }
    
    def _wait(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Wait for specified duration"""
        duration = params.get("duration", 1)
        
        time.sleep(duration)
        
        return {
            "wait_completed": True,
            "duration": duration
        }
    
    def _scroll(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Scroll in specified direction"""
        direction = params.get("direction", "down")
        
        if direction == "up":
            self.driver.scroll_up()
        elif direction == "down":
            self.driver.scroll_down()
        else:
            raise ValueError(f"Unknown scroll direction: {direction}")
        
        return {
            "scroll_performed": True,
            "direction": direction
        }

    def _generate_appium_code(self, chain: TestChain) -> str:
        """Generate Appium test case code from chain"""
        code_lines = []

        # Header
        code_lines.extend([
            "# Generated Appium Test Case",
            f"# Test: {chain.name}",
            f"# Description: {chain.description}",
            f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "import time",
            "from appium import webdriver",
            "from selenium.webdriver.common.by import By",
            "from selenium.webdriver.support.ui import WebDriverWait",
            "from selenium.webdriver.support import expected_conditions as EC",
            "",
            "class GeneratedTestCase:",
            "    def __init__(self, driver):",
            "        self.driver = driver",
            "        self.wait = WebDriverWait(driver, 10)",
            "",
            f"    def test_{chain.name.lower().replace(' ', '_')}(self):",
            f'        """',
            f'        {chain.description}',
            f'        """',
            "        try:"
        ])

        # Generate code for each step
        for i, step in enumerate(chain.steps):
            code_lines.append(f"            # Step {i+1}: {step.description}")

            if step.step_type == ChainStepType.LAUNCH_APP:
                package = step.parameters.get("package_name")
                activity = step.parameters.get("activity")
                if activity:
                    code_lines.append(f'            self.driver.start_activity("{package}", "{activity}")')
                else:
                    code_lines.append(f'            self.driver.activate_app("{package}")')
                code_lines.append("            time.sleep(2)  # Wait for app to load")

            elif step.step_type == ChainStepType.TAKE_SCREENSHOT:
                code_lines.append("            screenshot = self.driver.get_screenshot_as_base64()")
                code_lines.append("            # Save or process screenshot as needed")

            elif step.step_type == ChainStepType.VERIFY_ELEMENT:
                locator_type = step.parameters.get("locator_type", "id")
                locator_value = step.parameters.get("locator_value")
                by_map = {
                    "id": "By.ID",
                    "xpath": "By.XPATH",
                    "class": "By.CLASS_NAME",
                    "text": "By.LINK_TEXT",
                    "partial_text": "By.PARTIAL_LINK_TEXT"
                }
                by_str = by_map.get(locator_type, "By.ID")
                code_lines.extend([
                    f'            element = self.wait.until(',
                    f'                EC.presence_of_element_located(({by_str}, "{locator_value}"))',
                    f'            )',
                    f'            assert element.is_displayed(), "Element not visible: {locator_value}"'
                ])

            elif step.step_type == ChainStepType.VERIFY_TEXT:
                text = step.parameters.get("text")
                case_sensitive = step.parameters.get("case_sensitive", False)
                if case_sensitive:
                    code_lines.append(f'            assert "{text}" in self.driver.page_source, "Text not found: {text}"')
                else:
                    code_lines.append(f'            assert "{text}".lower() in self.driver.page_source.lower(), "Text not found: {text}"')

            elif step.step_type == ChainStepType.TAP_ELEMENT:
                locator_type = step.parameters.get("locator_type", "id")
                locator_value = step.parameters.get("locator_value")
                by_map = {
                    "id": "By.ID",
                    "xpath": "By.XPATH",
                    "class": "By.CLASS_NAME",
                    "text": "By.LINK_TEXT",
                    "partial_text": "By.PARTIAL_LINK_TEXT"
                }
                by_str = by_map.get(locator_type, "By.ID")
                code_lines.extend([
                    f'            element = self.wait.until(',
                    f'                EC.element_to_be_clickable(({by_str}, "{locator_value}"))',
                    f'            )',
                    f'            element.click()'
                ])

            elif step.step_type == ChainStepType.TAP_COORDINATES:
                x = step.parameters.get("x")
                y = step.parameters.get("y")
                code_lines.append(f'            self.driver.tap([({x}, {y})])')

            elif step.step_type == ChainStepType.TYPE_TEXT:
                text = step.parameters.get("text")
                code_lines.append(f'            self.driver.press_keycode(66)  # Clear field if needed')
                code_lines.append(f'            self.driver.find_element(By.CLASS_NAME, "android.widget.EditText").send_keys("{text}")')

            elif step.step_type == ChainStepType.SWIPE:
                start_x = step.parameters.get("start_x")
                start_y = step.parameters.get("start_y")
                end_x = step.parameters.get("end_x")
                end_y = step.parameters.get("end_y")
                duration = step.parameters.get("duration", 1000)
                code_lines.append(f'            self.driver.swipe({start_x}, {start_y}, {end_x}, {end_y}, {duration})')

            elif step.step_type == ChainStepType.PRESS_BUTTON:
                button = step.parameters.get("button", "home")
                if button == "home":
                    code_lines.append('            self.driver.press_keycode(3)  # Home button')
                elif button == "back":
                    code_lines.append('            self.driver.press_keycode(4)  # Back button')
                elif button == "menu":
                    code_lines.append('            self.driver.press_keycode(82)  # Menu button')

            elif step.step_type == ChainStepType.WAIT:
                duration = step.parameters.get("duration", 1)
                code_lines.append(f'            time.sleep({duration})')

            elif step.step_type == ChainStepType.SCROLL:
                direction = step.parameters.get("direction", "down")
                if direction == "down":
                    code_lines.append('            self.driver.swipe(500, 1500, 500, 500, 1000)  # Scroll down')
                else:
                    code_lines.append('            self.driver.swipe(500, 500, 500, 1500, 1000)  # Scroll up')

            code_lines.append("")  # Empty line between steps

        # Footer
        code_lines.extend([
            "            print('Test completed successfully')",
            "",
            "        except Exception as e:",
            "            print(f'Test failed: {str(e)}')",
            "            raise",
            "",
            "        finally:",
            "            # Cleanup if needed",
            "            pass"
        ])

        return "\n".join(code_lines)

    def export_chain(self, chain: TestChain = None) -> Dict[str, Any]:
        """Export chain to JSON format"""
        if chain:
            self.current_chain = chain

        if not self.current_chain:
            raise ValueError("No chain to export")

        # Convert dataclass to dict, handling enums
        def convert_for_json(obj):
            if isinstance(obj, Enum):
                return obj.value
            elif hasattr(obj, '__dict__'):
                return {k: convert_for_json(v) for k, v in obj.__dict__.items()}
            elif isinstance(obj, list):
                return [convert_for_json(item) for item in obj]
            elif isinstance(obj, dict):
                return {k: convert_for_json(v) for k, v in obj.items()}
            else:
                return obj

        return convert_for_json(self.current_chain)

    def import_chain(self, chain_data: Dict[str, Any]) -> TestChain:
        """Import chain from JSON format"""
        # Convert back from JSON, handling enums
        steps = []
        for step_data in chain_data.get("steps", []):
            step = ChainStep(
                step_type=ChainStepType(step_data["step_type"]),
                description=step_data["description"],
                parameters=step_data["parameters"],
                expected_result=step_data.get("expected_result"),
                timeout=step_data.get("timeout", 10),
                continue_on_failure=step_data.get("continue_on_failure", False),
                status=ChainStepStatus(step_data.get("status", "pending")),
                result=step_data.get("result"),
                error_message=step_data.get("error_message"),
                execution_time=step_data.get("execution_time", 0.0),
                screenshot_data=step_data.get("screenshot_data")
            )
            steps.append(step)

        chain = TestChain(
            name=chain_data["name"],
            description=chain_data["description"],
            app_package=chain_data.get("app_package"),
            steps=steps,
            created_at=chain_data.get("created_at", ""),
            total_steps=chain_data.get("total_steps", 0),
            completed_steps=chain_data.get("completed_steps", 0),
            failed_steps=chain_data.get("failed_steps", 0),
            total_execution_time=chain_data.get("total_execution_time", 0.0),
            overall_status=ChainStepStatus(chain_data.get("overall_status", "pending")),
            generated_code=chain_data.get("generated_code")
        )

        self.current_chain = chain
        return chain
