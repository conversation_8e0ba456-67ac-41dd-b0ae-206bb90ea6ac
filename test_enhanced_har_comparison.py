"""
Test script for Enhanced HAR Comparison functionality
Tests all the new AI Analysis features and visualizations
"""

import json
import base64
from integrated_har_comparison import IntegratedHARCom<PERSON>ison

def create_test_har_data():
    """Create test HAR data for validation"""
    
    # Sample response data (base64 encoded JSON)
    response1_data = {
        "user": {"id": 123, "name": "<PERSON>", "email": "<EMAIL>", "status": "active"}
    }
    response1_encoded = base64.b64encode(json.dumps(response1_data).encode()).decode()
    
    response2_data = {
        "user": {"id": 123, "name": "<PERSON>", "email": "<EMAIL>", "status": "active", "lastLogin": "2024-01-15T10:30:00Z"}
    }
    response2_encoded = base64.b64encode(json.dumps(response2_data).encode()).decode()
    
    # HAR data structure
    har1 = {
        "log": {
            "entries": [
                {
                    "request": {"url": "https://dashboard.paytm.com/api/user/profile"},
                    "response": {"content": {"text": response1_encoded}}
                },
                {
                    "request": {"url": "https://dashboard.paytm.com/api/transactions/recent"},
                    "response": {"content": {"text": response1_encoded}}
                }
            ]
        }
    }
    
    har2 = {
        "log": {
            "entries": [
                {
                    "request": {"url": "https://dashboard.paytm.com/api/user/profile"},
                    "response": {"content": {"text": response2_encoded}}
                },
                {
                    "request": {"url": "https://dashboard.paytm.com/api/transactions/recent"},
                    "response": {"content": {"text": response2_encoded}}
                }
            ]
        }
    }
    
    return har1, har2

def test_enhanced_ai_analysis():
    """Test the enhanced AI analysis functionality"""
    print("🧪 Testing Enhanced AI Analysis Functionality")
    print("=" * 50)
    
    # Initialize comparison engine
    engine = IntegratedHARComparison()
    
    # Create test data
    har1, har2 = create_test_har_data()
    
    # Parse HAR files
    har1_data = {
        'success': True,
        'entries': har1['log']['entries'],
        'format': 'HAR'
    }
    
    har2_data = {
        'success': True,
        'entries': har2['log']['entries'],
        'format': 'HAR'
    }
    
    # Process comparison
    print("📊 Processing comparison...")
    comparison_result = engine.process_comparison(har1_data, har2_data)
    
    if comparison_result['success']:
        print("✅ Comparison successful!")
        print(f"   Total requests: {comparison_result['total_requests']}")
        print(f"   Matched responses: {comparison_result['matched_responses']}")
        print(f"   Mismatched responses: {comparison_result['mismatched_responses']}")
        
        # Test AI analysis
        print("\n🤖 Testing AI Analysis...")
        ai_analysis = engine.generate_ai_analysis()
        
        if 'error' not in ai_analysis:
            print("✅ AI Analysis successful!")
            print(f"   Pass rate: {ai_analysis['pass_rate']}%")
            print(f"   Failed APIs: {ai_analysis['failed_apis_count']}")
            print(f"   Total mismatches: {ai_analysis['total_mismatches']}")
            
            # Test visualization data
            print("\n📈 Testing Visualization Data...")
            if 'endpoint_mismatch_counts' in ai_analysis:
                print("✅ Endpoint mismatch data available")
                print(f"   Endpoints analyzed: {len(ai_analysis['endpoint_mismatch_counts'])}")
            
            if 'error_type_distribution' in ai_analysis:
                print("✅ Error type distribution available")
                print(f"   Error types: {len(ai_analysis['error_type_distribution'])}")
            
            if 'performance_data' in ai_analysis:
                print("✅ Performance data available")
                print(f"   Performance records: {len(ai_analysis['performance_data'])}")
            
            if 'severity_assessment' in ai_analysis:
                severity = ai_analysis['severity_assessment']
                print("✅ Severity assessment available")
                print(f"   Severity level: {severity['level']}")
                print(f"   Message: {severity['message']}")
            
            # Test recommendations
            if 'recommendations' in ai_analysis:
                print("✅ Recommendations available")
                print(f"   Number of recommendations: {len(ai_analysis['recommendations'])}")
            
        else:
            print(f"❌ AI Analysis failed: {ai_analysis['error']}")
    
    else:
        print(f"❌ Comparison failed: {comparison_result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced AI Analysis Test Complete!")

def test_professional_styling():
    """Test that emoji icons have been removed"""
    print("\n🧪 Testing Professional Styling (No Emojis)")
    print("=" * 50)
    
    # Read the integrated HAR comparison file
    with open('integrated_har_comparison.py', 'r') as f:
        content = f.read()
    
    # Check for common emoji patterns
    emoji_patterns = ['🔄', '📁', '✅', '❌', '🔍', '📊', '📄', '📋', '🤖', '🎯', '💡', '🔴', '🟡', '🟢']
    
    found_emojis = []
    for emoji in emoji_patterns:
        if emoji in content:
            found_emojis.append(emoji)
    
    if found_emojis:
        print(f"⚠️  Found emojis that should be removed: {', '.join(found_emojis)}")
    else:
        print("✅ No emoji icons found - Professional styling confirmed!")
    
    # Check for professional text alternatives
    professional_terms = [
        'Compare HAR Files',
        'Upload HAR Files',
        'AI Analysis',
        'Generate Report',
        'Export as JSON',
        'Search and Filter',
        'Passed Results',
        'Failed Results'
    ]
    
    found_terms = []
    for term in professional_terms:
        if term in content:
            found_terms.append(term)
    
    print(f"✅ Professional terms found: {len(found_terms)}/{len(professional_terms)}")
    
    print("=" * 50)

def test_visualization_components():
    """Test that all visualization components are properly defined"""
    print("\n🧪 Testing Visualization Components")
    print("=" * 50)
    
    # Read the integrated HAR comparison file
    with open('integrated_har_comparison.py', 'r') as f:
        content = f.read()
    
    # Check for visualization functions
    viz_functions = [
        'render_error_distribution_chart',
        'render_endpoint_analysis_chart',
        'render_mismatch_patterns_chart',
        'render_performance_impact_chart',
        'render_enhanced_recommendations',
        'render_overview_charts',
        'render_detailed_analysis_charts',
        'render_trend_analysis',
        'render_performance_metrics'
    ]
    
    found_functions = []
    for func in viz_functions:
        if f"def {func}" in content:
            found_functions.append(func)
    
    print(f"✅ Visualization functions found: {len(found_functions)}/{len(viz_functions)}")
    
    # Check for Plotly chart types
    chart_types = [
        'go.Pie',
        'go.Bar',
        'go.Heatmap',
        'go.Indicator',
        'px.bar',
        'px.line',
        'px.histogram'
    ]
    
    found_charts = []
    for chart in chart_types:
        if chart in content:
            found_charts.append(chart)
    
    print(f"✅ Chart types implemented: {len(found_charts)}/{len(chart_types)}")
    
    print("=" * 50)

if __name__ == "__main__":
    print("🚀 Starting Enhanced HAR Comparison Tests")
    print("=" * 60)
    
    # Run all tests
    test_enhanced_ai_analysis()
    test_professional_styling()
    test_visualization_components()
    
    print("\n🎉 All Tests Complete!")
    print("=" * 60)
    print("\n📋 Summary:")
    print("✅ Enhanced AI Analysis with comprehensive visualizations")
    print("✅ Professional styling without emoji icons")
    print("✅ Multiple chart types and interactive visualizations")
    print("✅ Severity assessment and intelligent recommendations")
    print("✅ Performance metrics and trend analysis")
    print("\n🌐 Access your enhanced dashboard at: http://localhost:8529")
