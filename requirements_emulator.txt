# Additional requirements for Android Emulator Testing
# Install these with: pip install -r requirements_emulator.txt

# Appium WebDriver for mobile automation
Appium-Python-Client==3.1.0

# Selenium for web driver support
selenium==4.15.2

# System process management
psutil==5.9.6

# XML parsing for Android UI hierarchy
lxml==4.9.3

# Image processing for screenshots
Pillow==10.1.0

# Computer vision for element recognition (optional)
opencv-python==********

# OCR for text recognition (optional)
pytesseract==0.3.10

# WebSocket support for real-time communication
websockets==12.0

# Additional utilities
pathlib2==2.3.7
typing-extensions==4.8.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
