#!/usr/bin/env python3
import os
import json
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime
import uuid
import time
from typing import Dict, List, Any, Optional, Union
import re
import sqlite3
from urllib.parse import urlparse
from qa_knowledge_base import QAKnowledgeBase

# Import Claude AI agent if available
try:
    from claude_agent import ClaudeAgent
    CLAUDE_AVAILABLE = True
except ImportError:
    CLAUDE_AVAILABLE = False
    # Create a simple fallback agent
    class ClaudeAgent:
        def analyze_logs(self, log_data, prompt=None):
            return ["Claude AI agent not available. Please check that claude_agent.py exists."]

# Set page configuration
st.set_page_config(
    page_title="QA Automation Log Analysis Dashboard",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize knowledge base
kb = QAKnowledgeBase()

# Initialize session state variables
if "log_data" not in st.session_state:
    st.session_state.log_data = None
if "log_df" not in st.session_state:
    st.session_state.log_df = None
if "ai_analysis" not in st.session_state:
    st.session_state.ai_analysis = None
if "analysis_history" not in st.session_state:
    st.session_state.analysis_history = []
if "selected_analysis" not in st.session_state:
    st.session_state.selected_analysis = None
if "current_tab" not in st.session_state:
    st.session_state.current_tab = "dashboard"
if "filter_settings" not in st.session_state:
    st.session_state.filter_settings = {
        "status_filter": [],
        "host_filter": [],
        "method_filter": [],
        "time_range": [0, 10000],
        "search_query": ""
    }
if "test_context" not in st.session_state:
    st.session_state.test_context = ""
if "test_tags" not in st.session_state:
    st.session_state.test_tags = ""
if "qa_report" not in st.session_state:
    st.session_state.qa_report = None
if "run_comparison" not in st.session_state:
    st.session_state.run_comparison = False
if "show_quick_stats" not in st.session_state:
    st.session_state.show_quick_stats = False

# Apply custom styling
def apply_custom_styling():
    """Apply custom styling for the dashboard"""
    st.markdown("""
    <style>
    /* Main theme colors */
    :root {
        --primary-color: #4361ee;
        --secondary-color: #3a0ca3;
        --background-color: #f8f9fa;
        --text-color: #212529;
        --light-accent: #e9ecef;
        --dark-accent: #495057;
        --success-color: #38b000;
        --warning-color: #ff9e00;
        --error-color: #d90429;
    }

    /* Base styling */
    .main {
        background-color: var(--background-color);
        color: var(--text-color);
    }

    /* Headers */
    h1, h2, h3 {
        color: var(--primary-color);
    }

    /* Cards */
    .card {
        background-color: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    /* Metrics */
    .metric-card {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .metric-label {
        font-size: 0.9rem;
        color: var(--dark-accent);
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
    }
    
    .status-2xx {
        background-color: var(--success-color);
        color: white;
    }
    
    .status-3xx {
        background-color: var(--primary-color);
        color: white;
    }
    
    .status-4xx {
        background-color: var(--warning-color);
        color: black;
    }
    
    .status-5xx {
        background-color: var(--error-color);
        color: white;
    }

    /* Insights */
    .insight-card {
        background-color: #f1f8fe;
        border-left: 4px solid var(--primary-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }
    
    .insight-title {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    /* Severity indicators */
    .severity-high {
        color: var(--error-color);
        font-weight: bold;
    }
    
    .severity-medium {
        color: var(--warning-color);
        font-weight: bold;
    }
    
    .severity-low {
        color: var(--success-color);
        font-weight: bold;
    }

    /* Knowledge base */
    .kb-card {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .kb-title {
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .kb-meta {
        font-size: 0.8rem;
        color: var(--dark-accent);
    }
    
    /* Tags */
    .tag {
        display: inline-block;
        background-color: var(--light-accent);
        color: var(--dark-accent);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    /* Recommendations */
    .recommendation {
        background-color: #e6f3ff;
        border-left: 4px solid #0077cc;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }
    </style>
    """, unsafe_allow_html=True)

# Apply styling
apply_custom_styling()

# Main function to run the app
def main():
    # Sidebar
    with st.sidebar:
        render_sidebar()
    
    # Main content
    st.markdown('<h1>QA Automation Log Analysis Dashboard</h1>', unsafe_allow_html=True)
    
    # Create all 12 tabs
    tabs = st.tabs([
        "📊 Dashboard",
        "🔍 Request Details",
        "📋 HAR Comparison",
        "🤖 Karate Test Generator",
        "📋 API Documentation",
        "🎨 P4B Figma Validation",
        "📱 RC Testing",
        "📱 Emulator Testing",
        "🔗 Chain Testing",
        "📚 Knowledge Base",
        "🤖 AI Analysis",
        "📋 QA Reports"
    ])
    
    # All 12 tabs
    with tabs[0]:
        render_dashboard_tab()

    with tabs[1]:
        render_request_details_tab()

    with tabs[2]:
        render_har_comparison_tab()

    with tabs[3]:
        render_karate_test_generator_tab()

    with tabs[4]:
        render_api_documentation_tab()

    with tabs[5]:
        render_figma_validation_tab()

    with tabs[6]:
        render_rc_testing_tab()

    with tabs[7]:
        render_emulator_testing_tab()

    with tabs[8]:
        render_chain_testing_tab()

    with tabs[9]:
        render_knowledge_base_tab()

    with tabs[10]:
        render_ai_analysis_tab()

    with tabs[11]:
        render_qa_reports_tab()

# Function to render the sidebar
def render_sidebar():
    st.markdown('<h3>QA Automation Tools</h3>', unsafe_allow_html=True)
    
    # Claude AI Settings
    st.markdown('<h4>Claude AI Settings</h4>', unsafe_allow_html=True)
    api_key = st.text_input("Enter your Anthropic API Key:", type="password", key="sidebar_api_key")
    if api_key:
        os.environ["ANTHROPIC_API_KEY"] = api_key
        st.success("API key set! You can now use Claude AI for analysis.")

    # Upload log file
    st.markdown('<h4>Upload Log File</h4>', unsafe_allow_html=True)
    uploaded_file = st.file_uploader("Choose a Charles Proxy log file", type=["json", "chlsj"], key="sidebar_file_upload")

    # Test context
    st.markdown('<h4>Test Context</h4>', unsafe_allow_html=True)
    test_context = st.text_input("Test description:", key="sidebar_test_context_input")
    if test_context != st.session_state.test_context:
        st.session_state.test_context = test_context

    # Test tags
    test_tags = st.text_input("Tags (comma separated):", key="sidebar_test_tags_input")
    if test_tags != st.session_state.test_tags:
        st.session_state.test_tags = test_tags
    
    # Process uploaded file
    if uploaded_file is not None:
        try:
            # Read and parse the file
            log_data = json.load(uploaded_file)
            
            # Store in session state
            st.session_state.log_data = log_data
            
            # Create DataFrame
            create_log_dataframe(log_data)
            
            st.success(f"Successfully loaded log file with {len(log_data.get('entries', []))} entries")
            
            # Analyze with Claude if available
            if CLAUDE_AVAILABLE and "ANTHROPIC_API_KEY" in os.environ:
                with st.spinner("Analyzing logs with Claude AI..."):
                    claude = ClaudeAgent()
                    qa_prompt = f"""
                    Analyze this Charles Proxy log from a QA automation perspective.
                    Test Context: {st.session_state.test_context}
                    
                    Focus on:
                    1. Identifying potential bugs or issues
                    2. Performance problems (slow responses, timeouts)
                    3. Error patterns and their root causes
                    4. Authentication or security concerns
                    5. API response validation issues
                    
                    Provide specific, actionable insights that would help a QA engineer improve test coverage.
                    """
                    analysis = claude.analyze_logs(log_data, prompt=qa_prompt)
                    st.session_state.ai_analysis = analysis
                    
                    # Save to knowledge base
                    tags = [tag.strip() for tag in st.session_state.test_tags.split(",")] if st.session_state.test_tags else []
                    session_id = kb.save_analysis_session(
                        log_data=log_data,
                        log_df=st.session_state.log_df,
                        ai_analysis=analysis,
                        file_name=uploaded_file.name,
                        test_context=st.session_state.test_context,
                        tags=tags
                    )
                    st.success(f"Analysis saved to knowledge base with ID: {session_id}")
        except Exception as e:
            st.error(f"Error loading file: {str(e)}")
    
    # Knowledge base navigation
    st.markdown('<h4>Knowledge Base</h4>', unsafe_allow_html=True)
    if st.button("View All Sessions", key="sidebar_view_sessions"):
        st.session_state.current_tab = "knowledge_base"

    # Search
    st.markdown('<h4>Search</h4>', unsafe_allow_html=True)
    search_query = st.text_input("Search knowledge base:", key="sidebar_search_query")
    if search_query:
        search_results = kb.search(search_query)
        st.session_state.search_results = search_results
        st.session_state.current_tab = "knowledge_base"

# Import the missing functions
from qa_integrated_dashboard_functions import (
    create_log_dataframe,
    render_dashboard_tab,
    render_request_details_tab
)

# Function to render AI analysis tab
def render_ai_analysis_tab():
    """Render the AI analysis tab"""
    st.markdown('<h2>AI Analysis</h2>', unsafe_allow_html=True)

    if st.session_state.ai_analysis:
        st.markdown('<h3>Analysis Results</h3>', unsafe_allow_html=True)

        # Display analysis
        for insight in st.session_state.ai_analysis:
            st.markdown(f"""
            <div class="insight-card">
                <div class="insight-title">AI Insight</div>
                <div>{insight}</div>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No AI analysis available. Upload a log file to get started.")

# Function to render knowledge base tab
def render_knowledge_base_tab():
    """Render the knowledge base tab"""
    st.markdown('<h2>Knowledge Base</h2>', unsafe_allow_html=True)

    # Display recent sessions
    sessions = kb.get_all_sessions()[:10]  # Get first 10 sessions

    if sessions:
        st.markdown('<h3>Recent Analysis Sessions</h3>', unsafe_allow_html=True)

        for session in sessions:
            st.markdown(f"""
            <div class="kb-card">
                <div class="kb-title">{session.get('file_name', 'Unknown')}</div>
                <div class="kb-meta">
                    Date: {session.get('timestamp', 'Unknown')} |
                    Context: {session.get('test_context', 'None')}
                </div>
                <div>{session.get('summary', 'No summary available')}</div>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No analysis sessions found. Upload and analyze log files to build your knowledge base.")

# Function to render QA reports tab
def render_qa_reports_tab():
    """Render the QA reports tab"""
    st.markdown('<h2>QA Reports</h2>', unsafe_allow_html=True)

    if st.session_state.log_df is not None and not st.session_state.log_df.empty:
        df = st.session_state.log_df

        # Generate report
        st.markdown('<h3>Test Execution Report</h3>', unsafe_allow_html=True)

        # Summary metrics
        total_requests = len(df)
        success_count = len(df[df["status"].between(200, 299)])
        error_count = len(df[df["status"] >= 400])
        success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total Requests", total_requests)
        with col2:
            st.metric("Success Rate", f"{success_rate:.1f}%")
        with col3:
            st.metric("Error Count", error_count)

        # Detailed findings
        st.markdown('<h4>Key Findings</h4>', unsafe_allow_html=True)

        if error_count > 0:
            st.markdown(f"""
            <div class="recommendation">
                <strong>⚠️ Errors Detected:</strong> Found {error_count} failed requests that require investigation.
            </div>
            """, unsafe_allow_html=True)

        if success_rate < 95:
            st.markdown(f"""
            <div class="recommendation">
                <strong>📊 Success Rate:</strong> Current success rate is {success_rate:.1f}%. Consider investigating failed requests.
            </div>
            """, unsafe_allow_html=True)

        # Performance insights
        avg_response_time = df["time"].mean()
        slow_requests = len(df[df["time"] > 5000])  # > 5 seconds

        if slow_requests > 0:
            st.markdown(f"""
            <div class="recommendation">
                <strong>⏱️ Performance:</strong> Found {slow_requests} slow requests (>5s). Average response time: {avg_response_time:.0f}ms.
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No data available for report generation. Please upload a log file.")

def render_har_comparison_tab():
    """Complete integrated HAR comparison tab with all index.html features"""
    try:
        from integrated_har_comparison import render_integrated_har_comparison_tab
        render_integrated_har_comparison_tab()
    except ImportError as e:
        st.error(f"Integrated HAR comparison not available: {str(e)}")
        # Fallback to existing implementation
        render_legacy_har_comparison()

def render_legacy_har_comparison():
    """Legacy HAR comparison implementation"""
    st.markdown("## 📋 HAR File Comparison & Analysis")
    st.markdown("Compare HAR files with advanced filtering, side-by-side analysis, and detailed insights")

    # Sidebar controls
    with st.sidebar:
        st.markdown("### 🔧 Comparison Settings")

        # Domain filtering
        st.markdown("#### Domain Filtering")
        domain_filter = st.text_input("Filter by domain (e.g., api.example.com)", key="har_domain_filter")
        exclude_domains = st.text_area("Exclude domains (one per line)", key="har_exclude_domains")

        # Resource type filtering
        st.markdown("#### Resource Type Filtering")
        resource_types = st.multiselect(
            "Include resource types",
            ["xhr", "document", "script", "stylesheet", "image", "font", "other"],
            default=["xhr", "document"],
            key="har_resource_types"
        )

        # Comparison options
        st.markdown("#### Comparison Options")
        compare_headers = st.checkbox("Compare Headers", value=True, key="har_compare_headers")
        compare_content = st.checkbox("Compare Response Content", value=True, key="har_compare_content")
        compare_timing = st.checkbox("Compare Response Times", value=True, key="har_compare_timing")
        ignore_params = st.checkbox("Ignore Dynamic Parameters", value=True, key="har_ignore_params")

        # Analysis options
        st.markdown("#### Analysis Options")
        show_performance = st.checkbox("Performance Analysis", value=True, key="har_show_performance")
        show_security = st.checkbox("Security Analysis", value=True, key="har_show_security")
        show_detailed_diff = st.checkbox("Show Detailed Differences", value=False, key="har_show_detailed_diff")

    # Main content area
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 📁 Upload First HAR File")
        file1 = st.file_uploader("Choose first HAR file", type=['har', 'json', 'chlsj'], key="har_file1_upload")
        if file1:
            st.success(f"✅ {file1.name} loaded ({file1.size} bytes)")

            # Quick preview
            try:
                file1_content = json.loads(file1.read())
                file1.seek(0)  # Reset file pointer

                if 'log' in file1_content and 'entries' in file1_content['log']:
                    entries_count = len(file1_content['log']['entries'])
                    st.info(f"📊 {entries_count} requests found")
                elif 'entries' in file1_content:
                    entries_count = len(file1_content['entries'])
                    st.info(f"📊 {entries_count} requests found")
                elif isinstance(file1_content, list):
                    st.info(f"📊 {len(file1_content)} requests found")
                else:
                    st.info("📊 Single request entry")
            except:
                st.warning("⚠️ Could not preview file structure")

    with col2:
        st.markdown("### 📁 Upload Second HAR File")
        file2 = st.file_uploader("Choose second HAR file", type=['har', 'json', 'chlsj'], key="har_file2_upload")
        if file2:
            st.success(f"✅ {file2.name} loaded ({file2.size} bytes)")

            # Quick preview
            try:
                file2_content = json.loads(file2.read())
                file2.seek(0)  # Reset file pointer

                if 'log' in file2_content and 'entries' in file2_content['log']:
                    entries_count = len(file2_content['log']['entries'])
                    st.info(f"📊 {entries_count} requests found")
                elif 'entries' in file2_content:
                    entries_count = len(file2_content['entries'])
                    st.info(f"📊 {entries_count} requests found")
                elif isinstance(file2_content, list):
                    st.info(f"📊 {len(file2_content)} requests found")
                else:
                    st.info("📊 Single request entry")
            except:
                st.warning("⚠️ Could not preview file structure")

    if file1 and file2:
        st.markdown("---")

        # Comparison controls
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            st.markdown("### 🔍 Ready to Compare")
        with col2:
            if st.button("🔍 Compare Files", type="primary", use_container_width=True, key="har_compare_button"):
                st.session_state['run_comparison'] = True
        with col3:
            if st.button("📊 Quick Stats", use_container_width=True, key="har_quick_stats_button"):
                st.session_state['show_quick_stats'] = True

        # Show quick stats if requested
        if st.session_state.get('show_quick_stats', False):
            render_quick_har_stats(file1, file2)

        # Run comparison if requested
        if st.session_state.get('run_comparison', False):
            render_comprehensive_har_comparison(
                file1, file2,
                domain_filter, exclude_domains, resource_types,
                compare_headers, compare_content, compare_timing, ignore_params,
                show_performance, show_security, show_detailed_diff
            )
            st.session_state['run_comparison'] = False

def render_quick_har_stats(file1, file2):
    """Show quick statistics for both HAR files"""
    st.markdown("### 📊 Quick File Statistics")

    try:
        # Parse both files
        file1_data = json.loads(file1.read())
        file1.seek(0)
        file2_data = json.loads(file2.read())
        file2.seek(0)

        # Extract entries
        entries1 = extract_har_entries(file1_data)
        entries2 = extract_har_entries(file2_data)

        # Create comparison table
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### 📁 File 1")
            st.metric("Total Requests", len(entries1))
            if entries1:
                domains1 = set(get_domain_from_url(e.get('url', '')) for e in entries1 if e.get('url'))
                st.metric("Unique Domains", len(domains1))
                methods1 = [e.get('method', 'GET') for e in entries1 if e.get('method')]
                st.metric("Most Common Method", max(set(methods1), key=methods1.count) if methods1 else "N/A")

        with col2:
            st.markdown("#### 📁 File 2")
            st.metric("Total Requests", len(entries2))
            if entries2:
                domains2 = set(get_domain_from_url(e.get('url', '')) for e in entries2 if e.get('url'))
                st.metric("Unique Domains", len(domains2))
                methods2 = [e.get('method', 'GET') for e in entries2 if e.get('method')]
                st.metric("Most Common Method", max(set(methods2), key=methods2.count) if methods2 else "N/A")

        with col3:
            st.markdown("#### 🔍 Quick Comparison")
            diff_requests = len(entries2) - len(entries1)
            st.metric("Request Count Diff", diff_requests, delta=diff_requests)

            if entries1 and entries2:
                domains1 = set(get_domain_from_url(e.get('url', '')) for e in entries1 if e.get('url'))
                domains2 = set(get_domain_from_url(e.get('url', '')) for e in entries2 if e.get('url'))
                common_domains = len(domains1 & domains2)
                st.metric("Common Domains", common_domains)

        # Show domain breakdown
        if entries1 or entries2:
            st.markdown("#### 🌐 Domain Breakdown")

            col1, col2 = st.columns(2)

            with col1:
                if entries1:
                    domains1 = [get_domain_from_url(e.get('url', '')) for e in entries1 if e.get('url')]
                    if domains1:
                        domain_counts1 = pd.Series(domains1).value_counts().head(10)
                        st.markdown("**File 1 - Top Domains:**")
                        for domain, count in domain_counts1.items():
                            st.write(f"• {domain}: {count} requests")

            with col2:
                if entries2:
                    domains2 = [get_domain_from_url(e.get('url', '')) for e in entries2 if e.get('url')]
                    if domains2:
                        domain_counts2 = pd.Series(domains2).value_counts().head(10)
                        st.markdown("**File 2 - Top Domains:**")
                        for domain, count in domain_counts2.items():
                            st.write(f"• {domain}: {count} requests")

    except Exception as e:
        st.error(f"Error analyzing files: {str(e)}")

def extract_har_entries(data):
    """Extract entries from HAR data in various formats"""
    if isinstance(data, dict):
        if 'log' in data and 'entries' in data['log']:
            return data['log']['entries']
        elif 'entries' in data:
            return data['entries']
        else:
            return [data]
    elif isinstance(data, list):
        return data
    else:
        return []

def get_domain_from_url(url):
    """Extract domain from URL"""
    try:
        if not url:
            return "unknown"
        return urlparse(url).netloc or "unknown"
    except:
        return "unknown"

def render_comprehensive_har_comparison(file1, file2, domain_filter, exclude_domains, resource_types,
                                      compare_headers, compare_content, compare_timing, ignore_params,
                                      show_performance, show_security, show_detailed_diff):
    """Render comprehensive HAR file comparison"""
    st.markdown("### 🔍 Comprehensive Comparison Results")

    try:
        with st.spinner("🔄 Analyzing and comparing HAR files..."):
            # Parse files
            file1_data = json.loads(file1.read())
            file1.seek(0)
            file2_data = json.loads(file2.read())
            file2.seek(0)

            # Extract and normalize entries
            entries1 = extract_har_entries(file1_data)
            entries2 = extract_har_entries(file2_data)

            # Apply filters
            filtered_entries1 = apply_har_filters(entries1, domain_filter, exclude_domains, resource_types)
            filtered_entries2 = apply_har_filters(entries2, domain_filter, exclude_domains, resource_types)

            # Perform comparison
            comparison_result = compare_har_entries(
                filtered_entries1, filtered_entries2,
                compare_headers, compare_content, compare_timing, ignore_params
            )

            # Display results
            display_comparison_summary(comparison_result, file1.name, file2.name)

            if show_performance:
                display_performance_analysis(comparison_result)

            if show_security:
                display_security_analysis(comparison_result)

            if show_detailed_diff:
                display_detailed_differences(comparison_result)

            # Side-by-side comparison
            display_side_by_side_comparison(filtered_entries1, filtered_entries2, file1.name, file2.name)

    except Exception as e:
        st.error(f"❌ Comparison failed: {str(e)}")
        st.exception(e)

def apply_har_filters(entries, domain_filter, exclude_domains, resource_types):
    """Apply filters to HAR entries"""
    filtered = entries.copy()

    # Domain filtering
    if domain_filter:
        filtered = [e for e in filtered if domain_filter.lower() in (get_domain_from_url(e.get('url', '')) or '').lower()]

    # Exclude domains
    if exclude_domains:
        exclude_list = [d.strip().lower() for d in exclude_domains.split('\n') if d.strip()]
        filtered = [e for e in filtered if not any(excl in (get_domain_from_url(e.get('url', '')) or '').lower() for excl in exclude_list)]

    # Resource type filtering
    if resource_types:
        filtered = [e for e in filtered if detect_resource_type(e) in resource_types]

    return filtered

def detect_resource_type(entry):
    """Detect resource type from HAR entry"""
    if not entry:
        return 'other'

    url = (entry.get('url') or '').lower()

    # Check response content type
    if 'response' in entry and 'content' in entry['response']:
        mime_type = (entry['response']['content'].get('mimeType') or '').lower()
        if 'json' in mime_type or 'xml' in mime_type:
            return 'xhr'
        elif 'javascript' in mime_type:
            return 'script'
        elif 'css' in mime_type:
            return 'stylesheet'
        elif 'image' in mime_type:
            return 'image'
        elif 'font' in mime_type:
            return 'font'
        elif 'html' in mime_type:
            return 'document'

    # Fallback to URL-based detection
    if url:
        if any(ext in url for ext in ['.js', '.jsx']):
            return 'script'
        elif any(ext in url for ext in ['.css', '.scss']):
            return 'stylesheet'
        elif any(ext in url for ext in ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp']):
            return 'image'
        elif any(ext in url for ext in ['.woff', '.woff2', '.ttf', '.otf']):
            return 'font'
        elif 'api' in url or 'json' in url:
            return 'xhr'
        elif '.html' in url or url.endswith('/'):
            return 'document'

    return 'other'

def compare_har_entries(entries1, entries2, compare_headers, compare_content, compare_timing, ignore_params):
    """Compare HAR entries and return detailed results"""

    # Create endpoint maps
    endpoints1 = create_endpoint_map(entries1, ignore_params)
    endpoints2 = create_endpoint_map(entries2, ignore_params)

    # Find common and unique endpoints
    all_endpoints = set(endpoints1.keys()) | set(endpoints2.keys())
    common_endpoints = set(endpoints1.keys()) & set(endpoints2.keys())
    unique_file1 = set(endpoints1.keys()) - set(endpoints2.keys())
    unique_file2 = set(endpoints2.keys()) - set(endpoints1.keys())

    # Detailed comparison
    detailed_differences = []
    response_time_diffs = []
    status_changes = 0
    content_changes = 0
    header_changes = 0

    for endpoint in common_endpoints:
        entry1 = endpoints1[endpoint]
        entry2 = endpoints2[endpoint]

        diff = {
            'endpoint': endpoint,
            'url': entry1.get('url', ''),
            'method': entry1.get('method', 'GET'),
            'changes': []
        }

        # Compare response times
        if compare_timing:
            time1 = get_response_time(entry1)
            time2 = get_response_time(entry2)
            if time1 is not None and time2 is not None:
                time_diff = time2 - time1
                response_time_diffs.append(time_diff)
                if abs(time_diff) > 100:  # 100ms threshold
                    diff['changes'].append({
                        'type': 'timing',
                        'old_value': f"{time1}ms",
                        'new_value': f"{time2}ms",
                        'difference': f"{time_diff:+.0f}ms"
                    })

        # Compare status codes
        status1 = get_status_code(entry1)
        status2 = get_status_code(entry2)
        if status1 != status2:
            status_changes += 1
            diff['changes'].append({
                'type': 'status_code',
                'old_value': status1,
                'new_value': status2
            })

        # Compare headers
        if compare_headers:
            headers_diff = compare_response_headers(entry1, entry2)
            if headers_diff:
                header_changes += 1
                diff['changes'].append({
                    'type': 'headers',
                    'details': headers_diff
                })

        # Compare content
        if compare_content:
            content_diff = compare_response_content(entry1, entry2)
            if content_diff:
                content_changes += 1
                diff['changes'].append({
                    'type': 'content',
                    'details': content_diff
                })

        if diff['changes']:
            detailed_differences.append(diff)

    return {
        'total_endpoints': len(all_endpoints),
        'common_endpoints': len(common_endpoints),
        'unique_file1': len(unique_file1),
        'unique_file2': len(unique_file2),
        'unique_file1_list': list(unique_file1),
        'unique_file2_list': list(unique_file2),
        'response_time_diffs': response_time_diffs,
        'avg_response_time_diff': sum(response_time_diffs) / len(response_time_diffs) if response_time_diffs else 0,
        'status_changes': status_changes,
        'content_changes': content_changes,
        'header_changes': header_changes,
        'detailed_differences': detailed_differences,
        'entries1_count': len(entries1),
        'entries2_count': len(entries2)
    }

def create_endpoint_map(entries, ignore_params):
    """Create endpoint map for comparison"""
    endpoint_map = {}
    for entry in entries:
        url = entry.get('url', '')
        method = entry.get('method', 'GET')

        if ignore_params and '?' in url:
            url = url.split('?')[0]

        key = f"{method}|{url}"
        endpoint_map[key] = entry

    return endpoint_map

def get_response_time(entry):
    """Extract response time from HAR entry"""
    if 'timings' in entry:
        timings = entry['timings']
        total_time = 0
        for key in ['wait', 'receive', 'send']:
            if key in timings and timings[key] > 0:
                total_time += timings[key]
        return total_time if total_time > 0 else None
    elif 'time' in entry:
        return entry['time']
    elif 'duration' in entry:
        return entry['duration']
    return None

def get_status_code(entry):
    """Extract status code from HAR entry"""
    if 'response' in entry and 'status' in entry['response']:
        return entry['response']['status']
    elif 'status' in entry:
        return entry['status']
    return 0

def compare_response_headers(entry1, entry2):
    """Compare response headers between two entries"""
    headers1 = get_response_headers(entry1)
    headers2 = get_response_headers(entry2)

    changes = {'added': {}, 'removed': {}, 'modified': {}}

    all_headers = set(headers1.keys()) | set(headers2.keys())

    for header in all_headers:
        if header in headers1 and header in headers2:
            if headers1[header] != headers2[header]:
                changes['modified'][header] = {
                    'old': headers1[header],
                    'new': headers2[header]
                }
        elif header in headers1:
            changes['removed'][header] = headers1[header]
        else:
            changes['added'][header] = headers2[header]

    return changes if any(changes.values()) else None

def compare_response_content(entry1, entry2):
    """Compare response content between two entries"""
    content1 = get_response_content(entry1)
    content2 = get_response_content(entry2)

    if content1 == content2:
        return None

    # Calculate content similarity
    if content1 and content2:
        # Simple similarity check
        similarity = len(set(content1.split()) & set(content2.split())) / max(len(set(content1.split())), len(set(content2.split())), 1)
        return {
            'similarity': similarity,
            'size_diff': len(content2) - len(content1),
            'changed': True
        }

    return {'changed': True, 'size_diff': len(content2 or '') - len(content1 or '')}

def get_response_headers(entry):
    """Extract response headers from HAR entry"""
    if 'response' in entry and 'headers' in entry['response']:
        headers = entry['response']['headers']
        if isinstance(headers, list):
            return {h.get('name', ''): h.get('value', '') for h in headers}
        elif isinstance(headers, dict):
            return headers
    elif 'response_headers' in entry:
        return entry['response_headers']
    return {}

def get_response_content(entry):
    """Extract response content from HAR entry"""
    if 'response' in entry and 'content' in entry['response']:
        content = entry['response']['content']
        return content.get('text', '') or content.get('body', '')
    elif 'response_body' in entry:
        return entry['response_body']
    return ''

def display_comparison_summary(result, file1_name, file2_name):
    """Display comparison summary with metrics"""
    st.markdown("#### 📊 Comparison Summary")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Common Endpoints",
            result['common_endpoints'],
            delta=f"{result['common_endpoints']/max(result['total_endpoints'], 1)*100:.1f}% overlap"
        )

    with col2:
        st.metric(
            "Response Time Diff",
            f"{result['avg_response_time_diff']:+.0f}ms",
            delta="Faster" if result['avg_response_time_diff'] < 0 else "Slower" if result['avg_response_time_diff'] > 0 else "Same"
        )

    with col3:
        st.metric(
            "Status Code Changes",
            result['status_changes'],
            delta=f"{result['status_changes']/max(result['common_endpoints'], 1)*100:.1f}% changed" if result['status_changes'] > 0 else None
        )

    with col4:
        st.metric(
            "Content Changes",
            result['content_changes'],
            delta=f"{result['content_changes']/max(result['common_endpoints'], 1)*100:.1f}% changed" if result['content_changes'] > 0 else None
        )

    # Detailed breakdown
    st.markdown("#### 📋 Detailed Breakdown")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**📁 File Comparison:**")
        st.write(f"• **{file1_name}**: {result['entries1_count']} total requests")
        st.write(f"• **{file2_name}**: {result['entries2_count']} total requests")
        st.write(f"• **Common endpoints**: {result['common_endpoints']}")
        st.write(f"• **Unique to File 1**: {result['unique_file1']}")
        st.write(f"• **Unique to File 2**: {result['unique_file2']}")

    with col2:
        st.markdown("**🔍 Change Analysis:**")
        st.write(f"• **Status code changes**: {result['status_changes']}")
        st.write(f"• **Header changes**: {result['header_changes']}")
        st.write(f"• **Content changes**: {result['content_changes']}")

        if result['response_time_diffs']:
            fastest_improvement = min(result['response_time_diffs'])
            slowest_regression = max(result['response_time_diffs'])
            st.write(f"• **Best improvement**: {fastest_improvement:.0f}ms")
            st.write(f"• **Worst regression**: {slowest_regression:.0f}ms")

def display_performance_analysis(result):
    """Display performance analysis"""
    st.markdown("#### ⚡ Performance Analysis")

    if result['response_time_diffs']:
        # Performance metrics
        times = result['response_time_diffs']

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Average Change", f"{np.mean(times):.0f}ms")
            st.metric("Median Change", f"{np.median(times):.0f}ms")

        with col2:
            improved_count = sum(1 for t in times if t < -50)  # 50ms improvement threshold
            st.metric("Improved Endpoints", improved_count)

            regressed_count = sum(1 for t in times if t > 50)  # 50ms regression threshold
            st.metric("Regressed Endpoints", regressed_count)

        with col3:
            st.metric("Best Improvement", f"{min(times):.0f}ms")
            st.metric("Worst Regression", f"{max(times):.0f}ms")

        # Performance distribution chart
        if len(times) > 1:
            import plotly.graph_objects as go

            fig = go.Figure()
            fig.add_trace(go.Histogram(
                x=times,
                nbinsx=20,
                name="Response Time Changes",
                marker_color='lightblue'
            ))

            fig.update_layout(
                title="Response Time Change Distribution",
                xaxis_title="Time Change (ms)",
                yaxis_title="Number of Endpoints",
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No timing data available for performance analysis")

def display_security_analysis(result):
    """Display security analysis"""
    st.markdown("#### 🔒 Security Analysis")

    # This is a placeholder for security analysis
    # In a real implementation, you would analyze:
    # - HTTP vs HTTPS usage
    # - Security headers
    # - Authentication changes
    # - Error responses that might leak information

    security_issues = []

    # Check for status code changes that might indicate security issues
    if result['status_changes'] > 0:
        security_issues.append(f"⚠️ {result['status_changes']} endpoints changed status codes - review for authentication/authorization changes")

    # Check for significant content changes
    if result['content_changes'] > result['common_endpoints'] * 0.3:
        security_issues.append(f"⚠️ High number of content changes ({result['content_changes']}) - review for data exposure")

    if security_issues:
        st.warning("Security considerations found:")
        for issue in security_issues:
            st.write(f"• {issue}")
    else:
        st.success("✅ No obvious security concerns detected")

def display_detailed_differences(result):
    """Display detailed differences"""
    st.markdown("#### 🔍 Detailed Differences")

    if result['detailed_differences']:
        # Show differences in an expandable format
        for i, diff in enumerate(result['detailed_differences'][:20]):  # Limit to first 20
            with st.expander(f"🔄 {diff['method']} {diff['url'][:80]}{'...' if len(diff['url']) > 80 else ''}"):
                for change in diff['changes']:
                    if change['type'] == 'timing':
                        st.write(f"⏱️ **Timing**: {change['old_value']} → {change['new_value']} ({change['difference']})")
                    elif change['type'] == 'status_code':
                        st.write(f"📊 **Status**: {change['old_value']} → {change['new_value']}")
                    elif change['type'] == 'headers':
                        st.write("📋 **Headers changed:**")
                        details = change['details']
                        if details.get('added'):
                            st.write("  • Added:", details['added'])
                        if details.get('removed'):
                            st.write("  • Removed:", details['removed'])
                        if details.get('modified'):
                            st.write("  • Modified:", details['modified'])
                    elif change['type'] == 'content':
                        details = change['details']
                        if 'similarity' in details:
                            st.write(f"📝 **Content**: {details['similarity']*100:.1f}% similar, size changed by {details['size_diff']} bytes")
                        else:
                            st.write(f"📝 **Content**: Changed (size diff: {details['size_diff']} bytes)")

        if len(result['detailed_differences']) > 20:
            st.info(f"Showing first 20 of {len(result['detailed_differences'])} differences")
    else:
        st.info("No detailed differences found")

def display_side_by_side_comparison(entries1, entries2, file1_name, file2_name):
    """Display side-by-side comparison"""
    st.markdown("#### 🔄 Side-by-Side Comparison")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"**📁 {file1_name}**")
        if entries1:
            df1 = pd.DataFrame([{
                'Method': e.get('method', 'GET'),
                'URL': e.get('url', '')[:50] + '...' if len(e.get('url', '')) > 50 else e.get('url', ''),
                'Status': get_status_code(e),
                'Time (ms)': get_response_time(e) or 0,
                'Domain': get_domain_from_url(e.get('url', ''))
            } for e in entries1[:100]])  # Limit to 100 for performance

            st.dataframe(df1, use_container_width=True, height=400)
        else:
            st.info("No entries to display")

    with col2:
        st.markdown(f"**📁 {file2_name}**")
        if entries2:
            df2 = pd.DataFrame([{
                'Method': e.get('method', 'GET'),
                'URL': e.get('url', '')[:50] + '...' if len(e.get('url', '')) > 50 else e.get('url', ''),
                'Status': get_status_code(e),
                'Time (ms)': get_response_time(e) or 0,
                'Domain': get_domain_from_url(e.get('url', ''))
            } for e in entries2[:100]])  # Limit to 100 for performance

            st.dataframe(df2, use_container_width=True, height=400)
        else:
            st.info("No entries to display")

def render_karate_test_generator_tab():
    """Karate test generator tab"""
    try:
        from karate_test_generator import KarateTestGenerator

        st.markdown("## 🤖 Karate Test Generator")

        generator = KarateTestGenerator()
        uploaded_file = st.file_uploader("Upload API Specification", type=['json', 'yaml'], key="karate_file_upload")

        if uploaded_file:
            st.success("✅ API specification uploaded!")

            col1, col2 = st.columns(2)
            with col1:
                test_types = st.multiselect("Test Types", ["Functional", "Performance", "Security"], default=["Functional"], key="karate_test_types")
            with col2:
                environment = st.selectbox("Environment", ["Development", "Staging", "Production"], key="karate_environment")

            if st.button("🚀 Generate Tests", type="primary", key="karate_generate_button"):
                with st.spinner("Generating tests..."):
                    try:
                        test_content = generator.generate_from_file(uploaded_file, test_types, environment)
                        st.success("✅ Tests generated!")
                        st.code(test_content, language="gherkin")
                        st.download_button("📥 Download", data=test_content, file_name="tests.feature", key="karate_download_button")
                    except Exception as e:
                        st.error(f"Generation failed: {str(e)}")
    except ImportError:
        st.error("❌ Karate Test Generator not found")

def render_api_documentation_tab():
    """API documentation tab"""
    try:
        from api_documentation_tab import render_api_documentation_tab
        render_api_documentation_tab()
    except ImportError:
        st.error("❌ API Documentation module not found")

def render_figma_validation_tab():
    """Figma validation tab"""
    try:
        from figma_validation_tab import render_figma_validation_tab
        render_figma_validation_tab()
    except ImportError:
        st.error("❌ Figma Validation module not found")

def render_rc_testing_tab():
    """RC testing tab"""
    try:
        from rc_testing_tab import render_rc_testing_tab
        render_rc_testing_tab()
    except ImportError:
        st.error("❌ RC Testing module not found")

def render_emulator_testing_tab():
    """Emulator testing tab"""
    try:
        from emulator_testing_tab import render_emulator_testing_tab
        render_emulator_testing_tab()
    except ImportError:
        st.error("❌ Emulator Testing module not found")

def render_chain_testing_tab():
    """Chain testing tab"""
    try:
        from chain_testing_interface import render_chain_testing_interface
        class MockDriver:
            def is_connected(self): return False
            def get_connection_status(self): return {"connected": False}
        render_chain_testing_interface(MockDriver())
    except ImportError:
        st.error("❌ Chain Testing module not found")

# Run the app
if __name__ == "__main__":
    main()
