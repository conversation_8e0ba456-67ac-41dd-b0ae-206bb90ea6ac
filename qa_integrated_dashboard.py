#!/usr/bin/env python3
import streamlit as st

# Configure Streamlit for better performance - MUST BE FIRST
st.set_page_config(
    page_title="QA Security Dashboard",
    page_icon="🔒",
    layout="wide",
    initial_sidebar_state="collapsed"
)

import os
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime
import uuid
import time
from typing import Dict, List, Any, Optional, Union
import re
import sqlite3
import tempfile
import hashlib
import secrets
import string
import base64
from urllib.parse import urlparse, parse_qs
from io import BytesIO
# Optional matplotlib imports for advanced visualizations
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    from matplotlib.backends.backend_pdf import PdfPages
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None
    sns = None
    PdfPages = None
# Optional xlsxwriter for Excel export functionality
try:
    import xlsxwriter
    XLSXWRITER_AVAILABLE = True
except ImportError:
    XLSXWRITER_AVAILABLE = False
    xlsxwriter = None
from datetime import timedelta
from qa_knowledge_base import QAKnowledgeBase
from api_documentation_tab import render_api_documentation_tab
from figma_validation_tab import render_figma_validation_tab
from rc_testing_tab_simple import render_rc_testing_tab
from emulator_testing_tab import render_emulator_testing_tab

# Import Claude AI agent if available
try:
    from claude_agent import ClaudeAgent
    CLAUDE_AVAILABLE = True
except ImportError:
    CLAUDE_AVAILABLE = False
    # Create a simple fallback agent
    class ClaudeAgent:
        def analyze_logs(self, log_data, prompt=None):
            return ["Claude AI agent not available. Please check that claude_agent.py exists."]

# Import Karate Test Generator
try:
    from karate_test_generator import KarateTestGenerator
    KARATE_GENERATOR_AVAILABLE = True
except ImportError:
    KARATE_GENERATOR_AVAILABLE = False

# Page configuration already set at the top for performance

# Initialize knowledge base (only once per session)
@st.cache_resource
def initialize_knowledge_base():
    """Initialize knowledge base with caching to prevent multiple initializations"""
    try:
        print("Initializing knowledge base...")
        kb = QAKnowledgeBase()
        print("Knowledge base initialized successfully")
        return kb
    except Exception as e:
        print(f"Error initializing knowledge base: {str(e)}")
        st.error(f"Error initializing knowledge base: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# Get cached knowledge base instance
kb = initialize_knowledge_base()

# Initialize session state variables
if "log_data" not in st.session_state:
    st.session_state.log_data = None
if "log_df" not in st.session_state:
    st.session_state.log_df = None
if "ai_analysis" not in st.session_state:
    st.session_state.ai_analysis = None
if "analysis_history" not in st.session_state:
    st.session_state.analysis_history = []
if "selected_analysis" not in st.session_state:
    st.session_state.selected_analysis = None
if "current_tab" not in st.session_state:
    st.session_state.current_tab = "dashboard"
if "filter_settings" not in st.session_state:
    st.session_state.filter_settings = {
        "status_filter": [],
        "host_filter": [],
        "method_filter": [],
        "time_range": [0, 10000],
        "search_query": ""
    }
if "test_context" not in st.session_state:
    st.session_state.test_context = ""
if "test_tags" not in st.session_state:
    st.session_state.test_tags = ""
if "qa_report" not in st.session_state:
    st.session_state.qa_report = None
if "karate_generator" not in st.session_state:
    st.session_state.karate_generator = None
if "intellij_project_path" not in st.session_state:
    st.session_state.intellij_project_path = ""
if "test_opportunities" not in st.session_state:
    st.session_state.test_opportunities = []

# API Comparison session state variables
if "log_data_b" not in st.session_state:
    st.session_state.log_data_b = None
if "log_df_b" not in st.session_state:
    st.session_state.log_df_b = None
if "comparison_results" not in st.session_state:
    st.session_state.comparison_results = None
if "comparison_summary" not in st.session_state:
    st.session_state.comparison_summary = None
if "comparison_settings" not in st.session_state:
    st.session_state.comparison_settings = {
        "domain_filter_enabled": False,
        "domains": [],
        "exclude_static_resources": False,
        "excluded_extensions": [".js", ".css", ".woff", ".woff2", ".ttf", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico"],
        "custom_exclusions": [],
        "http_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
        "status_codes": [],
        "url_patterns": [],
        "ignore_fields": ["timestamp", "date", "time", "id", "uuid"]
    }

# Apply custom styling
def apply_custom_styling():
    """Apply custom styling for the dashboard"""
    st.markdown("""
    <style>
    /* Professional Dark Theme - Enterprise SaaS Style */
    :root {
        --primary-color: #6366f1;
        --primary-dark: #4f46e5;
        --primary-light: #818cf8;
        --secondary-color: #64748b;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #06b6d4;

        /* Dark Theme Colors */
        --background-color: #0f172a;
        --surface-color: #1e293b;
        --card-bg: #1e293b;
        --card-hover-bg: #334155;
        --border-color: #334155;
        --border-hover-color: #475569;

        /* Text Colors */
        --text-color: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
        --text-disabled: #64748b;

        /* Shadows for Dark Theme */
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

        /* Accent Colors for Dark Theme */
        --accent-blue: #3b82f6;
        --accent-purple: #8b5cf6;
        --accent-pink: #ec4899;
        --accent-green: #10b981;
        --accent-orange: #f97316;
    }

    /* Global Dark Theme Styles */
    .stApp {
        background-color: var(--background-color) !important;
        color: var(--text-color) !important;
    }

    .main .block-container {
        background-color: var(--background-color) !important;
        color: var(--text-color) !important;
    }

    /* Base styling */
    .stApp {
        background-color: var(--background-color);
        color: var(--text-color);
    }

    /* Sidebar */
    .css-1d391kg, .css-1lcbmhc {
        background-color: var(--light-accent);
    }

    /* Headers */
    h1, h2, h3, h4, h5, h6 {
        color: var(--text-color) !important;
    }

    /* Cards */
    .card {
        background-color: var(--light-accent);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        margin-bottom: 1rem;
    }

    /* Modern Dark Card Styling */
    .hyper-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .hyper-card:hover {
        background: var(--card-hover-bg);
        border-color: var(--border-hover-color);
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    /* Metric Cards for Dark Theme */
    .metric-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        height: 100%;
    }

    .metric-card:hover {
        background: var(--card-hover-bg);
        border-color: var(--primary-color);
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: var(--text-color);
    }

    .metric-label {
        font-size: 0.875rem;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }

    /* Professional Icons for Dark Theme */
    .metric-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.25rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .metric-icon.success {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
    }

    .metric-icon.warning {
        background: linear-gradient(135deg, var(--warning-color), #d97706);
        color: white;
    }

    .metric-icon.danger {
        background: linear-gradient(135deg, var(--danger-color), #dc2626);
        color: white;
    }

    .metric-icon.info {
        background: linear-gradient(135deg, var(--info-color), #0891b2);
        color: white;
    }

    .metric-icon.primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
    }

    .status-2xx {
        background-color: var(--success-color);
        color: white;
    }

    .status-3xx {
        background-color: var(--primary-color);
        color: white;
    }

    .status-4xx {
        background-color: var(--warning-color);
        color: black;
    }

    .status-5xx {
        background-color: var(--error-color);
        color: white;
    }

    /* Tables */
    .dataframe {
        width: 100%;
        border-collapse: collapse;
        color: var(--text-color);
    }

    .dataframe th {
        background-color: var(--secondary-color);
        color: white;
        font-weight: bold;
        padding: 0.5rem;
        text-align: left;
    }

    .dataframe td {
        padding: 0.5rem;
        border-bottom: 1px solid var(--light-accent);
    }

    .dataframe tbody tr:nth-child(even) {
        background-color: rgba(37, 37, 37, 0.7);
    }

    .dataframe tr:hover {
        background-color: var(--light-accent);
    }

    /* Insights */
    .insight-card {
        background-color: var(--light-accent);
        border-left: 4px solid var(--primary-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }

    .insight-title {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    /* Severity indicators */
    .severity-high {
        color: var(--error-color);
        font-weight: bold;
    }

    .severity-medium {
        color: var(--warning-color);
        font-weight: bold;
    }

    .severity-low {
        color: var(--success-color);
        font-weight: bold;
    }

    /* Knowledge base */
    .kb-card {
        background-color: var(--light-accent);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .kb-title {
        font-weight: bold;
        color: var(--primary-color);
    }

    .kb-meta {
        font-size: 0.8rem;
        color: var(--text-color);
    }

    /* Tags */
    .tag {
        display: inline-block;
        background-color: var(--dark-accent);
        color: var(--text-color);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    /* Inputs */
    .stTextInput > div > div > input,
    .stSelectbox > div > div > input,
    .stTextArea > div > div > textarea {
        background-color: #3A3A3A;
        color: var(--text-color);
        border: 1px solid var(--light-accent);
    }

    /* Expanders */
    .streamlit-expanderHeader {
        background-color: var(--light-accent);
        color: var(--text-color);
    }

    /* Tabs */
    .stTabs [data-baseweb="tab-list"] {
        background-color: var(--light-accent);
    }

    .stTabs [data-baseweb="tab"] {
        color: var(--text-color);
    }

    .stTabs [aria-selected="true"] {
        background-color: var(--primary-color);
        color: white;
    }

    /* Recommendations */
    .recommendation {
        background-color: var(--light-accent);
        border-left: 4px solid var(--primary-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }

    /* Buttons */
    .stButton > button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0.5rem 1rem;
    }

    .stButton > button:hover {
        background-color: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: 0 0.5rem 1rem rgba(114, 124, 245, 0.3);
    }

    /* Dark Theme Hero Section */
    .qa-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 4rem 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
    }

    .qa-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, transparent 70%);
        opacity: 0.3;
    }

    .qa-hero-content {
        position: relative;
        z-index: 1;
        text-align: center;
    }

    .qa-hero h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        color: white;
    }

    .qa-hero p {
        font-size: 1.25rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        color: rgba(255,255,255,0.9);
    }

    /* Dark Theme Feature Cards */
    .feature-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 2rem;
        text-align: center;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        background: var(--card-hover-bg);
        box-shadow: var(--shadow-lg);
        transform: translateY(-4px);
        border-color: var(--primary-color);
    }

    .feature-icon {
        width: 4rem;
        height: 4rem;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        transition: all 0.3s ease;
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
        box-shadow: var(--shadow-lg);
    }

    .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-color);
    }

    .feature-description {
        color: var(--text-muted);
        line-height: 1.6;
    }

    /* Dark Theme Status Indicators */
    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        border: 1px solid transparent;
        transition: all 0.3s ease;
    }

    .status-indicator.success {
        background: rgba(16, 185, 129, 0.15);
        color: var(--success-color);
        border-color: rgba(16, 185, 129, 0.3);
    }

    .status-indicator.warning {
        background: rgba(245, 158, 11, 0.15);
        color: var(--warning-color);
        border-color: rgba(245, 158, 11, 0.3);
    }

    .status-indicator.danger {
        background: rgba(239, 68, 68, 0.15);
        color: var(--danger-color);
        border-color: rgba(239, 68, 68, 0.3);
    }

    /* Dark Theme Progress Bars */
    .progress-hyper {
        height: 0.5rem;
        background: var(--border-color);
        border-radius: 0.25rem;
        overflow: hidden;
    }

    .progress-bar-hyper {
        height: 100%;
        border-radius: 0.25rem;
        transition: width 0.6s ease;
    }

    .progress-bar-hyper.success {
        background: linear-gradient(90deg, var(--success-color), #059669);
    }

    .progress-bar-hyper.warning {
        background: linear-gradient(90deg, var(--warning-color), #d97706);
    }

    .progress-bar-hyper.danger {
        background: linear-gradient(90deg, var(--danger-color), #dc2626);
    }

    /* Dark Theme Section Headers */
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: var(--text-muted);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Responsive Grid */
    .hyper-grid {
        display: grid;
        gap: 1.5rem;
    }

    .hyper-grid-2 {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .hyper-grid-3 {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .hyper-grid-4 {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    </style>
    """, unsafe_allow_html=True)

# Apply styling
apply_custom_styling()

# Helper functions for data extraction
def extract_host(url):
    """Extract host from URL"""
    if not url:
        return ""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        return parsed_url.netloc
    except:
        return ""

def extract_path(url):
    """Extract path from URL"""
    if not url:
        return ""
    try:
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        return parsed_url.path
    except:
        return ""

def get_content_type(headers):
    """Get content type from headers"""
    for header in headers:
        if header.get("name", "").lower() == "content-type":
            return header.get("value", "")
    return ""

# Robust Charles Proxy parsing functions
def detect_and_parse_charles_format(data):
    """
    Detect and parse different Charles Proxy log formats
    Returns: (entries_list, format_info)
    """
    format_info = {"detected_format": "Unknown", "structure": "", "entry_count": 0}

    try:
        # Case 1: HAR format (HTTP Archive) - data.log.entries
        if isinstance(data, dict) and "log" in data and isinstance(data["log"], dict) and "entries" in data["log"]:
            entries = data["log"]["entries"]
            format_info = {
                "detected_format": "HAR (HTTP Archive)",
                "structure": "data.log.entries",
                "entry_count": len(entries) if isinstance(entries, list) else 0
            }
            return entries, format_info

        # Case 2: Charles Session format - data.entries
        elif isinstance(data, dict) and "entries" in data and isinstance(data["entries"], list):
            entries = data["entries"]
            format_info = {
                "detected_format": "Charles Session JSON (.chlsj)",
                "structure": "data.entries",
                "entry_count": len(entries)
            }
            return entries, format_info

        # Case 3: Charles Native Sessions format - data.sessions[].entries
        elif isinstance(data, dict) and "sessions" in data and isinstance(data["sessions"], list):
            all_entries = []
            for session in data["sessions"]:
                if isinstance(session, dict) and "entries" in session:
                    all_entries.extend(session["entries"])
            format_info = {
                "detected_format": "Charles Native Sessions",
                "structure": "data.sessions[].entries",
                "entry_count": len(all_entries)
            }
            return all_entries, format_info

        # Case 4: Direct array of entries
        elif isinstance(data, list):
            format_info = {
                "detected_format": "Direct Array",
                "structure": "data[]",
                "entry_count": len(data)
            }
            return data, format_info

        # Case 5: Single entry object
        elif isinstance(data, dict) and any(key in data for key in ["request", "response", "startedDateTime", "time"]):
            format_info = {
                "detected_format": "Single Entry Object",
                "structure": "data (single entry)",
                "entry_count": 1
            }
            return [data], format_info

        # Case 6: Charles export with nested structure
        elif isinstance(data, dict):
            # Look for entries in any nested structure
            for key, value in data.items():
                if isinstance(value, list) and len(value) > 0:
                    # Check if this looks like entries
                    first_item = value[0]
                    if isinstance(first_item, dict) and any(k in first_item for k in ["request", "response", "url", "method"]):
                        format_info = {
                            "detected_format": f"Nested Structure (key: {key})",
                            "structure": f"data.{key}",
                            "entry_count": len(value)
                        }
                        return value, format_info

            # If no entries found, return empty
            format_info = {
                "detected_format": "Unknown Dictionary Structure",
                "structure": "data (no entries found)",
                "entry_count": 0
            }
            return [], format_info

        else:
            format_info = {
                "detected_format": "Unsupported Format",
                "structure": f"Type: {type(data).__name__}",
                "entry_count": 0
            }
            return [], format_info

    except Exception as e:
        format_info = {
            "detected_format": "Parse Error",
            "structure": f"Error: {str(e)}",
            "entry_count": 0
        }
        return [], format_info

def is_paytm_dashboard_api(url, host=""):
    """
    Check if the request is from Paytm dashboard domain
    """
    if not url and not host:
        return False

    # Extract host from URL if not provided
    if not host and url:
        host = extract_host(url)

    # Check for Paytm dashboard domains
    paytm_domains = [
        "dashboard.paytm",
        "dashboard.paytm.com",
        "api.dashboard.paytm.com",
        "paytm.com",
        "api.paytm.com"
    ]

    return any(domain in host.lower() for domain in paytm_domains)

def decode_response_body(response_body, encoding="utf-8"):
    """
    Decode response body from various encodings (base64, gzip, etc.)
    """
    if not response_body:
        return ""

    try:
        # If it's already a string, try to decode it
        if isinstance(response_body, str):
            # Try base64 decoding first
            try:
                import base64
                decoded = base64.b64decode(response_body).decode(encoding)
                return decoded
            except:
                # If base64 fails, return as-is
                return response_body

        # If it's bytes, decode directly
        elif isinstance(response_body, bytes):
            return response_body.decode(encoding)

        return str(response_body)

    except Exception as e:
        # If all decoding fails, return original
        return str(response_body) if response_body else ""

def parse_paytm_api_response(response_body):
    """
    Parse Paytm API response with proper decoding
    """
    if not response_body:
        return {}

    try:
        # First decode the response body
        decoded_body = decode_response_body(response_body)

        # Try to parse as JSON
        import json
        response_data = json.loads(decoded_body)

        # Extract Paytm-specific fields
        paytm_fields = {}

        if isinstance(response_data, dict):
            paytm_fields["statusCode"] = response_data.get("statusCode")
            paytm_fields["message"] = response_data.get("message")
            paytm_fields["data"] = response_data.get("data")
            paytm_fields["error"] = response_data.get("error")
            paytm_fields["success"] = response_data.get("success")
            paytm_fields["responseCode"] = response_data.get("responseCode")
            paytm_fields["responseMessage"] = response_data.get("responseMessage")

        return paytm_fields

    except Exception as e:
        return {"parse_error": str(e)}

# Security Testing Functions
def perform_security_analysis(response_data, headers, url, method):
    """
    Perform comprehensive security analysis on API response
    """
    security_issues = []

    # 1. Check for sensitive data exposure
    sensitive_data_issues = check_sensitive_data_exposure(response_data)
    security_issues.extend(sensitive_data_issues)

    # 2. Check security headers
    header_issues = check_security_headers(headers)
    security_issues.extend(header_issues)

    # 3. Check for authentication issues
    auth_issues = check_authentication_issues(response_data, headers)
    security_issues.extend(auth_issues)

    # 4. Check for injection vulnerabilities
    injection_issues = check_injection_vulnerabilities(response_data, url)
    security_issues.extend(injection_issues)

    # 5. Check for CORS issues
    cors_issues = check_cors_configuration(headers)
    security_issues.extend(cors_issues)

    # 6. Check for information disclosure
    info_disclosure_issues = check_information_disclosure(response_data, headers)
    security_issues.extend(info_disclosure_issues)

    return {
        "total_issues": len(security_issues),
        "issues": security_issues,
        "risk_level": calculate_risk_level(security_issues)
    }

def check_sensitive_data_exposure(response_data):
    """Check for sensitive data in API responses"""
    issues = []

    if not response_data:
        return issues

    response_str = str(response_data).lower()

    # Patterns for sensitive data
    sensitive_patterns = {
        "password": r"password[\"\':\s]*[\"\']\w+",
        "api_key": r"(api[_-]?key|apikey)[\"\':\s]*[\"\']\w+",
        "secret": r"secret[\"\':\s]*[\"\']\w+",
        "token": r"(access[_-]?token|accesstoken)[\"\':\s]*[\"\']\w+",
        "credit_card": r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b",
        "ssn": r"\b\d{3}-\d{2}-\d{4}\b",
        "email": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
        "phone": r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b"
    }

    for data_type, pattern in sensitive_patterns.items():
        if re.search(pattern, response_str):
            issues.append({
                "type": "Sensitive Data Exposure",
                "severity": "HIGH" if data_type in ["password", "api_key", "secret", "token"] else "MEDIUM",
                "description": f"Potential {data_type.replace('_', ' ').title()} found in response",
                "recommendation": f"Remove or mask {data_type.replace('_', ' ')} from API response"
            })

    return issues

def check_security_headers(headers):
    """Check for missing security headers"""
    issues = []

    if not headers:
        return issues

    # Convert headers to dict for easier checking
    header_dict = {}
    if isinstance(headers, list):
        for header in headers:
            if isinstance(header, dict):
                header_dict[header.get('name', '').lower()] = header.get('value', '')

    # Required security headers
    security_headers = {
        "x-content-type-options": "nosniff",
        "x-frame-options": ["DENY", "SAMEORIGIN"],
        "x-xss-protection": "1; mode=block",
        "strict-transport-security": None,
        "content-security-policy": None,
        "referrer-policy": None
    }

    for header_name, expected_value in security_headers.items():
        if header_name not in header_dict:
            issues.append({
                "type": "Missing Security Header",
                "severity": "MEDIUM",
                "description": f"Missing {header_name.title()} header",
                "recommendation": f"Add {header_name} header for enhanced security"
            })
        elif expected_value and header_dict[header_name] not in expected_value:
            if isinstance(expected_value, list):
                issues.append({
                    "type": "Weak Security Header",
                    "severity": "LOW",
                    "description": f"{header_name.title()} header has weak value: {header_dict[header_name]}",
                    "recommendation": f"Set {header_name} to one of: {', '.join(expected_value)}"
                })

    return issues

def check_authentication_issues(response_data, headers):
    """Check for authentication and authorization issues"""
    issues = []

    if not response_data:
        return issues

    response_str = str(response_data).lower()

    # Check for exposed authentication tokens
    auth_patterns = [
        r"bearer\s+[a-zA-Z0-9\-._~+/]+=*",
        r"basic\s+[a-zA-Z0-9+/]+=*",
        r"jwt\s+[a-zA-Z0-9\-._~+/]+=*"
    ]

    for pattern in auth_patterns:
        if re.search(pattern, response_str):
            issues.append({
                "type": "Authentication Token Exposure",
                "severity": "HIGH",
                "description": "Authentication token found in response body",
                "recommendation": "Remove authentication tokens from response body"
            })

    # Check for weak session management
    if "sessionid" in response_str or "session_id" in response_str:
        issues.append({
            "type": "Session Management",
            "severity": "MEDIUM",
            "description": "Session ID found in response body",
            "recommendation": "Avoid exposing session IDs in response body"
        })

    return issues

def check_injection_vulnerabilities(response_data, url):
    """Check for potential injection vulnerabilities"""
    issues = []

    if not response_data or not url:
        return issues

    response_str = str(response_data)

    # Check for SQL injection indicators
    sql_patterns = [
        r"sql\s+error",
        r"mysql_fetch",
        r"ora-\d{5}",
        r"microsoft\s+ole\s+db",
        r"syntax\s+error.*near"
    ]

    for pattern in sql_patterns:
        if re.search(pattern, response_str, re.IGNORECASE):
            issues.append({
                "type": "SQL Injection Vulnerability",
                "severity": "HIGH",
                "description": "Potential SQL injection vulnerability detected",
                "recommendation": "Implement proper input validation and parameterized queries"
            })

    # Check for XSS vulnerabilities
    xss_patterns = [
        r"<script[^>]*>",
        r"javascript:",
        r"on\w+\s*=",
        r"eval\s*\(",
        r"document\.cookie"
    ]

    for pattern in xss_patterns:
        if re.search(pattern, response_str, re.IGNORECASE):
            issues.append({
                "type": "XSS Vulnerability",
                "severity": "HIGH",
                "description": "Potential XSS vulnerability detected",
                "recommendation": "Implement proper output encoding and input validation"
            })

    return issues

def check_cors_configuration(headers):
    """Check CORS configuration for security issues"""
    issues = []

    if not headers:
        return issues

    header_dict = {}
    if isinstance(headers, list):
        for header in headers:
            if isinstance(header, dict):
                header_dict[header.get('name', '').lower()] = header.get('value', '')

    # Check for overly permissive CORS
    if "access-control-allow-origin" in header_dict:
        origin_value = header_dict["access-control-allow-origin"]
        if origin_value == "*":
            issues.append({
                "type": "CORS Misconfiguration",
                "severity": "MEDIUM",
                "description": "Overly permissive CORS policy (Access-Control-Allow-Origin: *)",
                "recommendation": "Restrict CORS to specific trusted domains"
            })

    if "access-control-allow-credentials" in header_dict:
        if header_dict["access-control-allow-credentials"].lower() == "true":
            if header_dict.get("access-control-allow-origin") == "*":
                issues.append({
                    "type": "CORS Security Risk",
                    "severity": "HIGH",
                    "description": "Dangerous CORS configuration: credentials allowed with wildcard origin",
                    "recommendation": "Never use Access-Control-Allow-Credentials: true with Access-Control-Allow-Origin: *"
                })

    return issues

def check_information_disclosure(response_data, headers):
    """Check for information disclosure issues"""
    issues = []

    # Check headers for information disclosure
    if headers:
        header_dict = {}
        if isinstance(headers, list):
            for header in headers:
                if isinstance(header, dict):
                    header_dict[header.get('name', '').lower()] = header.get('value', '')

        # Check for server information disclosure
        if "server" in header_dict:
            issues.append({
                "type": "Information Disclosure",
                "severity": "LOW",
                "description": f"Server information disclosed: {header_dict['server']}",
                "recommendation": "Remove or obfuscate server header"
            })

        if "x-powered-by" in header_dict:
            issues.append({
                "type": "Information Disclosure",
                "severity": "LOW",
                "description": f"Technology stack disclosed: {header_dict['x-powered-by']}",
                "recommendation": "Remove X-Powered-By header"
            })

    # Check response for stack traces or error details
    if response_data:
        response_str = str(response_data).lower()

        error_patterns = [
            r"stack\s+trace",
            r"exception\s+in",
            r"error\s+at\s+line",
            r"debug\s+information",
            r"file\s+not\s+found.*\.php",
            r"warning.*mysql",
            r"fatal\s+error"
        ]

        for pattern in error_patterns:
            if re.search(pattern, response_str):
                issues.append({
                    "type": "Information Disclosure",
                    "severity": "MEDIUM",
                    "description": "Detailed error information disclosed in response",
                    "recommendation": "Implement proper error handling to avoid information disclosure"
                })
                break

    return issues

def calculate_risk_level(security_issues):
    """Calculate overall risk level based on security issues"""
    if not security_issues:
        return "LOW"

    high_count = sum(1 for issue in security_issues if issue.get("severity") == "HIGH")
    medium_count = sum(1 for issue in security_issues if issue.get("severity") == "MEDIUM")

    if high_count > 0:
        return "HIGH"
    elif medium_count > 2:
        return "HIGH"
    elif medium_count > 0:
        return "MEDIUM"
    else:
        return "LOW"

def parse_entry_object(entry, domain_filter=None):
    """
    Parse a single entry object and extract standardized fields
    Handles different entry formats (HAR, Charles native, etc.)
    Supports domain filtering for Paytm dashboard APIs
    """
    if not isinstance(entry, dict):
        return None

    # Initialize with defaults
    parsed_entry = {
        "startedDateTime": "",
        "time": 0,
        "method": "",
        "url": "",
        "status": 0,
        "statusText": "",
        "host": "",
        "path": "",
        "contentType": "",
        "size": 0,
        "entry_id": str(uuid.uuid4()),
        "request_headers": [],
        "request_body": "",
        "response_headers": [],
        "response_body": "",
        "request_cookies": [],
        "response_cookies": [],
        "paytm_fields": {}
    }

    try:
        # Extract basic timing and metadata
        parsed_entry["startedDateTime"] = entry.get("startedDateTime", entry.get("startTime", ""))
        parsed_entry["time"] = entry.get("time", entry.get("duration", 0))

        # Handle different request/response structures
        request = entry.get("request", {})
        response = entry.get("response", {})

        # If no nested request/response, check if entry itself contains request data
        if not request and "method" in entry:
            request = entry
        if not response and "status" in entry:
            response = entry

        # Extract request data
        parsed_entry["method"] = request.get("method", "")
        parsed_entry["url"] = request.get("url", "")
        parsed_entry["host"] = extract_host(parsed_entry["url"])
        parsed_entry["path"] = extract_path(parsed_entry["url"])

        # Apply domain filtering ONLY if specifically requested
        if domain_filter == "paytm" and not is_paytm_dashboard_api(parsed_entry["url"], parsed_entry["host"]):
            return None  # Skip non-Paytm requests only when Paytm filter is enabled

        # Extract response data
        parsed_entry["status"] = response.get("status", 0)
        parsed_entry["statusText"] = response.get("statusText", "")
        parsed_entry["contentType"] = get_content_type(response.get("headers", []))

        # Calculate size
        body_size = response.get("bodySize", 0)
        headers_size = response.get("headersSize", 0)
        parsed_entry["size"] = body_size + headers_size

        # Extract headers and bodies
        parsed_entry["request_headers"] = request.get("headers", [])
        parsed_entry["response_headers"] = response.get("headers", [])

        # Extract request body
        post_data = request.get("postData", {})
        if isinstance(post_data, dict):
            parsed_entry["request_body"] = post_data.get("text", "")
        else:
            parsed_entry["request_body"] = str(post_data) if post_data else ""

        # Extract response body and decode it
        content = response.get("content", {})
        if isinstance(content, dict):
            raw_body = content.get("text", "")
            parsed_entry["response_body"] = decode_response_body(raw_body)
        else:
            parsed_entry["response_body"] = decode_response_body(str(content) if content else "")

        # Extract cookies
        parsed_entry["request_cookies"] = request.get("cookies", [])
        parsed_entry["response_cookies"] = response.get("cookies", [])

        # Parse Paytm-specific fields if this is a Paytm API (always check, regardless of filtering)
        if is_paytm_dashboard_api(parsed_entry["url"], parsed_entry["host"]):
            parsed_entry["paytm_fields"] = parse_paytm_api_response(parsed_entry["response_body"])
            parsed_entry["is_paytm_api"] = True
        else:
            parsed_entry["is_paytm_api"] = False

        # Skip security analysis during initial parsing for speed
        # Security analysis will be done on-demand in the security tab
        parsed_entry["security_analysis"] = None

        return parsed_entry

    except Exception as e:
        print(f"Error parsing entry: {str(e)}")
        return parsed_entry  # Return with defaults

# Function to create log dataframe
@st.cache_data(ttl=300, max_entries=3)  # Cache for 5 minutes, max 3 entries
def create_log_dataframe(log_data, domain_filter=None):
    """Create a DataFrame from log data for Version A with robust parsing and domain filtering"""
    try:
        # Detect format and extract entries using robust parsing
        entries, format_info = detect_and_parse_charles_format(log_data)

        # Log format detection info
        print(f"Version A - Detected format: {format_info['detected_format']}")
        print(f"Version A - Structure: {format_info['structure']}")
        print(f"Version A - Entry count: {format_info['entry_count']}")

        # Get domain filter from session state if not provided (defaults to None = no filtering)
        if domain_filter is None:
            domain_filter = getattr(st.session_state, 'domain_filter', None)

        # Parse each entry using robust parser with domain filtering
        parsed_data = []
        filtered_count = 0
        for entry in entries:
            parsed_entry = parse_entry_object(entry, domain_filter=domain_filter)
            if parsed_entry:
                parsed_entry["version"] = "A"  # Mark as Version A
                parsed_data.append(parsed_entry)
            elif domain_filter:
                filtered_count += 1

        # Create DataFrame
        df = pd.DataFrame(parsed_data)

        # Convert time to datetime if data exists
        if "startedDateTime" in df.columns and len(df) > 0:
            try:
                df["startedDateTime"] = pd.to_datetime(df["startedDateTime"], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert startedDateTime to datetime: {str(e)}")

        # Store in session state
        st.session_state.log_df = df
        st.session_state.format_info_a = format_info

        # Log filtering results
        if domain_filter:
            print(f"Domain filtering applied: {domain_filter}")
            print(f"Entries after filtering: {len(parsed_data)}")
            print(f"Entries filtered out: {filtered_count}")

        return df

    except Exception as e:
        print(f"Error in create_log_dataframe: {str(e)}")
        # Return empty DataFrame on error
        df = pd.DataFrame()
        st.session_state.log_df = df
        return df

# Function to create log dataframe for Version B
@st.cache_data(ttl=300, max_entries=3)  # Cache for 5 minutes, max 3 entries
def create_log_dataframe_b(log_data, domain_filter=None):
    """Create a DataFrame from log data for Version B with robust parsing and domain filtering"""
    try:
        # Detect format and extract entries using robust parsing
        entries, format_info = detect_and_parse_charles_format(log_data)

        # Log format detection info
        print(f"Version B - Detected format: {format_info['detected_format']}")
        print(f"Version B - Structure: {format_info['structure']}")
        print(f"Version B - Entry count: {format_info['entry_count']}")

        # Get domain filter from session state if not provided
        if domain_filter is None:
            domain_filter = getattr(st.session_state, 'domain_filter', None)

        # Parse each entry using robust parser with domain filtering
        parsed_data = []
        filtered_count = 0
        for entry in entries:
            parsed_entry = parse_entry_object(entry, domain_filter=domain_filter)
            if parsed_entry:
                parsed_entry["version"] = "B"  # Mark as Version B
                parsed_data.append(parsed_entry)
            elif domain_filter:
                filtered_count += 1

        # Create DataFrame
        df = pd.DataFrame(parsed_data)

        # Convert time to datetime if data exists
        if "startedDateTime" in df.columns and len(df) > 0:
            try:
                df["startedDateTime"] = pd.to_datetime(df["startedDateTime"], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert startedDateTime to datetime: {str(e)}")

        # Store in session state
        st.session_state.log_df_b = df
        st.session_state.format_info_b = format_info

        # Log filtering results
        if domain_filter:
            print(f"Domain filtering applied: {domain_filter}")
            print(f"Entries after filtering: {len(parsed_data)}")
            print(f"Entries filtered out: {filtered_count}")

        return df

    except Exception as e:
        print(f"Error in create_log_dataframe_b: {str(e)}")
        # Return empty DataFrame on error
        df = pd.DataFrame()
        st.session_state.log_df_b = df
        return df

# Function to compare API requests
def compare_api_requests(df_a, df_b):
    """Compare API requests between Version A and Version B"""
    if df_a is None or df_b is None or len(df_a) == 0 or len(df_b) == 0:
        st.session_state.comparison_results = None
        st.session_state.comparison_summary = None
        return

    # Apply filters based on comparison settings
    df_a_filtered = apply_comparison_filters(df_a)
    df_b_filtered = apply_comparison_filters(df_b)

    # Match requests between the two dataframes
    comparison_results = []

    # Track statistics
    total_apis = len(df_a_filtered)
    matched_apis = 0
    mismatched_apis = 0
    missing_apis = 0

    # For each request in Version A, find a matching request in Version B
    for _, row_a in df_a_filtered.iterrows():
        # Find matching requests in Version B based on method and URL
        matches = df_b_filtered[(df_b_filtered["method"] == row_a["method"]) &
                              (df_b_filtered["path"] == row_a["path"])]

        if len(matches) > 0:
            # Take the first match (could be improved to find best match)
            row_b = matches.iloc[0]

            # Compare the requests and responses
            comparison = compare_request_response(row_a, row_b)
            comparison_results.append(comparison)

            if comparison["match_status"] == "MATCH":
                matched_apis += 1
            else:
                mismatched_apis += 1
        else:
            # No matching request found in Version B
            comparison = {
                "request_a": row_a,
                "request_b": None,
                "match_status": "MISSING_IN_B",
                "status_match": False,
                "response_body_match": False,
                "response_headers_match": False,
                "mismatched_fields": [],
                "method": row_a["method"],
                "path": row_a["path"],
                "url": row_a["url"]
            }
            comparison_results.append(comparison)
            missing_apis += 1

    # Check for requests in Version B that are not in Version A
    for _, row_b in df_b_filtered.iterrows():
        matches = df_a_filtered[(df_a_filtered["method"] == row_b["method"]) &
                             (df_a_filtered["path"] == row_b["path"])]

        if len(matches) == 0:
            # Request exists only in Version B
            comparison = {
                "request_a": None,
                "request_b": row_b,
                "match_status": "MISSING_IN_A",
                "status_match": False,
                "response_body_match": False,
                "response_headers_match": False,
                "mismatched_fields": [],
                "method": row_b["method"],
                "path": row_b["path"],
                "url": row_b["url"]
            }
            comparison_results.append(comparison)
            missing_apis += 1
            total_apis += 1

    # Calculate match rate
    match_rate = (matched_apis / total_apis * 100) if total_apis > 0 else 0

    # Create summary
    summary = {
        "total_apis": total_apis,
        "matched_apis": matched_apis,
        "mismatched_apis": mismatched_apis,
        "missing_apis": missing_apis,
        "match_rate": match_rate,
        "a_request_count": len(df_a_filtered),
        "b_request_count": len(df_b_filtered)
    }

    # Store results in session state
    st.session_state.comparison_results = comparison_results
    st.session_state.comparison_summary = summary

# Function to apply comparison filters
def apply_comparison_filters(df):
    """Apply filters based on comparison settings"""
    if df is None or len(df) == 0:
        return df

    filtered_df = df.copy()
    settings = st.session_state.comparison_settings

    # Domain filtering
    if settings["domain_filter_enabled"] and settings["domains"]:
        filtered_df = filtered_df[filtered_df["host"].isin(settings["domains"])]

    # Resource type filtering
    if settings["exclude_static_resources"]:
        # Create a pattern to match static resource extensions
        extensions = settings["excluded_extensions"] + settings["custom_exclusions"]
        if extensions:
            pattern = "|".join([re.escape(ext) for ext in extensions])
            filtered_df = filtered_df[~filtered_df["path"].str.contains(f"({pattern})$", regex=True, na=False)]

    # HTTP method filtering
    if settings["http_methods"]:
        filtered_df = filtered_df[filtered_df["method"].isin(settings["http_methods"])]

    # URL pattern filtering
    if settings["url_patterns"]:
        for pattern in settings["url_patterns"]:
            if pattern:
                filtered_df = filtered_df[filtered_df["url"].str.contains(pattern, regex=True, na=False)]

    return filtered_df

# Function to compare request and response
def compare_request_response(row_a, row_b):
    """Compare request and response between two API calls"""
    # Check if status codes match
    status_match = row_a["status"] == row_b["status"]

    # Compare response bodies if they exist and are JSON
    response_body_match = False
    mismatched_fields = []

    try:
        # Try to parse response bodies as JSON
        body_a = json.loads(row_a["response_body"]) if row_a["response_body"] else {}
        body_b = json.loads(row_b["response_body"]) if row_b["response_body"] else {}

        # Compare JSON structures
        if body_a and body_b:
            response_body_match, mismatched_fields = compare_json(body_a, body_b)
    except:
        # If JSON parsing fails, compare as strings
        response_body_match = row_a["response_body"] == row_b["response_body"]

    # Compare response headers
    headers_a = {h.get("name", "").lower(): h.get("value", "") for h in row_a["response_headers"]}
    headers_b = {h.get("name", "").lower(): h.get("value", "") for h in row_b["response_headers"]}

    # Ignore certain headers like date, server, etc.
    ignore_headers = ["date", "server", "set-cookie", "etag", "last-modified"]
    for header in ignore_headers:
        headers_a.pop(header, None)
        headers_b.pop(header, None)

    response_headers_match = headers_a == headers_b

    # Determine overall match status
    if status_match and response_body_match and response_headers_match:
        match_status = "MATCH"
    else:
        match_status = "MISMATCH"

    # Create comparison result
    comparison = {
        "request_a": row_a,
        "request_b": row_b,
        "match_status": match_status,
        "status_match": status_match,
        "response_body_match": response_body_match,
        "response_headers_match": response_headers_match,
        "mismatched_fields": mismatched_fields,
        "method": row_a["method"],
        "path": row_a["path"],
        "url": row_a["url"]
    }

    return comparison

# Function to compare JSON objects
def compare_json(json_a, json_b, path="", ignore_fields=None):
    """Compare two JSON objects and return differences"""
    if ignore_fields is None:
        ignore_fields = st.session_state.comparison_settings["ignore_fields"]

    mismatched_fields = []
    match = True

    # Handle different types
    if type(json_a) != type(json_b):
        return False, [{"path": path, "value_a": json_a, "value_b": json_b}]

    # Handle dictionaries
    if isinstance(json_a, dict):
        # Check for missing keys
        keys_a = set(json_a.keys())
        keys_b = set(json_b.keys())

        # Check keys only in A
        for key in keys_a - keys_b:
            if key not in ignore_fields and not any(key.startswith(f) for f in ignore_fields):
                match = False
                mismatched_fields.append({
                    "path": f"{path}.{key}" if path else key,
                    "value_a": json_a[key],
                    "value_b": "MISSING"
                })

        # Check keys only in B
        for key in keys_b - keys_a:
            if key not in ignore_fields and not any(key.startswith(f) for f in ignore_fields):
                match = False
                mismatched_fields.append({
                    "path": f"{path}.{key}" if path else key,
                    "value_a": "MISSING",
                    "value_b": json_b[key]
                })

        # Check common keys
        for key in keys_a & keys_b:
            if key not in ignore_fields and not any(key.startswith(f) for f in ignore_fields):
                key_path = f"{path}.{key}" if path else key
                key_match, key_mismatches = compare_json(json_a[key], json_b[key], key_path, ignore_fields)
                if not key_match:
                    match = False
                    mismatched_fields.extend(key_mismatches)

    # Handle lists
    elif isinstance(json_a, list):
        if len(json_a) != len(json_b):
            match = False
            mismatched_fields.append({
                "path": f"{path}",
                "value_a": f"Array length: {len(json_a)}",
                "value_b": f"Array length: {len(json_b)}"
            })
        else:
            # Compare elements (this is a simple implementation, could be improved)
            for i, (item_a, item_b) in enumerate(zip(json_a, json_b)):
                item_path = f"{path}[{i}]"
                item_match, item_mismatches = compare_json(item_a, item_b, item_path, ignore_fields)
                if not item_match:
                    match = False
                    mismatched_fields.extend(item_mismatches)

    # Handle primitive values
    else:
        if json_a != json_b:
            match = False
            mismatched_fields.append({
                "path": path,
                "value_a": json_a,
                "value_b": json_b
            })

    return match, mismatched_fields

# Function to render the API comparison tab
def render_api_comparison_tab():
    """Render the API comparison tab with side-by-side comparison"""
    st.markdown('<h2>API Comparison</h2>', unsafe_allow_html=True)

    # Check if both files are loaded
    if st.session_state.log_data is None or st.session_state.log_data_b is None:
        st.info("Please upload both Version A and Version B files to compare APIs")
        return

    # Check if comparison results exist
    if st.session_state.comparison_results is None:
        st.info("No comparison results available. Please check your filter settings.")
        return

    # Display summary statistics
    summary = st.session_state.comparison_summary

    # Create summary cards
    st.markdown('<h3>Comparison Summary</h3>', unsafe_allow_html=True)

    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{summary["total_apis"]}</div>
            <div class="metric-label">Total APIs</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{summary["matched_apis"]}</div>
            <div class="metric-label">Matched</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{summary["mismatched_apis"]}</div>
            <div class="metric-label">Mismatched</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{summary["missing_apis"]}</div>
            <div class="metric-label">Missing</div>
        </div>
        """, unsafe_allow_html=True)

    with col5:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{summary["match_rate"]:.1f}%</div>
            <div class="metric-label">Match Rate</div>
        </div>
        """, unsafe_allow_html=True)

    # Display comparison results
    st.markdown('<h3>API Comparison Results</h3>', unsafe_allow_html=True)

    # Filter options
    filter_col1, filter_col2, filter_col3 = st.columns(3)

    with filter_col1:
        status_filter = st.selectbox(
            "Filter by status:",
            ["All", "Matched", "Mismatched", "Missing in A", "Missing in B"]
        )

    with filter_col2:
        method_filter = st.multiselect(
            "Filter by HTTP method:",
            ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
            default=[]
        )

    with filter_col3:
        path_filter = st.text_input("Filter by path (contains):")

    # Apply filters to comparison results
    filtered_results = st.session_state.comparison_results.copy()

    if status_filter == "Matched":
        filtered_results = [r for r in filtered_results if r["match_status"] == "MATCH"]
    elif status_filter == "Mismatched":
        filtered_results = [r for r in filtered_results if r["match_status"] == "MISMATCH"]
    elif status_filter == "Missing in A":
        filtered_results = [r for r in filtered_results if r["match_status"] == "MISSING_IN_A"]
    elif status_filter == "Missing in B":
        filtered_results = [r for r in filtered_results if r["match_status"] == "MISSING_IN_B"]

    if method_filter:
        filtered_results = [r for r in filtered_results if r["method"] in method_filter]

    if path_filter:
        filtered_results = [r for r in filtered_results if path_filter.lower() in r["path"].lower()]

    # Display results
    if not filtered_results:
        st.info("No results match the selected filters")
        return

    # Performance optimization: Limit results and add pagination
    results_per_page = 10  # Reduced from 20 for faster rendering
    total_results = len(filtered_results)

    if total_results > results_per_page:
        # Add pagination
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            page = st.number_input("Page", min_value=1, max_value=(total_results // results_per_page) + 1, value=1)

        start_idx = (page - 1) * results_per_page
        end_idx = min(start_idx + results_per_page, total_results)
        displayed_results = filtered_results[start_idx:end_idx]

        st.markdown(f"**Showing {len(displayed_results)} of {total_results} results (Page {page})**")
    else:
        displayed_results = filtered_results
        st.markdown(f"**Showing {len(displayed_results)} results**")

    # Create expandable sections for each comparison
    for i, comparison in enumerate(displayed_results):
        # Determine status color and icon
        if comparison["match_status"] == "MATCH":
            status_color = "green"
            status_icon = "✅"
        elif comparison["match_status"] == "MISMATCH":
            status_color = "orange"
            status_icon = "⚠️"
        elif comparison["match_status"] == "MISSING_IN_A":
            status_color = "red"
            status_icon = "❌"
        elif comparison["match_status"] == "MISSING_IN_B":
            status_color = "red"
            status_icon = "❌"

        # Create expander header
        expander_label = f"{status_icon} {comparison['method']} {comparison['path']} ({comparison['match_status']})"

        with st.expander(expander_label):
            # Side-by-side comparison
            if comparison["match_status"] not in ["MISSING_IN_A", "MISSING_IN_B"]:
                col1, col2 = st.columns(2)

                # Version A
                with col1:
                    st.markdown(f"<h4>Version A</h4>", unsafe_allow_html=True)
                    st.markdown(f"**Status:** {comparison['request_a']['status']} {comparison['request_a']['statusText']}")
                    st.markdown(f"**URL:** {comparison['request_a']['url']}")

                    # Response headers (using collapsible sections instead of nested expanders)
                    if st.checkbox("Show Response Headers", key=f"headers_a_{i}"):
                        st.markdown("**Response Headers:**")
                        for header in comparison['request_a']['response_headers']:
                            st.markdown(f"• **{header.get('name', '')}:** {header.get('value', '')}")

                    # Response body (decoded)
                    if st.checkbox("Show Response Body", key=f"body_a_{i}"):
                        st.markdown("**Response Body:**")
                        response_body = comparison['request_a']['response_body']
                        if response_body:
                            try:
                                # Try to parse as JSON for pretty display
                                import json
                                parsed_json = json.loads(response_body)
                                st.json(parsed_json)
                            except:
                                # If not JSON, show as text with length limit for performance
                                if len(response_body) > 1000:
                                    st.text_area("Response Body", response_body[:1000] + "...", height=200, disabled=True)
                                    st.caption(f"Showing first 1000 characters of {len(response_body)} total")
                                else:
                                    st.code(response_body)
                        else:
                            st.markdown("*No response body*")

                # Version B
                with col2:
                    st.markdown(f"<h4>Version B</h4>", unsafe_allow_html=True)
                    st.markdown(f"**Status:** {comparison['request_b']['status']} {comparison['request_b']['statusText']}")
                    st.markdown(f"**URL:** {comparison['request_b']['url']}")

                    # Response headers (using collapsible sections instead of nested expanders)
                    if st.checkbox("Show Response Headers", key=f"headers_b_{i}"):
                        st.markdown("**Response Headers:**")
                        for header in comparison['request_b']['response_headers']:
                            st.markdown(f"• **{header.get('name', '')}:** {header.get('value', '')}")

                    # Response body (decoded)
                    if st.checkbox("Show Response Body", key=f"body_b_{i}"):
                        st.markdown("**Response Body:**")
                        response_body = comparison['request_b']['response_body']
                        if response_body:
                            try:
                                # Try to parse as JSON for pretty display
                                import json
                                parsed_json = json.loads(response_body)
                                st.json(parsed_json)
                            except:
                                # If not JSON, show as text with length limit for performance
                                if len(response_body) > 1000:
                                    st.text_area("Response Body", response_body[:1000] + "...", height=200, disabled=True)
                                    st.caption(f"Showing first 1000 characters of {len(response_body)} total")
                                else:
                                    st.code(response_body)
                        else:
                            st.markdown("*No response body*")

                # Differences section
                if comparison["match_status"] == "MISMATCH":
                    st.markdown("<h4>Differences</h4>", unsafe_allow_html=True)

                    # Status code difference
                    if not comparison["status_match"]:
                        st.markdown(f"""
                        <div style="background-color: rgba(255, 0, 0, 0.1); padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                            <b>Status Code:</b> Version A: {comparison['request_a']['status']} | Version B: {comparison['request_b']['status']}
                        </div>
                        """, unsafe_allow_html=True)

                    # Response headers difference
                    if not comparison["response_headers_match"]:
                        st.markdown(f"""
                        <div style="background-color: rgba(255, 165, 0, 0.1); padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                            <b>Response Headers:</b> Headers do not match
                        </div>
                        """, unsafe_allow_html=True)

                    # Response body differences
                    if not comparison["response_body_match"] and comparison["mismatched_fields"]:
                        st.markdown("<b>Mismatched Fields:</b>", unsafe_allow_html=True)

                        for field in comparison["mismatched_fields"]:
                            st.markdown(f"""
                            <div style="background-color: rgba(255, 0, 0, 0.1); padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                                <b>Path:</b> {field["path"]}<br>
                                <b>Version A:</b> {field["value_a"]}<br>
                                <b>Version B:</b> {field["value_b"]}
                            </div>
                            """, unsafe_allow_html=True)
            else:
                # Handle missing requests
                if comparison["match_status"] == "MISSING_IN_A":
                    st.markdown("<h4>Request exists only in Version B</h4>", unsafe_allow_html=True)
                    st.markdown(f"**Method:** {comparison['request_b']['method']}")
                    st.markdown(f"**URL:** {comparison['request_b']['url']}")
                    st.markdown(f"**Status:** {comparison['request_b']['status']} {comparison['request_b']['statusText']}")
                else:
                    st.markdown("<h4>Request exists only in Version A</h4>", unsafe_allow_html=True)
                    st.markdown(f"**Method:** {comparison['request_a']['method']}")
                    st.markdown(f"**URL:** {comparison['request_a']['url']}")
                    st.markdown(f"**Status:** {comparison['request_a']['status']} {comparison['request_a']['statusText']}")

    # Export options
    st.markdown("<h3>Export Options</h3>", unsafe_allow_html=True)

    export_col1, export_col2 = st.columns(2)

    with export_col1:
        if st.button("Export Comparison Results (JSON)"):
            # Create a JSON file for download
            export_data = {
                "summary": summary,
                "results": filtered_results
            }

            # Convert to JSON string
            json_str = json.dumps(export_data, indent=2, default=str)

            # Create download button
            st.download_button(
                label="Download JSON",
                data=json_str,
                file_name="api_comparison_results.json",
                mime="application/json"
            )

    with export_col2:
        if st.button("Export Comparison Report (CSV)"):
            # Create a CSV file for download
            csv_data = []

            for comparison in filtered_results:
                row = {
                    "Method": comparison["method"],
                    "Path": comparison["path"],
                    "Status": comparison["match_status"],
                    "Status Code Match": comparison.get("status_match", "N/A"),
                    "Headers Match": comparison.get("response_headers_match", "N/A"),
                    "Body Match": comparison.get("response_body_match", "N/A"),
                    "Version A Status": comparison.get("request_a", {}).get("status", "N/A") if comparison.get("request_a") else "MISSING",
                    "Version B Status": comparison.get("request_b", {}).get("status", "N/A") if comparison.get("request_b") else "MISSING",
                }
                csv_data.append(row)

            # Convert to DataFrame and then to CSV
            csv_df = pd.DataFrame(csv_data)
            csv_str = csv_df.to_csv(index=False)

            # Create download button
            st.download_button(
                label="Download CSV",
                data=csv_str,
                file_name="api_comparison_results.csv",
                mime="text/csv"
            )

# Main function to run the app
def render_dashboard_tab():
    """Render the enhanced dashboard tab with comprehensive analytics and visualizations"""

    # Dashboard Header with Theme Toggle
    col1, col2, col3 = st.columns([3, 1, 1])
    with col1:
        st.markdown('<h1>📊 QA Analytics Dashboard</h1>', unsafe_allow_html=True)
    with col2:
        # Theme toggle
        theme_mode = st.selectbox("Theme", ["Dark", "Light"], key="theme_selector")
        if theme_mode == "Light":
            st.markdown("""
            <style>
            .stApp { background-color: #ffffff; color: #000000; }
            .metric-card { background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); color: #333; }
            </style>
            """, unsafe_allow_html=True)
    with col3:
        # Real-time monitoring toggle
        real_time_mode = st.checkbox("🔴 Real-time Mode", key="realtime_toggle")
        if real_time_mode:
            st.markdown('<small style="color: #ff4444;">● Live Monitoring</small>', unsafe_allow_html=True)

    # Check if data is available
    if st.session_state.log_df is None or st.session_state.log_df.empty:
        render_dashboard_welcome()
        return

    df = st.session_state.log_df

    # Advanced Filtering Section
    render_advanced_filters(df)

    # Apply filters to get filtered dataframe
    filtered_df = apply_dashboard_filters(df)

    # Key Performance Indicators
    render_enhanced_kpis(filtered_df)

    # Interactive Visualizations Section
    render_interactive_visualizations(filtered_df)

    # Advanced Analytics Section
    render_advanced_analytics(filtered_df)

    # Real-time Monitoring (if enabled)
    if real_time_mode:
        render_realtime_monitoring(filtered_df)

    # Export and Actions Section
    render_dashboard_actions(filtered_df)

def render_dashboard_welcome():
    """Render welcome screen when no data is available"""
    # Hero Section
    st.markdown("""
    <div class="qa-hero">
        <div class="qa-hero-content">
            <h1>⚡ Professional QA Analytics Platform</h1>
            <p>Transform your testing data into actionable insights with enterprise-grade analytics and visualization tools</p>
            <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap; margin-top: 2rem;">
                <button class="btn-hyper btn-primary">↗ Upload Log Files</button>
                <button class="btn-hyper btn-outline">≡ View Documentation</button>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Feature Cards Section
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">Powerful QA Testing Features</h2>
        <p class="section-subtitle">Everything you need to analyze, monitor, and optimize your testing workflows with professional-grade tools</p>
    </div>
    """, unsafe_allow_html=True)

    # Feature Cards Grid
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">▣</div>
            <h3 class="feature-title">Advanced Analytics</h3>
            <p class="feature-description">Deep insights into performance trends, anomaly detection, and predictive analytics for your testing data</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">◉</div>
            <h3 class="feature-title">Real-time Monitoring</h3>
            <p class="feature-description">Live monitoring capabilities with auto-refresh, real-time alerts, and instant performance feedback</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">⧉</div>
            <h3 class="feature-title">Interactive Filtering</h3>
            <p class="feature-description">Multi-dimensional data exploration with dynamic filtering, drill-down capabilities, and custom views</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">⟐</div>
            <h3 class="feature-title">Rich Visualizations</h3>
            <p class="feature-description">Professional charts, heatmaps, timelines, and distribution plots with interactive hover effects</p>
        </div>
        """, unsafe_allow_html=True)

    # Quick start guide
    with st.expander("🚀 Quick Start Guide", expanded=True):
        st.markdown("""
        ### Getting Started:
        1. **Upload Log File**: Use the sidebar to upload your Charles Proxy log file
        2. **Explore Data**: View automatic insights and key metrics
        3. **Interactive Analysis**: Use filters and drill-down capabilities
        4. **Generate Reports**: Export findings and create professional reports
        5. **Save Insights**: Store patterns and findings in the Knowledge Base

        ### Supported Formats:
        - Charles Proxy Session Files (.chlsj)
        - HAR Files (.har)
        - JSON Log Files (.json)
        """)

def render_advanced_filters(df):
    """Render advanced filtering controls"""
    with st.expander("🎛️ Advanced Filters & Controls", expanded=False):
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            # Time range filter
            if 'timestamp' in df.columns:
                try:
                    time_range = st.slider(
                        "Time Range (minutes)",
                        min_value=0,
                        max_value=int((df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 60) if df['timestamp'].dtype == 'datetime64[ns]' else 60,
                        value=(0, 60),
                        key="time_range_filter"
                    )
                except:
                    time_range = (0, 60)

            # Status code filter
            status_options = sorted(df['status'].unique())
            selected_status = st.multiselect(
                "Status Codes",
                options=status_options,
                default=status_options,
                key="status_filter_dash"
            )

        with col2:
            # HTTP method filter
            method_options = df['method'].unique() if 'method' in df.columns else []
            selected_methods = st.multiselect(
                "HTTP Methods",
                options=method_options,
                default=method_options,
                key="method_filter_dash"
            )

            # Host filter
            host_options = df['host'].unique() if 'host' in df.columns else []
            selected_hosts = st.multiselect(
                "Hosts",
                options=host_options[:10],  # Limit to first 10 for performance
                default=host_options[:5] if len(host_options) > 5 else host_options,
                key="host_filter_dash"
            )

        with col3:
            # Response time filter
            if 'time' in df.columns:
                time_min, time_max = int(df['time'].min()), int(df['time'].max())
                response_time_range = st.slider(
                    "Response Time (ms)",
                    min_value=time_min,
                    max_value=time_max,
                    value=(time_min, time_max),
                    key="response_time_filter"
                )

            # Content type filter
            if 'contentType' in df.columns:
                content_types = df['contentType'].unique()
                selected_content_types = st.multiselect(
                    "Content Types",
                    options=content_types[:10],
                    default=content_types[:3] if len(content_types) > 3 else content_types,
                    key="content_type_filter"
                )

        with col4:
            # URL pattern filter
            url_pattern = st.text_input(
                "URL Pattern (regex)",
                placeholder="e.g., /api/v1/.*",
                key="url_pattern_filter"
            )

            # Anomaly detection threshold
            anomaly_threshold = st.slider(
                "Anomaly Threshold",
                min_value=1.0,
                max_value=5.0,
                value=2.0,
                step=0.1,
                help="Standard deviations for anomaly detection",
                key="anomaly_threshold"
            )

        # Store filter values in session state
        st.session_state.dashboard_filters = {
            'selected_status': selected_status,
            'selected_methods': selected_methods,
            'selected_hosts': selected_hosts,
            'response_time_range': response_time_range if 'time' in df.columns else None,
            'selected_content_types': selected_content_types if 'contentType' in df.columns else [],
            'url_pattern': url_pattern,
            'anomaly_threshold': anomaly_threshold
        }

def apply_dashboard_filters(df):
    """Apply filters to the dataframe with safety checks"""
    if df is None or len(df) == 0:
        return df

    if not hasattr(st.session_state, 'dashboard_filters'):
        return df

    filters = st.session_state.dashboard_filters
    filtered_df = df.copy()

    try:
        # Apply status filter
        if filters.get('selected_status') and 'status' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['status'].isin(filters['selected_status'])]

        # Apply method filter
        if filters.get('selected_methods') and 'method' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['method'].isin(filters['selected_methods'])]

        # Apply host filter
        if filters.get('selected_hosts') and 'host' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['host'].isin(filters['selected_hosts'])]

        # Apply response time filter
        if filters.get('response_time_range') and 'time' in filtered_df.columns:
            time_min, time_max = filters['response_time_range']
            filtered_df = filtered_df[(filtered_df['time'] >= time_min) & (filtered_df['time'] <= time_max)]

        # Apply content type filter
        if filters.get('selected_content_types') and 'contentType' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['contentType'].isin(filters['selected_content_types'])]

        # Apply URL pattern filter
        if filters.get('url_pattern') and 'url' in filtered_df.columns:
            try:
                filtered_df = filtered_df[filtered_df['url'].str.contains(filters['url_pattern'], regex=True, na=False)]
            except:
                pass  # Invalid regex, skip filter

    except Exception as e:
        st.error(f"Error applying filters: {str(e)}")
        return df  # Return original DataFrame if filtering fails

    return filtered_df

def render_enhanced_kpis(df):
    """Render enhanced Key Performance Indicators with advanced metrics"""
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">▣ Performance Overview</h2>
        <p class="section-subtitle">Real-time insights into your testing performance and system health</p>
    </div>
    """, unsafe_allow_html=True)

    # Safety check for empty DataFrame
    if df is None or len(df) == 0:
        st.warning("No data available for analysis. Please upload a log file or adjust your filters.")
        return

    # Calculate advanced metrics with safety checks
    total_requests = len(df)
    success_rate = df["status"].between(200, 299).mean() * 100 if total_requests > 0 and 'status' in df.columns else 0
    avg_response_time = df["time"].mean() if 'time' in df.columns and total_requests > 0 else 0
    error_rate = (df["status"] >= 400).mean() * 100 if total_requests > 0 and 'status' in df.columns else 0
    unique_hosts = df["host"].nunique() if 'host' in df.columns and total_requests > 0 else 0

    # Safe throughput calculation
    throughput = 0
    if 'timestamp' in df.columns and total_requests > 1:
        try:
            time_diff = (df['timestamp'].max() - df['timestamp'].min()).total_seconds()
            if time_diff > 0:
                throughput = total_requests / time_diff * 60
        except:
            throughput = 0

    # Create enhanced metric cards with professional icons
    col1, col2, col3, col4, col5, col6 = st.columns(6)

    with col1:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon primary">▤</div>
            <div class="metric-value">{total_requests:,}</div>
            <div class="metric-label">Total Requests</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        icon_class = "success" if success_rate >= 95 else "warning" if success_rate >= 90 else "danger"
        icon_symbol = "✓" if success_rate >= 95 else "⚠" if success_rate >= 90 else "✗"
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon {icon_class}">{icon_symbol}</div>
            <div class="metric-value">{success_rate:.1f}%</div>
            <div class="metric-label">Success Rate</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        icon_class = "success" if avg_response_time <= 200 else "warning" if avg_response_time <= 1000 else "danger"
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon {icon_class}">⟲</div>
            <div class="metric-value">{avg_response_time:.0f}ms</div>
            <div class="metric-label">Avg Response Time</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        icon_class = "success" if error_rate <= 1 else "warning" if error_rate <= 5 else "danger"
        icon_symbol = "◉" if error_rate <= 1 else "◐" if error_rate <= 5 else "●"
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon {icon_class}">{icon_symbol}</div>
            <div class="metric-value">{error_rate:.1f}%</div>
            <div class="metric-label">Error Rate</div>
        </div>
        """, unsafe_allow_html=True)

    with col5:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon info">⬢</div>
            <div class="metric-value">{unique_hosts}</div>
            <div class="metric-label">Unique Hosts</div>
        </div>
        """, unsafe_allow_html=True)

    with col6:
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-icon primary">⟐</div>
            <div class="metric-value">{throughput:.1f}</div>
            <div class="metric-label">Req/Min</div>
        </div>
        """, unsafe_allow_html=True)

    # Performance indicators with modern status badges
    st.markdown("<br>", unsafe_allow_html=True)
    col1, col2, col3 = st.columns(3)

    with col1:
        # System Health Status
        if success_rate >= 95:
            status_class = "success"
            status_text = "Excellent"
            status_icon = "✓"
        elif success_rate >= 90:
            status_class = "warning"
            status_text = "Good"
            status_icon = "⚠"
        else:
            status_class = "danger"
            status_text = "Needs Attention"
            status_icon = "✗"

        st.markdown(f"""
        <div class="hyper-card" style="text-align: center; padding: 1.5rem;">
            <h4 style="color: var(--text-color); margin-bottom: 1rem;">System Health</h4>
            <div class="status-indicator {status_class}" style="font-size: 1.1rem;">
                {status_icon} {status_text}
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        # Performance Status
        if avg_response_time <= 200:
            perf_class = "success"
            perf_text = "Fast"
            perf_icon = "⟲"
        elif avg_response_time <= 1000:
            perf_class = "warning"
            perf_text = "Moderate"
            perf_icon = "◐"
        else:
            perf_class = "danger"
            perf_text = "Slow"
            perf_icon = "●"

        st.markdown(f"""
        <div class="hyper-card" style="text-align: center; padding: 1.5rem;">
            <h4 style="color: var(--text-color); margin-bottom: 1rem;">Performance</h4>
            <div class="status-indicator {perf_class}" style="font-size: 1.1rem;">
                {perf_icon} {perf_text}
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        # Error Rate Status
        if error_rate <= 1:
            error_class = "success"
            error_text = "Low"
            error_icon = "◉"
        elif error_rate <= 5:
            error_class = "warning"
            error_text = "Medium"
            error_icon = "◐"
        else:
            error_class = "danger"
            error_text = "High"
            error_icon = "●"

        st.markdown(f"""
        <div class="hyper-card" style="text-align: center; padding: 1.5rem;">
            <h4 style="color: var(--text-color); margin-bottom: 1rem;">Error Rate</h4>
            <div class="status-indicator {error_class}" style="font-size: 1.1rem;">
                {error_icon} {error_text}
            </div>
        </div>
        """, unsafe_allow_html=True)

def render_interactive_visualizations(df):
    """Render interactive visualizations with drill-down capabilities"""
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">⟐ Interactive Visualizations</h2>
        <p class="section-subtitle">Explore your data with professional charts, heatmaps, and interactive analytics</p>
    </div>
    """, unsafe_allow_html=True)

    # Visualization tabs
    viz_tabs = st.tabs(["▣ Overview", "⟲ Timeline", "▦ Heatmaps", "⟐ Trends", "◉ Distribution"])

    with viz_tabs[0]:  # Overview
        col1, col2 = st.columns(2)

        with col1:
            # Enhanced status code distribution with drill-down
            status_counts = df["status"].value_counts().reset_index()
            status_counts.columns = ["Status", "Count"]

            # Add status categories
            def get_status_category(status):
                if 200 <= status < 300:
                    return "2xx Success"
                elif 300 <= status < 400:
                    return "3xx Redirection"
                elif 400 <= status < 500:
                    return "4xx Client Error"
                elif 500 <= status < 600:
                    return "5xx Server Error"
                else:
                    return "Other"

            status_counts["Category"] = status_counts["Status"].apply(get_status_category)

            fig_status = px.pie(
                status_counts,
                values="Count",
                names="Status",
                color="Category",
                title="Status Code Distribution",
                hover_data=["Category"],
                color_discrete_map={
                    "2xx Success": "#4CAF50",
                    "3xx Redirection": "#2196F3",
                    "4xx Client Error": "#FF9800",
                    "5xx Server Error": "#F44336",
                    "Other": "#9E9E9E"
                }
            )
            fig_status.update_traces(textposition='inside', textinfo='percent+label')
            st.plotly_chart(fig_status, use_container_width=True)

        with col2:
            # Response time distribution with statistical overlay
            if 'time' in df.columns:
                fig_time = px.histogram(
                    df,
                    x="time",
                    title="Response Time Distribution",
                    nbins=50,
                    marginal="box"
                )

                # Add mean and median lines
                mean_time = df['time'].mean()
                median_time = df['time'].median()

                fig_time.add_vline(x=mean_time, line_dash="dash", line_color="red",
                                 annotation_text=f"Mean: {mean_time:.0f}ms")
                fig_time.add_vline(x=median_time, line_dash="dash", line_color="blue",
                                 annotation_text=f"Median: {median_time:.0f}ms")

                st.plotly_chart(fig_time, use_container_width=True)

    with viz_tabs[1]:  # Timeline
        if 'timestamp' in df.columns:
            # Interactive timeline with multiple metrics
            timeline_metric = st.selectbox(
                "Select Timeline Metric:",
                ["Response Time", "Status Codes", "Request Volume"],
                key="timeline_metric"
            )

            if timeline_metric == "Response Time":
                fig_timeline = px.scatter(
                    df,
                    x="timestamp",
                    y="time",
                    color="status",
                    size="time",
                    title="Response Time Timeline",
                    hover_data=["url", "method"] if 'url' in df.columns else None
                )
            elif timeline_metric == "Status Codes":
                # Group by time intervals
                df_timeline = df.set_index('timestamp').resample('1min')['status'].apply(list).reset_index()
                df_timeline['success_rate'] = df_timeline['status'].apply(
                    lambda x: sum(1 for s in x if 200 <= s < 300) / len(x) * 100 if x else 0
                )

                fig_timeline = px.line(
                    df_timeline,
                    x="timestamp",
                    y="success_rate",
                    title="Success Rate Over Time",
                    labels={"success_rate": "Success Rate (%)"}
                )
            else:  # Request Volume
                df_volume = df.set_index('timestamp').resample('1min').size().reset_index()
                df_volume.columns = ['timestamp', 'request_count']

                fig_timeline = px.bar(
                    df_volume,
                    x="timestamp",
                    y="request_count",
                    title="Request Volume Over Time"
                )

            st.plotly_chart(fig_timeline, use_container_width=True)
        else:
            st.info("Timestamp data not available for timeline visualization")

    with viz_tabs[2]:  # Heatmaps
        col1, col2 = st.columns(2)

        with col1:
            # Response time heatmap by hour and day
            if 'timestamp' in df.columns:
                df_heatmap = df.copy()
                df_heatmap['hour'] = df_heatmap['timestamp'].dt.hour
                df_heatmap['day'] = df_heatmap['timestamp'].dt.day_name()

                heatmap_data = df_heatmap.groupby(['day', 'hour'])['time'].mean().reset_index()
                heatmap_pivot = heatmap_data.pivot(index='day', columns='hour', values='time')

                fig_heatmap = px.imshow(
                    heatmap_pivot,
                    title="Response Time Heatmap (by Day & Hour)",
                    labels=dict(x="Hour", y="Day", color="Avg Response Time (ms)"),
                    aspect="auto"
                )
                st.plotly_chart(fig_heatmap, use_container_width=True)
            else:
                st.info("Timestamp data not available for heatmap")

        with col2:
            # Status code heatmap by host
            if 'host' in df.columns:
                host_status = df.groupby(['host', 'status']).size().reset_index(name='count')
                host_status_pivot = host_status.pivot(index='host', columns='status', values='count').fillna(0)

                fig_host_heatmap = px.imshow(
                    host_status_pivot,
                    title="Status Code Distribution by Host",
                    labels=dict(x="Status Code", y="Host", color="Request Count"),
                    aspect="auto"
                )
                st.plotly_chart(fig_host_heatmap, use_container_width=True)
            else:
                st.info("Host data not available for heatmap")

    with viz_tabs[3]:  # Trends
        if 'timestamp' in df.columns:
            # Performance trends over time
            trend_window = st.selectbox("Trend Window:", ["1min", "5min", "15min", "1H"], key="trend_window")

            df_trends = df.set_index('timestamp')

            # Calculate rolling statistics
            rolling_stats = df_trends.resample(trend_window).agg({
                'time': ['mean', 'std', 'min', 'max'],
                'status': lambda x: (x.between(200, 299)).mean() * 100
            }).reset_index()

            rolling_stats.columns = ['timestamp', 'avg_time', 'std_time', 'min_time', 'max_time', 'success_rate']

            # Create trend visualization
            fig_trends = go.Figure()

            # Add average response time
            fig_trends.add_trace(go.Scatter(
                x=rolling_stats['timestamp'],
                y=rolling_stats['avg_time'],
                mode='lines',
                name='Avg Response Time',
                line=dict(color='blue')
            ))

            # Add confidence band
            fig_trends.add_trace(go.Scatter(
                x=rolling_stats['timestamp'],
                y=rolling_stats['avg_time'] + rolling_stats['std_time'],
                mode='lines',
                line=dict(width=0),
                showlegend=False
            ))

            fig_trends.add_trace(go.Scatter(
                x=rolling_stats['timestamp'],
                y=rolling_stats['avg_time'] - rolling_stats['std_time'],
                mode='lines',
                line=dict(width=0),
                fill='tonexty',
                name='±1 Std Dev',
                fillcolor='rgba(0,100,80,0.2)'
            ))

            fig_trends.update_layout(title="Performance Trends with Confidence Band")
            st.plotly_chart(fig_trends, use_container_width=True)
        else:
            st.info("Timestamp data not available for trend analysis")

    with viz_tabs[4]:  # Distribution
        col1, col2 = st.columns(2)

        with col1:
            # Method distribution
            if 'method' in df.columns:
                method_counts = df['method'].value_counts()
                fig_methods = px.bar(
                    x=method_counts.index,
                    y=method_counts.values,
                    title="HTTP Method Distribution",
                    labels={'x': 'HTTP Method', 'y': 'Count'}
                )
                st.plotly_chart(fig_methods, use_container_width=True)

        with col2:
            # Content type distribution
            if 'contentType' in df.columns:
                content_counts = df['contentType'].value_counts().head(10)
                fig_content = px.bar(
                    x=content_counts.values,
                    y=content_counts.index,
                    orientation='h',
                    title="Top 10 Content Types",
                    labels={'x': 'Count', 'y': 'Content Type'}
                )
                st.plotly_chart(fig_content, use_container_width=True)

def render_advanced_analytics(df):
    """Render advanced analytics section with anomaly detection and insights"""
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">◈ Advanced Analytics</h2>
        <p class="section-subtitle">AI-powered insights, anomaly detection, and predictive analytics for your testing data</p>
    </div>
    """, unsafe_allow_html=True)

    analytics_tabs = st.tabs(["⚠ Anomaly Detection", "▤ Statistical Analysis", "◉ Predictive Insights", "⧉ Custom Widgets"])

    with analytics_tabs[0]:  # Anomaly Detection
        if 'time' in df.columns:
            # Detect response time anomalies
            threshold = st.session_state.dashboard_filters.get('anomaly_threshold', 2.0)

            mean_time = df['time'].mean()
            std_time = df['time'].std()

            anomalies = df[
                (df['time'] > mean_time + threshold * std_time) |
                (df['time'] < mean_time - threshold * std_time)
            ]

            col1, col2 = st.columns([2, 1])

            with col1:
                # Anomaly visualization
                fig_anomaly = px.scatter(
                    df,
                    x=range(len(df)),
                    y='time',
                    title=f"Response Time Anomalies (>{threshold}σ)",
                    labels={'x': 'Request Index', 'y': 'Response Time (ms)'}
                )

                # Highlight anomalies
                if not anomalies.empty:
                    fig_anomaly.add_scatter(
                        x=anomalies.index,
                        y=anomalies['time'],
                        mode='markers',
                        marker=dict(color='red', size=10),
                        name='Anomalies'
                    )

                # Add threshold lines
                fig_anomaly.add_hline(y=mean_time + threshold * std_time, line_dash="dash", line_color="red")
                fig_anomaly.add_hline(y=mean_time - threshold * std_time, line_dash="dash", line_color="red")

                st.plotly_chart(fig_anomaly, use_container_width=True)

            with col2:
                # Anomaly summary
                st.markdown("### 🚨 Anomaly Summary")
                st.metric("Total Anomalies", len(anomalies))
                st.metric("Anomaly Rate", f"{len(anomalies)/len(df)*100:.2f}%")

                if not anomalies.empty:
                    st.markdown("### Top Anomalous Requests")
                    for idx, row in anomalies.nlargest(5, 'time').iterrows():
                        st.markdown(f"**{row['time']:.0f}ms** - {row.get('url', 'N/A')[:50]}...")
        else:
            st.info("Response time data not available for anomaly detection")

    with analytics_tabs[1]:  # Statistical Analysis
        col1, col2 = st.columns(2)

        with col1:
            # Response time statistics
            if 'time' in df.columns:
                st.markdown("### 📊 Response Time Statistics")
                stats_df = pd.DataFrame({
                    'Metric': ['Mean', 'Median', 'Std Dev', 'Min', 'Max', '95th Percentile', '99th Percentile'],
                    'Value (ms)': [
                        df['time'].mean(),
                        df['time'].median(),
                        df['time'].std(),
                        df['time'].min(),
                        df['time'].max(),
                        df['time'].quantile(0.95),
                        df['time'].quantile(0.99)
                    ]
                })
                st.dataframe(stats_df, use_container_width=True)

        with col2:
            # Status code statistics
            st.markdown("### 📈 Status Code Analysis")
            status_stats = df['status'].value_counts().reset_index()
            status_stats.columns = ['Status Code', 'Count']
            status_stats['Percentage'] = (status_stats['Count'] / len(df) * 100).round(2)
            st.dataframe(status_stats, use_container_width=True)

def render_realtime_monitoring(df):
    """Render real-time monitoring section"""
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">◉ Real-time Monitoring</h2>
        <p class="section-subtitle">Live performance tracking with auto-refresh and instant alerts</p>
    </div>
    """, unsafe_allow_html=True)

    # Auto-refresh controls
    col1, col2, col3 = st.columns(3)
    with col1:
        refresh_interval = st.selectbox("Refresh Interval", ["5s", "10s", "30s", "1min"], key="refresh_interval")
    with col2:
        auto_refresh = st.checkbox("Auto Refresh", key="auto_refresh")
    with col3:
        if st.button("🔄 Refresh Now", key="manual_refresh"):
            st.experimental_rerun()

    # Real-time metrics
    if auto_refresh:
        st.markdown('<div style="color: #ff4444;">● Live monitoring active</div>', unsafe_allow_html=True)

        # Simulate real-time updates (in production, this would connect to live data)
        placeholder = st.empty()

        with placeholder.container():
            # Latest metrics
            latest_requests = df.tail(10)

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Last 10 Requests", len(latest_requests))
            with col2:
                recent_success_rate = latest_requests["status"].between(200, 299).mean() * 100
                st.metric("Recent Success Rate", f"{recent_success_rate:.1f}%")
            with col3:
                recent_avg_time = latest_requests["time"].mean() if 'time' in latest_requests.columns else 0
                st.metric("Recent Avg Time", f"{recent_avg_time:.0f}ms")

            # Live chart
            if 'timestamp' in df.columns:
                fig_live = px.line(
                    latest_requests,
                    x='timestamp',
                    y='time',
                    title="Live Response Time (Last 10 Requests)"
                )
                st.plotly_chart(fig_live, use_container_width=True)

def render_dashboard_actions(df):
    """Render dashboard actions and export functionality"""
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">⚙ Dashboard Actions</h2>
        <p class="section-subtitle">Export data, save views, and generate automated insights</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Export current view
        if st.button("📊 Export Dashboard", key="export_dashboard"):
            # Create dashboard export
            export_data = {
                'summary': {
                    'total_requests': len(df),
                    'success_rate': df["status"].between(200, 299).mean() * 100,
                    'avg_response_time': df["time"].mean() if 'time' in df.columns else 0,
                    'unique_hosts': df["host"].nunique() if 'host' in df.columns else 0
                },
                'filters_applied': st.session_state.get('dashboard_filters', {}),
                'export_timestamp': pd.Timestamp.now().isoformat()
            }

            st.download_button(
                label="📥 Download Dashboard Data",
                data=json.dumps(export_data, indent=2),
                file_name=f"dashboard_export_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json",
                key="download_dashboard"
            )

    with col2:
        # Save current view
        if st.button("💾 Save View", key="save_view"):
            view_name = st.text_input("View Name:", key="view_name_input")
            if view_name:
                # Save view configuration
                saved_view = {
                    'name': view_name,
                    'filters': st.session_state.get('dashboard_filters', {}),
                    'timestamp': pd.Timestamp.now().isoformat()
                }

                if 'saved_views' not in st.session_state:
                    st.session_state.saved_views = []

                st.session_state.saved_views.append(saved_view)
                st.success(f"View '{view_name}' saved!")

    with col3:
        # Load saved view
        if hasattr(st.session_state, 'saved_views') and st.session_state.saved_views:
            saved_view_names = [view['name'] for view in st.session_state.saved_views]
            selected_view = st.selectbox("Load Saved View:", [""] + saved_view_names, key="load_view")

            if selected_view and st.button("📂 Load View", key="load_view_btn"):
                for view in st.session_state.saved_views:
                    if view['name'] == selected_view:
                        st.session_state.dashboard_filters = view['filters']
                        st.success(f"Loaded view '{selected_view}'")
                        st.experimental_rerun()

    with col4:
        # Generate insights
        if st.button("🧠 Generate Insights", key="generate_insights"):
            insights = []

            # Performance insights
            if 'time' in df.columns:
                avg_time = df['time'].mean()
                if avg_time > 1000:
                    insights.append("⚠️ High average response time detected")
                elif avg_time < 200:
                    insights.append("✅ Excellent response time performance")

            # Error rate insights
            error_rate = (df["status"] >= 400).mean() * 100
            if error_rate > 5:
                insights.append("🔴 High error rate requires attention")
            elif error_rate < 1:
                insights.append("✅ Low error rate indicates stable system")

            # Success rate insights
            success_rate = df["status"].between(200, 299).mean() * 100
            if success_rate < 90:
                insights.append("⚠️ Success rate below recommended threshold")

            # Display insights
            if insights:
                st.markdown("### 💡 Automated Insights")
                for insight in insights:
                    st.markdown(f"- {insight}")
            else:
                st.info("No specific insights detected. System appears to be performing normally.")

def render_request_details_tab():
    """Render the request details tab"""
    st.markdown('<h2>Request Details</h2>', unsafe_allow_html=True)

    if st.session_state.log_df is not None and not st.session_state.log_df.empty:
        df = st.session_state.log_df

        # Display request details
        st.dataframe(df[["method", "url", "status", "time", "contentType"]], use_container_width=True)
    else:
        st.info("Upload a log file to see request details")

def render_ai_analysis_tab():
    """Render the AI analysis tab"""
    st.markdown('<h2>AI Analysis</h2>', unsafe_allow_html=True)

    if st.session_state.ai_analysis:
        for insight in st.session_state.ai_analysis:
            st.markdown(f"- {insight}")
    else:
        st.info("Run AI analysis to see insights")

def render_knowledge_base_tab():
    """Render the enhanced knowledge base tab with full functionality"""
    st.markdown('<h1>Knowledge Base</h1>', unsafe_allow_html=True)

    if kb is None:
        st.error("Knowledge Base not initialized. Please restart the dashboard.")
        return

    # Create sub-tabs for different KB sections
    kb_tabs = st.tabs(["Analysis Sessions", "Search", "Test Patterns", "References", "Management"])

    # Analysis Sessions Tab
    with kb_tabs[0]:
        st.markdown('<h2>Recent Analysis Sessions</h2>', unsafe_allow_html=True)

        sessions = kb.get_all_sessions()
        if sessions:
            # Display sessions in a more interactive way
            for i, session in enumerate(sessions[:10]):  # Show last 10 sessions
                with st.expander(f"📁 {session['file_name']} - {session['timestamp'][:19]}"):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.markdown(f"**Session ID:** `{session['id']}`")
                        st.markdown(f"**File:** {session['file_name']}")
                        st.markdown(f"**Total Entries:** {session.get('total_entries', 'N/A')}")
                        st.markdown(f"**Success Rate:** {session.get('status_success_rate', 'N/A'):.1f}%" if session.get('status_success_rate') else "**Success Rate:** N/A")

                    with col2:
                        st.markdown(f"**Timestamp:** {session['timestamp']}")
                        st.markdown(f"**Test Context:** {session.get('test_context', 'N/A')}")
                        st.markdown(f"**Tags:** {session.get('tags', 'N/A')}")

                        # Action buttons
                        if st.button(f"View Insights", key=f"insights_{i}"):
                            insights = kb.get_insights_by_session(session['id'])
                            if insights:
                                st.markdown("**Insights:**")
                                for insight in insights:
                                    severity_color = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}.get(insight.get('severity', 'LOW'), "🔵")
                                    st.markdown(f"{severity_color} **{insight['title']}**: {insight['description']}")
                            else:
                                st.info("No insights available for this session")
        else:
            st.info("No analysis sessions found. Upload and analyze some Charles Proxy logs to populate the knowledge base.")

    # Search Tab
    with kb_tabs[1]:
        st.markdown('<h2>🔍 Search Knowledge Base</h2>', unsafe_allow_html=True)

        search_query = st.text_input("Enter search terms:", placeholder="Search sessions, insights, patterns...", key="kb_search_input")

        if search_query:
            with st.spinner("Searching..."):
                results = kb.search(search_query)

                if any(results.values()):
                    # Display sessions results
                    if results.get('sessions'):
                        st.markdown("### 📊 Sessions")
                        for session in results['sessions']:
                            st.markdown(f"**{session['file_name']}** - {session['timestamp'][:19]}")
                            st.markdown(f"Context: {session.get('test_context', 'N/A')}")
                            st.markdown("---")

                    # Display insights results
                    if results.get('insights'):
                        st.markdown("### 💡 Insights")
                        for insight in results['insights']:
                            severity_color = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}.get(insight.get('severity', 'LOW'), "🔵")
                            st.markdown(f"{severity_color} **{insight['title']}**")
                            st.markdown(f"Description: {insight['description']}")
                            st.markdown(f"From: {insight.get('file_name', 'Unknown file')}")
                            st.markdown("---")

                    # Display patterns results
                    if results.get('patterns'):
                        st.markdown("### 📋 Test Patterns")
                        for pattern in results['patterns']:
                            st.markdown(f"**{pattern['name']}** ({pattern['pattern_type']})")
                            st.markdown(f"Description: {pattern['description']}")
                            if pattern.get('regex_pattern'):
                                st.code(pattern['regex_pattern'])
                            st.markdown("---")
                else:
                    st.warning("No results found for your search query.")

    # Test Patterns Tab
    with kb_tabs[2]:
        st.markdown('<h2>📋 Test Patterns</h2>', unsafe_allow_html=True)

        # Add new pattern section
        with st.expander("➕ Add New Test Pattern"):
            pattern_name = st.text_input("Pattern Name:", key="pattern_name_input")
            pattern_type = st.selectbox("Pattern Type:", ["URL", "Response", "Header", "Error", "Performance"], key="pattern_type_select")
            pattern_desc = st.text_area("Description:", key="pattern_desc_input")
            regex_pattern = st.text_input("Regex Pattern (optional):", key="pattern_regex_input")
            pattern_tags = st.text_input("Tags (comma-separated):", key="pattern_tags_input")

            if st.button("Save Pattern", key="save_pattern_btn"):
                if pattern_name and pattern_desc:
                    tags = [tag.strip() for tag in pattern_tags.split(",") if tag.strip()]
                    pattern_id = kb.save_test_pattern(
                        name=pattern_name,
                        pattern_type=pattern_type.lower(),
                        description=pattern_desc,
                        regex_pattern=regex_pattern if regex_pattern else None,
                        tags=tags
                    )
                    st.success(f"Pattern saved with ID: {pattern_id}")
                else:
                    st.error("Please provide at least a name and description.")

        # Display existing patterns
        patterns = kb.get_test_patterns()
        if patterns:
            st.markdown("### Existing Test Patterns")
            for pattern in patterns:
                with st.expander(f"📋 {pattern['name']} ({pattern['pattern_type']})"):
                    st.markdown(f"**Description:** {pattern['description']}")
                    if pattern.get('regex_pattern'):
                        st.markdown("**Regex Pattern:**")
                        st.code(pattern['regex_pattern'])
                    if pattern.get('tags'):
                        tags = json.loads(pattern['tags']) if isinstance(pattern['tags'], str) else pattern['tags']
                        st.markdown(f"**Tags:** {', '.join(tags)}")
                    st.markdown(f"**Created:** {pattern['created_at'][:19]}")
        else:
            st.info("No test patterns saved yet. Add some patterns to build your testing knowledge base.")

    # References Tab
    with kb_tabs[3]:
        st.markdown('<h2>📖 References</h2>', unsafe_allow_html=True)

        # Add new reference section
        with st.expander("➕ Add New Reference"):
            ref_title = st.text_input("Reference Title:", key="ref_title_input")
            ref_category = st.selectbox("Category:", ["API", "Testing", "Security", "Performance", "Documentation", "Workflow"], key="ref_category_select")
            ref_content = st.text_area("Content:", height=200, key="ref_content_input")
            ref_tags = st.text_input("Tags (comma-separated):", key="ref_tags_input")

            if st.button("Save Reference", key="save_reference_btn"):
                if ref_title and ref_content:
                    tags = [tag.strip() for tag in ref_tags.split(",") if tag.strip()]
                    ref_id = kb.save_reference(
                        title=ref_title,
                        content=ref_content,
                        category=ref_category.lower(),
                        tags=tags
                    )
                    st.success(f"Reference saved with ID: {ref_id}")
                else:
                    st.error("Please provide both title and content.")

        # Display existing references
        references = kb.get_references()
        if references:
            st.markdown("### Existing References")
            for ref in references:
                with st.expander(f"📖 {ref['title']} ({ref['category']})"):
                    st.markdown(ref['content'])
                    if ref.get('tags'):
                        tags = json.loads(ref['tags']) if isinstance(ref['tags'], str) else ref['tags']
                        st.markdown(f"**Tags:** {', '.join(tags)}")
                    st.markdown(f"**Created:** {ref['created_at'][:19]}")
        else:
            st.info("No references saved yet. Add some documentation and references to build your knowledge base.")

    # Management Tab
    with kb_tabs[4]:
        st.markdown('<h2>Knowledge Base Management</h2>', unsafe_allow_html=True)

        # Statistics
        sessions = kb.get_all_sessions()
        patterns = kb.get_test_patterns()
        references = kb.get_references()

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Analysis Sessions", len(sessions))
        with col2:
            st.metric("Test Patterns", len(patterns))
        with col3:
            st.metric("References", len(references))

        st.markdown("---")

        # Export/Import section
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 📤 Export Knowledge Base")
            if st.button("Export to JSON", key="export_kb_btn"):
                try:
                    export_path = kb.export_knowledge_base("exports")
                    st.success(f"Knowledge base exported to: {export_path}")

                    # Provide download link
                    with open(export_path, 'r') as f:
                        export_data = f.read()

                    st.download_button(
                        label="📥 Download Export File",
                        data=export_data,
                        file_name=os.path.basename(export_path),
                        mime="application/json",
                        key="download_export_btn"
                    )
                except Exception as e:
                    st.error(f"Export failed: {str(e)}")

        with col2:
            st.markdown("### 📥 Import Knowledge Base")
            import_file = st.file_uploader("Choose JSON file to import:", type=["json"], key="import_file_uploader")
            if import_file:
                try:
                    # Save uploaded file temporarily
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
                        tmp.write(import_file.getvalue())
                        tmp_path = tmp.name

                    if st.button("Import Knowledge Base", key="import_kb_btn"):
                        success = kb.import_knowledge_base(tmp_path)
                        if success:
                            st.success("Knowledge base imported successfully!")
                            st.experimental_rerun()
                        else:
                            st.error("Failed to import knowledge base.")

                    # Clean up temp file
                    os.unlink(tmp_path)
                except Exception as e:
                    st.error(f"Import failed: {str(e)}")

        st.markdown("---")

        # Quick actions
        st.markdown("### 🚀 Quick Actions")
        if st.button("🔄 Refresh Knowledge Base", key="refresh_kb_btn"):
            st.experimental_rerun()

        if st.button("📊 Generate KB Statistics", key="generate_stats_btn"):
            st.markdown("#### Knowledge Base Statistics")

            # Session statistics
            if sessions:
                total_entries = sum(s.get('total_entries', 0) for s in sessions if s.get('total_entries'))
                avg_success_rate = sum(s.get('status_success_rate', 0) for s in sessions if s.get('status_success_rate')) / len([s for s in sessions if s.get('status_success_rate')])

                st.markdown(f"- **Total Log Entries Analyzed:** {total_entries:,}")
                st.markdown(f"- **Average Success Rate:** {avg_success_rate:.1f}%")
                st.markdown(f"- **Most Recent Session:** {sessions[0]['timestamp'][:19] if sessions else 'N/A'}")

            # Pattern statistics
            if patterns:
                pattern_types = {}
                for pattern in patterns:
                    ptype = pattern.get('pattern_type', 'unknown')
                    pattern_types[ptype] = pattern_types.get(ptype, 0) + 1

                st.markdown("**Pattern Distribution:**")
                for ptype, count in pattern_types.items():
                    st.markdown(f"- {ptype.title()}: {count}")

            # Reference statistics
            if references:
                ref_categories = {}
                for ref in references:
                    category = ref.get('category', 'unknown')
                    ref_categories[category] = ref_categories.get(category, 0) + 1

                st.markdown("**Reference Categories:**")
                for category, count in ref_categories.items():
                    st.markdown(f"- {category.title()}: {count}")

def render_karate_test_generator_tab():
    """Render the AI-powered Karate Test Generator tab"""
    st.markdown("""
    <div class="section-header">
        <h1 class="section-title">🤖 AI-Powered Karate Test Generator</h1>
        <p class="section-subtitle">Automatically generate Karate framework test cases from your API logs and IntelliJ project patterns</p>
    </div>
    """, unsafe_allow_html=True)

    # Check if Karate generator is available
    if not KARATE_GENERATOR_AVAILABLE:
        st.error("❌ Karate Test Generator not available. Please ensure karate_test_generator.py is present.")
        return

    # Initialize Karate generator if not already done
    if st.session_state.karate_generator is None:
        claude_agent = ClaudeAgent() if CLAUDE_AVAILABLE else None
        st.session_state.karate_generator = KarateTestGenerator(claude_agent)

    generator = st.session_state.karate_generator

    # Configuration Section
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">⚙️ Configuration</h2>
        <p class="section-subtitle">Set up your IntelliJ project connection and preferences</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns([2, 1])

    with col1:
        # IntelliJ Project Path
        project_path = st.text_input(
            "📁 IntelliJ Project Path",
            value=st.session_state.intellij_project_path,
            help="Enter the full path to your IntelliJ project directory",
            placeholder="/path/to/your/intellij/project"
        )

        if project_path != st.session_state.intellij_project_path:
            st.session_state.intellij_project_path = project_path
            if generator.set_intellij_project_path(project_path):
                st.success("✅ Project path validated successfully!")
            else:
                st.error("❌ Invalid project path. Please check the directory exists.")

    with col2:
        # Test Directory
        test_directory = st.text_input(
            "📂 Test Directory",
            value="src/test/java",
            help="Relative path within project for test files"
        )

        # Scan existing tests button
        if st.button("🔍 Scan Existing Tests", help="Analyze existing Karate tests for patterns"):
            if generator.intellij_project_path:
                with st.spinner("Scanning existing Karate test files..."):
                    patterns = generator.scan_existing_karate_tests()
                    if patterns:
                        st.success(f"✅ Found patterns from {len(patterns.get('naming_conventions', []))} test files")

                        # Show patterns summary
                        with st.expander("📋 Discovered Patterns", expanded=False):
                            if patterns.get('naming_conventions'):
                                st.write("**Naming Conventions:**")
                                for name in patterns['naming_conventions'][:5]:
                                    st.write(f"- {name}")

                            if patterns.get('authentication_patterns'):
                                st.write("**Authentication Patterns:**")
                                for auth in patterns['authentication_patterns'][:3]:
                                    st.write(f"- {auth}")
                    else:
                        st.warning("⚠️ No Karate test files found or unable to scan project")
            else:
                st.error("❌ Please set a valid project path first")

    # API Analysis Section
    st.markdown("""
    <div class="section-header">
        <h2 class="section-title">📊 API Analysis & Test Opportunities</h2>
        <p class="section-subtitle">Analyze your captured API data to identify test case opportunities</p>
    </div>
    """, unsafe_allow_html=True)

    # Check if log data is available
    if st.session_state.log_df is None or len(st.session_state.log_df) == 0:
        st.info("📋 Please upload Charles Proxy log files in the Dashboard tab to analyze APIs for test generation")
        return

    df = st.session_state.log_df

    col1, col2 = st.columns([2, 1])

    with col1:
        if st.button("🔍 Analyze APIs for Test Opportunities", type="primary"):
            with st.spinner("Analyzing API data for test case opportunities..."):
                opportunities = generator.analyze_api_data_for_tests(df)
                st.session_state.test_opportunities = opportunities

                if opportunities:
                    st.success(f"✅ Found {len(opportunities)} test opportunities!")
                else:
                    st.warning("⚠️ No suitable test opportunities found in the current data")

    with col2:
        # Filter options
        if st.session_state.test_opportunities:
            min_priority = st.slider("Minimum Priority", 0, 20, 5, help="Filter opportunities by priority score")
            filtered_opportunities = [opp for opp in st.session_state.test_opportunities if opp['test_priority'] >= min_priority]
        else:
            filtered_opportunities = []

    # Display Test Opportunities
    if filtered_opportunities:
        st.markdown(f"**Found {len(filtered_opportunities)} Test Opportunities (Priority ≥ {min_priority})**")

        for i, opportunity in enumerate(filtered_opportunities[:10]):  # Show top 10
            with st.expander(f"🎯 {opportunity['endpoint']} (Priority: {opportunity['test_priority']})", expanded=False):
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Methods", len(opportunity['methods']))
                    st.write("**Methods:**", ", ".join(opportunity['methods']))

                with col2:
                    st.metric("Success Rate", f"{opportunity['success_rate']:.1f}%")
                    st.metric("Avg Response Time", f"{opportunity['avg_response_time']:.0f}ms")

                with col3:
                    st.metric("Sample Requests", opportunity['sample_requests'])
                    st.write("**Status Codes:**", ", ".join(map(str, opportunity['status_codes'])))

                # Test Generation Options
                st.markdown("**Test Generation Options:**")

                col1, col2 = st.columns(2)

                with col1:
                    test_name = st.text_input(f"Test Name",
                                            value=generator._generate_test_name(opportunity['endpoint']),
                                            key=f"test_name_{i}")

                    use_ai = st.checkbox("🤖 Use AI Enhancement",
                                       value=CLAUDE_AVAILABLE,
                                       key=f"use_ai_{i}",
                                       help="Use Claude AI for enhanced test generation")

                with col2:
                    custom_requirements = st.text_area(f"Custom Requirements",
                                                     placeholder="Add specific test requirements...",
                                                     key=f"requirements_{i}",
                                                     height=100)

                # Generate Test Case Button
                if st.button(f"🚀 Generate Test Case", key=f"generate_{i}"):
                    with st.spinner("Generating Karate test case..."):
                        try:
                            if use_ai and CLAUDE_AVAILABLE:
                                test_content = generator.generate_ai_enhanced_test_case(
                                    opportunity, custom_requirements
                                )
                            else:
                                test_content = generator.generate_karate_test_case(
                                    opportunity, test_name, custom_requirements
                                )

                            # Display generated test
                            st.markdown("**Generated Test Case:**")
                            st.code(test_content, language="gherkin")

                            # Save to project option
                            if generator.intellij_project_path:
                                if st.button(f"💾 Save to IntelliJ Project", key=f"save_{i}"):
                                    success, message = generator.save_test_case_to_project(
                                        test_content, test_name, test_directory
                                    )

                                    if success:
                                        st.success(f"✅ Test case saved to: {message}")
                                        st.balloons()
                                    else:
                                        st.error(f"❌ Failed to save: {message}")
                            else:
                                st.info("💡 Set IntelliJ project path to save directly to your project")

                                # Download option
                                st.download_button(
                                    label="📥 Download Test Case",
                                    data=test_content,
                                    file_name=f"{test_name}.feature",
                                    mime="text/plain",
                                    key=f"download_{i}"
                                )

                        except Exception as e:
                            st.error(f"❌ Error generating test case: {str(e)}")

    # Bulk Generation Section
    if filtered_opportunities:
        st.markdown("""
        <div class="section-header">
            <h2 class="section-title">⚡ Bulk Test Generation</h2>
            <p class="section-subtitle">Generate multiple test cases at once</p>
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3 = st.columns(3)

        with col1:
            bulk_count = st.number_input("Number of tests to generate", 1, min(len(filtered_opportunities), 10), 3)

        with col2:
            bulk_use_ai = st.checkbox("🤖 Use AI for all", value=CLAUDE_AVAILABLE, key="bulk_ai")

        with col3:
            if st.button("🚀 Generate All Tests", type="primary"):
                with st.spinner(f"Generating {bulk_count} test cases..."):
                    generated_tests = []

                    for i, opportunity in enumerate(filtered_opportunities[:bulk_count]):
                        try:
                            test_name = generator._generate_test_name(opportunity['endpoint'])

                            if bulk_use_ai and CLAUDE_AVAILABLE:
                                test_content = generator.generate_ai_enhanced_test_case(opportunity)
                            else:
                                test_content = generator.generate_karate_test_case(opportunity, test_name)

                            generated_tests.append({
                                'name': test_name,
                                'content': test_content,
                                'endpoint': opportunity['endpoint']
                            })

                        except Exception as e:
                            st.error(f"❌ Error generating test for {opportunity['endpoint']}: {str(e)}")

                    if generated_tests:
                        st.success(f"✅ Generated {len(generated_tests)} test cases!")

                        # Show summary
                        for test in generated_tests:
                            with st.expander(f"📄 {test['name']} - {test['endpoint']}"):
                                st.code(test['content'], language="gherkin")

                        # Bulk save option
                        if generator.intellij_project_path:
                            if st.button("💾 Save All to Project"):
                                saved_count = 0
                                for test in generated_tests:
                                    success, _ = generator.save_test_case_to_project(
                                        test['content'], test['name'], test_directory
                                    )
                                    if success:
                                        saved_count += 1

                                if saved_count == len(generated_tests):
                                    st.success(f"✅ All {saved_count} test cases saved successfully!")
                                    st.balloons()
                                else:
                                    st.warning(f"⚠️ Saved {saved_count} out of {len(generated_tests)} test cases")

    # Help Section
    with st.expander("❓ Help & Documentation", expanded=False):
        st.markdown("""
        ### 🤖 AI-Powered Karate Test Generator

        This tool automatically generates Karate framework test cases by analyzing your API logs and existing test patterns.

        **Features:**
        - 🔍 **Pattern Recognition**: Analyzes existing Karate tests to maintain consistency
        - 🤖 **AI Enhancement**: Uses Claude AI for intelligent test generation
        - 📊 **API Analysis**: Identifies test opportunities from captured API data
        - 💾 **Direct Integration**: Saves tests directly to your IntelliJ project
        - ⚡ **Bulk Generation**: Generate multiple test cases at once

        **How to Use:**
        1. Set your IntelliJ project path
        2. Scan existing tests to learn patterns
        3. Upload Charles Proxy logs in the Dashboard tab
        4. Analyze APIs for test opportunities
        5. Generate and save test cases

        **Requirements:**
        - IntelliJ project with Karate framework setup
        - Charles Proxy log files with API data
        - Optional: Claude AI API key for enhanced generation
        """)

    # Status Footer
    st.markdown("---")
    status_col1, status_col2, status_col3 = st.columns(3)

    with status_col1:
        project_status = "✅ Connected" if generator.intellij_project_path else "❌ Not Connected"
        st.markdown(f"**Project:** {project_status}")

    with status_col2:
        ai_status = "✅ Available" if CLAUDE_AVAILABLE else "⚠️ Local Only"
        st.markdown(f"**AI Enhancement:** {ai_status}")

    with status_col3:
        data_status = "✅ Loaded" if st.session_state.log_df is not None else "❌ No Data"
        st.markdown(f"**API Data:** {data_status}")

def render_qa_reports_tab():
    """Render the enhanced QA Reports tab with comprehensive reporting"""
    st.markdown('<h1>📊 Professional QA Reports</h1>', unsafe_allow_html=True)

    # Check if data is available
    if st.session_state.log_df is None or len(st.session_state.log_df) == 0:
        st.info("Please upload log files to generate QA reports")
        return

    # Report Configuration Section
    st.markdown('<h2>🔧 Report Configuration</h2>', unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        report_title = st.text_input("Report Title", value="QA Test Analysis Report", key="report_title")
        project_name = st.text_input("Project Name", value="API Testing Project", key="project_name")

    with col2:
        company_name = st.text_input("Company Name", value="Your Company", key="company_name")
        test_environment = st.selectbox("Test Environment", ["Production", "Staging", "Development", "QA"], key="test_environment")

    with col3:
        report_type = st.selectbox("Report Type", ["Full Report", "Executive Summary", "Security Focus", "Performance Focus"], key="report_type")
        include_charts = st.checkbox("Include Visual Charts", value=True, key="include_charts")

    # Advanced Options
    with st.expander("Advanced Report Options"):
        col1, col2 = st.columns(2)
        with col1:
            include_raw_data = st.checkbox("Include Raw Data", value=False)
            include_recommendations = st.checkbox("Include Recommendations", value=True)
        with col2:
            include_security_analysis = st.checkbox("Include Security Analysis", value=True)
            include_compliance_metrics = st.checkbox("Include Compliance Metrics", value=True)

    # Generate Report Button
    st.markdown('<h2>📋 Generate Reports</h2>', unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🔍 Preview Report", type="primary"):
            generate_report_preview(
                st.session_state.log_df, report_title, project_name, company_name,
                test_environment, report_type, include_charts, include_security_analysis
            )

    with col2:
        if st.button("📄 Generate PDF"):
            pdf_data = generate_pdf_report(
                st.session_state.log_df, report_title, project_name, company_name,
                test_environment, report_type, include_charts, include_security_analysis
            )
            if pdf_data:
                st.download_button(
                    label="📥 Download PDF Report",
                    data=pdf_data,
                    file_name=f"QA_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                    mime="application/pdf"
                )

    with col3:
        if st.button("📊 Generate Excel"):
            excel_data = generate_excel_report(
                st.session_state.log_df, report_title, project_name, company_name,
                test_environment, include_raw_data, include_security_analysis
            )
            if excel_data:
                st.download_button(
                    label="📥 Download Excel Report",
                    data=excel_data,
                    file_name=f"QA_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )

    with col4:
        if st.button("📈 Comparison Report"):
            if hasattr(st.session_state, 'log_df_b') and st.session_state.log_df_b is not None:
                generate_comparison_report()
            else:
                st.warning("Upload a second log file for comparison analysis")

    # Display existing report if available
    if hasattr(st.session_state, 'current_report') and st.session_state.current_report:
        st.markdown('<h2>📋 Current Report Preview</h2>', unsafe_allow_html=True)
        st.markdown(st.session_state.current_report, unsafe_allow_html=True)

# Professional Report Generation Functions

def calculate_qa_metrics(df):
    """Calculate comprehensive QA metrics"""
    total_requests = len(df)

    # Status code analysis
    status_codes = df['status'].value_counts()
    success_rate = len(df[df['status'].astype(str).str.startswith('2')]) / total_requests * 100
    error_rate = len(df[df['status'].astype(str).str.startswith(('4', '5'))]) / total_requests * 100

    # Response time analysis (if available)
    response_times = []
    if 'response_time' in df.columns:
        response_times = df['response_time'].dropna()
        avg_response_time = response_times.mean()
        p95_response_time = response_times.quantile(0.95)
    else:
        avg_response_time = 0
        p95_response_time = 0

    # API endpoint analysis
    unique_endpoints = df['path'].nunique()
    most_tested_endpoint = df['path'].value_counts().index[0] if len(df) > 0 else "N/A"

    # Method distribution
    method_distribution = df['method'].value_counts()

    # Domain analysis
    domain_distribution = df['host'].value_counts()

    return {
        'total_requests': total_requests,
        'success_rate': success_rate,
        'error_rate': error_rate,
        'status_codes': status_codes,
        'avg_response_time': avg_response_time,
        'p95_response_time': p95_response_time,
        'unique_endpoints': unique_endpoints,
        'most_tested_endpoint': most_tested_endpoint,
        'method_distribution': method_distribution,
        'domain_distribution': domain_distribution,
        'response_times': response_times
    }

def generate_executive_summary(metrics, security_metrics=None):
    """Generate executive summary section"""
    summary = f"""
    ## 📋 Executive Summary

    ### Key Findings
    - **Total API Requests Tested**: {metrics['total_requests']:,}
    - **Success Rate**: {metrics['success_rate']:.1f}%
    - **Error Rate**: {metrics['error_rate']:.1f}%
    - **Unique Endpoints**: {metrics['unique_endpoints']}
    - **Average Response Time**: {metrics['avg_response_time']:.2f}ms

    ### Quality Assessment
    """

    if metrics['success_rate'] >= 95:
        summary += "✅ **EXCELLENT** - High success rate indicates robust API performance\n"
    elif metrics['success_rate'] >= 90:
        summary += "🟡 **GOOD** - Acceptable success rate with room for improvement\n"
    else:
        summary += "🔴 **NEEDS ATTENTION** - Low success rate requires immediate investigation\n"

    if security_metrics:
        high_risk = security_metrics.get('high_risk', 0)
        if high_risk == 0:
            summary += "🔒 **SECURE** - No high-risk security vulnerabilities detected\n"
        else:
            summary += f"⚠️ **SECURITY RISK** - {high_risk} high-risk vulnerabilities found\n"

    return summary

def generate_detailed_analysis(df, metrics):
    """Generate detailed analysis section"""
    analysis = f"""
    ## 📊 Detailed Analysis

    ### API Endpoint Coverage
    - **Total Endpoints Tested**: {metrics['unique_endpoints']}
    - **Most Frequently Tested**: {metrics['most_tested_endpoint']}
    - **Request Distribution by Method**:
    """

    for method, count in metrics['method_distribution'].items():
        percentage = (count / metrics['total_requests']) * 100
        analysis += f"  - {method}: {count} ({percentage:.1f}%)\n"

    analysis += f"""

    ### Response Status Analysis
    """

    for status, count in metrics['status_codes'].items():
        percentage = (count / metrics['total_requests']) * 100
        status_emoji = "✅" if str(status).startswith('2') else "⚠️" if str(status).startswith('4') else "🔴"
        analysis += f"  - {status_emoji} {status}: {count} ({percentage:.1f}%)\n"

    if len(metrics['response_times']) > 0:
        analysis += f"""

        ### Performance Metrics
        - **Average Response Time**: {metrics['avg_response_time']:.2f}ms
        - **95th Percentile**: {metrics['p95_response_time']:.2f}ms
        - **Fastest Response**: {metrics['response_times'].min():.2f}ms
        - **Slowest Response**: {metrics['response_times'].max():.2f}ms
        """

    return analysis

def generate_security_assessment(security_results=None):
    """Generate security assessment section"""
    if not security_results:
        return """
        ## 🔒 Security Assessment

        Security analysis not performed. Run security testing to include vulnerability assessment.
        """

    high_risk = sum(1 for result in security_results if result.get('risk_level') == 'HIGH')
    medium_risk = sum(1 for result in security_results if result.get('risk_level') == 'MEDIUM')
    low_risk = sum(1 for result in security_results if result.get('risk_level') == 'LOW')

    assessment = f"""
    ## 🔒 Security Assessment

    ### Vulnerability Summary
    - 🔴 **High Risk**: {high_risk} issues
    - 🟡 **Medium Risk**: {medium_risk} issues
    - 🟢 **Low Risk**: {low_risk} issues

    ### Security Recommendations
    """

    if high_risk > 0:
        assessment += """
        ⚠️ **IMMEDIATE ACTION REQUIRED**
        - Review and fix all high-risk vulnerabilities
        - Implement proper input validation
        - Add missing security headers
        - Remove sensitive data from responses
        """
    elif medium_risk > 0:
        assessment += """
        📋 **RECOMMENDED ACTIONS**
        - Address medium-risk security issues
        - Implement security best practices
        - Review CORS configuration
        - Add comprehensive security headers
        """
    else:
        assessment += """
        ✅ **GOOD SECURITY POSTURE**
        - No critical vulnerabilities detected
        - Continue monitoring for new threats
        - Regular security assessments recommended
        """

    return assessment

def generate_recommendations(metrics, security_results=None):
    """Generate actionable recommendations"""
    recommendations = """
    ## 💡 Recommendations & Next Steps

    ### Performance Optimization
    """

    if metrics['avg_response_time'] > 1000:
        recommendations += "- 🚀 **Optimize Response Times**: Average response time exceeds 1 second\n"

    if metrics['error_rate'] > 5:
        recommendations += "- 🔧 **Reduce Error Rate**: Error rate above acceptable threshold\n"

    if metrics['success_rate'] < 95:
        recommendations += "- ✅ **Improve Success Rate**: Target 95%+ success rate for production APIs\n"

    recommendations += """

    ### Testing Coverage
    - 📊 **Expand Test Coverage**: Include edge cases and error scenarios
    - 🔄 **Automated Testing**: Implement continuous API testing
    - 📈 **Performance Monitoring**: Set up real-time performance alerts

    ### Quality Gates
    - ✅ Success Rate: Target ≥ 95%
    - ⚡ Response Time: Target ≤ 500ms average
    - 🔒 Security: Zero high-risk vulnerabilities
    - 📊 Coverage: Test all critical API endpoints
    """

    return recommendations

def generate_report_preview(df, title, project, company, environment, report_type, include_charts, include_security):
    """Generate and display report preview"""
    with st.spinner("Generating report preview..."):
        # Calculate metrics
        metrics = calculate_qa_metrics(df)

        # Get security results if available
        security_results = getattr(st.session_state, 'security_results', None) if include_security else None
        security_metrics = None
        if security_results:
            security_metrics = {
                'high_risk': sum(1 for r in security_results if r.get('risk_level') == 'HIGH'),
                'medium_risk': sum(1 for r in security_results if r.get('risk_level') == 'MEDIUM'),
                'low_risk': sum(1 for r in security_results if r.get('risk_level') == 'LOW')
            }

        # Generate report content
        report_content = f"""
        # {title}

        **Project**: {project}
        **Company**: {company}
        **Environment**: {environment}
        **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        **Report Type**: {report_type}

        ---
        """

        # Add sections based on report type
        if report_type in ["Full Report", "Executive Summary"]:
            report_content += generate_executive_summary(metrics, security_metrics)

        if report_type in ["Full Report", "Performance Focus"]:
            report_content += generate_detailed_analysis(df, metrics)

        if report_type in ["Full Report", "Security Focus"] and include_security:
            report_content += generate_security_assessment(security_results)

        if report_type == "Full Report":
            report_content += generate_recommendations(metrics, security_results)

        # Store in session state
        st.session_state.current_report = report_content

        # Display charts if requested
        if include_charts:
            st.markdown("### 📊 Visual Analytics")

            col1, col2 = st.columns(2)

            with col1:
                # Status code distribution
                fig_status = px.pie(
                    values=metrics['status_codes'].values,
                    names=metrics['status_codes'].index,
                    title="Response Status Distribution"
                )
                st.plotly_chart(fig_status, use_container_width=True)

            with col2:
                # Method distribution
                fig_method = px.bar(
                    x=metrics['method_distribution'].index,
                    y=metrics['method_distribution'].values,
                    title="HTTP Method Distribution"
                )
                st.plotly_chart(fig_method, use_container_width=True)

            if len(metrics['response_times']) > 0:
                # Response time histogram
                fig_response = px.histogram(
                    x=metrics['response_times'],
                    title="Response Time Distribution",
                    nbins=30
                )
                st.plotly_chart(fig_response, use_container_width=True)

        st.success("Report preview generated successfully!")

def generate_pdf_report(df, title, project, company, environment, report_type, include_charts, include_security):
    """Generate PDF report"""
    try:
        # Calculate metrics
        metrics = calculate_qa_metrics(df)
        security_results = getattr(st.session_state, 'security_results', None) if include_security else None

        # Create PDF buffer
        buffer = BytesIO()

        # Create matplotlib figure for PDF
        if not MATPLOTLIB_AVAILABLE:
            st.error("Matplotlib not available for PDF generation. Please install: pip install matplotlib")
            return None

        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'{title}\n{company} - {project}', fontsize=16, fontweight='bold')

        # Status code pie chart
        axes[0, 0].pie(metrics['status_codes'].values, labels=metrics['status_codes'].index, autopct='%1.1f%%')
        axes[0, 0].set_title('Response Status Distribution')

        # Method bar chart
        axes[0, 1].bar(metrics['method_distribution'].index, metrics['method_distribution'].values)
        axes[0, 1].set_title('HTTP Method Distribution')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # Response time histogram (if available)
        if len(metrics['response_times']) > 0:
            axes[1, 0].hist(metrics['response_times'], bins=30, alpha=0.7)
            axes[1, 0].set_title('Response Time Distribution')
            axes[1, 0].set_xlabel('Response Time (ms)')
        else:
            axes[1, 0].text(0.5, 0.5, 'No response time data', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Response Time Distribution')

        # Success/Error rate
        success_error = ['Success', 'Error']
        success_error_values = [metrics['success_rate'], metrics['error_rate']]
        axes[1, 1].bar(success_error, success_error_values, color=['green', 'red'])
        axes[1, 1].set_title('Success vs Error Rate (%)')
        axes[1, 1].set_ylabel('Percentage')

        plt.tight_layout()

        # Save to PDF
        with PdfPages(buffer) as pdf:
            pdf.savefig(fig, bbox_inches='tight')

            # Add metadata
            d = pdf.infodict()
            d['Title'] = title
            d['Author'] = company
            d['Subject'] = f'QA Report for {project}'
            d['Creator'] = 'QA Dashboard'
            d['Producer'] = 'QA Dashboard'

        plt.close(fig)

        buffer.seek(0)
        return buffer.getvalue()

    except Exception as e:
        st.error(f"Error generating PDF: {str(e)}")
        return None

def generate_excel_report(df, title, project, company, environment, include_raw_data, include_security):
    """Generate Excel report with multiple sheets"""
    try:
        buffer = BytesIO()

        if not XLSXWRITER_AVAILABLE:
            st.error("XlsxWriter not available for Excel export. Please install: pip install xlsxwriter")
            return None

        with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
            workbook = writer.book

            # Define formats
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })

            # Summary sheet
            metrics = calculate_qa_metrics(df)
            summary_data = {
                'Metric': [
                    'Total Requests',
                    'Success Rate (%)',
                    'Error Rate (%)',
                    'Unique Endpoints',
                    'Average Response Time (ms)',
                    '95th Percentile Response Time (ms)'
                ],
                'Value': [
                    metrics['total_requests'],
                    f"{metrics['success_rate']:.1f}",
                    f"{metrics['error_rate']:.1f}",
                    metrics['unique_endpoints'],
                    f"{metrics['avg_response_time']:.2f}",
                    f"{metrics['p95_response_time']:.2f}"
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # Format summary sheet
            worksheet = writer.sheets['Summary']
            worksheet.set_column('A:A', 30)
            worksheet.set_column('B:B', 20)

            # Status codes sheet
            status_df = pd.DataFrame({
                'Status Code': metrics['status_codes'].index,
                'Count': metrics['status_codes'].values,
                'Percentage': [f"{(count/metrics['total_requests']*100):.1f}%" for count in metrics['status_codes'].values]
            })
            status_df.to_excel(writer, sheet_name='Status Codes', index=False)

            # Methods sheet
            method_df = pd.DataFrame({
                'HTTP Method': metrics['method_distribution'].index,
                'Count': metrics['method_distribution'].values,
                'Percentage': [f"{(count/metrics['total_requests']*100):.1f}%" for count in metrics['method_distribution'].values]
            })
            method_df.to_excel(writer, sheet_name='HTTP Methods', index=False)

            # Raw data sheet (if requested)
            if include_raw_data:
                # Select relevant columns for export
                export_columns = ['method', 'url', 'status', 'host', 'path']
                if 'response_time' in df.columns:
                    export_columns.append('response_time')

                raw_df = df[export_columns].copy()
                raw_df.to_excel(writer, sheet_name='Raw Data', index=False)

            # Security sheet (if available)
            if include_security and hasattr(st.session_state, 'security_results'):
                security_results = st.session_state.security_results
                security_data = []

                for i, result in enumerate(security_results):
                    issues = result.get('issues', [])
                    for issue in issues:
                        security_data.append({
                            'Request Index': i,
                            'URL': df.iloc[i]['url'] if i < len(df) else '',
                            'Risk Level': result.get('risk_level', 'LOW'),
                            'Issue Type': issue.get('type', ''),
                            'Severity': issue.get('severity', ''),
                            'Description': issue.get('description', ''),
                            'Recommendation': issue.get('recommendation', '')
                        })

                if security_data:
                    security_df = pd.DataFrame(security_data)
                    security_df.to_excel(writer, sheet_name='Security Issues', index=False)

        buffer.seek(0)
        return buffer.getvalue()

    except Exception as e:
        st.error(f"Error generating Excel report: {str(e)}")
        return None

def generate_comparison_report():
    """Generate comparison report between two datasets"""
    if not hasattr(st.session_state, 'log_df_b') or st.session_state.log_df_b is None:
        st.error("No second dataset available for comparison")
        return

    df_a = st.session_state.log_df
    df_b = st.session_state.log_df_b

    metrics_a = calculate_qa_metrics(df_a)
    metrics_b = calculate_qa_metrics(df_b)

    comparison_report = f"""
    # 📈 Comparison Analysis Report

    **Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

    ## Summary Comparison

    | Metric | Version A | Version B | Change |
    |--------|-----------|-----------|---------|
    | Total Requests | {metrics_a['total_requests']:,} | {metrics_b['total_requests']:,} | {metrics_b['total_requests'] - metrics_a['total_requests']:+,} |
    | Success Rate | {metrics_a['success_rate']:.1f}% | {metrics_b['success_rate']:.1f}% | {metrics_b['success_rate'] - metrics_a['success_rate']:+.1f}% |
    | Error Rate | {metrics_a['error_rate']:.1f}% | {metrics_b['error_rate']:.1f}% | {metrics_b['error_rate'] - metrics_a['error_rate']:+.1f}% |
    | Avg Response Time | {metrics_a['avg_response_time']:.2f}ms | {metrics_b['avg_response_time']:.2f}ms | {metrics_b['avg_response_time'] - metrics_a['avg_response_time']:+.2f}ms |

    ## Analysis
    """

    if metrics_b['success_rate'] > metrics_a['success_rate']:
        comparison_report += "✅ **Improvement**: Success rate increased\n"
    elif metrics_b['success_rate'] < metrics_a['success_rate']:
        comparison_report += "⚠️ **Regression**: Success rate decreased\n"
    else:
        comparison_report += "➡️ **Stable**: Success rate unchanged\n"

    if metrics_b['avg_response_time'] < metrics_a['avg_response_time']:
        comparison_report += "🚀 **Performance Improvement**: Response time decreased\n"
    elif metrics_b['avg_response_time'] > metrics_a['avg_response_time']:
        comparison_report += "🐌 **Performance Regression**: Response time increased\n"
    else:
        comparison_report += "➡️ **Stable Performance**: Response time unchanged\n"

    st.session_state.current_report = comparison_report
    st.success("Comparison report generated!")

@st.cache_data(ttl=600)  # Cache for 10 minutes
def perform_bulk_security_analysis(df_data):
    """Perform security analysis on all requests (cached)"""
    results = []
    for _, row in df_data.iterrows():
        security_analysis = perform_security_analysis(
            row.get('response_body', ''),
            row.get('response_headers', []),
            row.get('url', ''),
            row.get('method', 'GET')
        )
        results.append(security_analysis)
    return results

def render_security_testing_tab():
    """Render the Security Testing tab with vulnerability analysis"""
    st.markdown('<h2>🔒 Security Testing & Vulnerability Analysis</h2>', unsafe_allow_html=True)

    # Check if log data is available
    if st.session_state.log_df is None or len(st.session_state.log_df) == 0:
        st.info("Please upload a log file to perform security analysis")
        return

    df = st.session_state.log_df

    # Performance optimization: Only analyze when requested
    if st.button("🚀 Start Security Analysis", type="primary"):
        with st.spinner("Performing security analysis..."):
            # Perform bulk security analysis (cached)
            security_results = perform_bulk_security_analysis(df)

            # Store results in session state
            st.session_state.security_results = security_results
            st.success("Security analysis completed!")

    # Check if security analysis has been performed
    if not hasattr(st.session_state, 'security_results'):
        st.info("Click 'Start Security Analysis' to begin vulnerability scanning")
        return

    security_results = st.session_state.security_results

    # Security Overview
    st.markdown('<h3>Security Overview</h3>', unsafe_allow_html=True)

    # Calculate security metrics from cached results
    total_requests = len(security_results)
    high_risk_requests = sum(1 for result in security_results if result.get('risk_level') == 'HIGH')
    medium_risk_requests = sum(1 for result in security_results if result.get('risk_level') == 'MEDIUM')
    low_risk_requests = sum(1 for result in security_results if result.get('risk_level') == 'LOW')

    # Security metrics cards
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Requests", total_requests)

    with col2:
        st.metric("🔴 High Risk", high_risk_requests, delta=f"{(high_risk_requests/total_requests*100):.1f}%")

    with col3:
        st.metric("🟡 Medium Risk", medium_risk_requests, delta=f"{(medium_risk_requests/total_requests*100):.1f}%")

    with col4:
        st.metric("🟢 Low Risk", low_risk_requests, delta=f"{(low_risk_requests/total_requests*100):.1f}%")

    # Security Risk Distribution Chart
    st.markdown('<h3>Security Risk Distribution</h3>', unsafe_allow_html=True)

    risk_data = {
        'Risk Level': ['High', 'Medium', 'Low'],
        'Count': [high_risk_requests, medium_risk_requests, low_risk_requests],
        'Color': ['#ff4444', '#ffaa00', '#44ff44']
    }

    fig = px.pie(
        values=risk_data['Count'],
        names=risk_data['Risk Level'],
        title="Security Risk Distribution",
        color_discrete_sequence=risk_data['Color']
    )
    st.plotly_chart(fig, use_container_width=True)

    # Security Issues Summary
    st.markdown('<h3>Security Issues Summary</h3>', unsafe_allow_html=True)

    # Collect all security issues from cached results
    all_issues = []
    for i, security_analysis in enumerate(security_results):
        issues = security_analysis.get('issues', [])
        for issue in issues:
            issue_data = issue.copy()
            issue_data['url'] = df.iloc[i].get('url', '') if i < len(df) else ''
            issue_data['method'] = df.iloc[i].get('method', '') if i < len(df) else ''
            all_issues.append(issue_data)

    if all_issues:
        # Group issues by type
        issue_types = {}
        for issue in all_issues:
            issue_type = issue.get('type', 'Unknown')
            if issue_type not in issue_types:
                issue_types[issue_type] = {'count': 0, 'severity': [], 'urls': []}
            issue_types[issue_type]['count'] += 1
            issue_types[issue_type]['severity'].append(issue.get('severity', 'LOW'))
            issue_types[issue_type]['urls'].append(issue.get('url', ''))

        # Display issue types
        for issue_type, data in issue_types.items():
            severity_counts = {s: data['severity'].count(s) for s in ['HIGH', 'MEDIUM', 'LOW']}
            max_severity = 'HIGH' if severity_counts['HIGH'] > 0 else 'MEDIUM' if severity_counts['MEDIUM'] > 0 else 'LOW'

            severity_color = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[max_severity]

            with st.expander(f"{severity_color} {issue_type} ({data['count']} issues)"):
                st.markdown(f"**Severity Distribution:**")
                st.markdown(f"- High: {severity_counts['HIGH']}")
                st.markdown(f"- Medium: {severity_counts['MEDIUM']}")
                st.markdown(f"- Low: {severity_counts['LOW']}")

                st.markdown(f"**Affected URLs:**")
                unique_urls = list(set(data['urls']))[:10]  # Show first 10 unique URLs
                for url in unique_urls:
                    st.markdown(f"- {url}")
                if len(unique_urls) < len(set(data['urls'])):
                    st.markdown(f"... and {len(set(data['urls'])) - len(unique_urls)} more")

    # Detailed Security Analysis
    st.markdown('<h3>Detailed Security Analysis</h3>', unsafe_allow_html=True)

    # Filter options
    col1, col2, col3 = st.columns(3)

    with col1:
        risk_filter = st.selectbox("Filter by Risk Level:", ["All", "High", "Medium", "Low"])

    with col2:
        issue_type_filter = st.selectbox("Filter by Issue Type:", ["All"] + list(issue_types.keys()) if all_issues else ["All"])

    with col3:
        show_details = st.checkbox("Show Detailed Analysis", value=False)

    # Apply filters using cached results
    filtered_indices = []
    for i, security_analysis in enumerate(security_results):
        # Ensure we don't go out of bounds
        if i >= len(df):
            continue

        include = True

        if risk_filter != "All" and security_analysis.get('risk_level') != risk_filter.upper():
            include = False

        if issue_type_filter != "All":
            issues = security_analysis.get('issues', [])
            if not any(issue.get('type') == issue_type_filter for issue in issues):
                include = False

        if include:
            filtered_indices.append(i)

    # Safe filtering with bounds checking
    if filtered_indices:
        # Ensure all indices are within DataFrame bounds
        valid_indices = [i for i in filtered_indices if i < len(df)]
        filtered_df = df.iloc[valid_indices] if valid_indices else pd.DataFrame()
        filtered_security_results = [security_results[i] for i in valid_indices if i < len(security_results)]
    else:
        filtered_df = pd.DataFrame()
        filtered_security_results = []

    # Display filtered results
    st.markdown(f"**Showing {len(filtered_df)} requests**")

    # Limit display for performance
    max_display = 50
    display_count = min(len(filtered_df), max_display)

    if len(filtered_df) > max_display:
        st.warning(f"Showing first {max_display} of {len(filtered_df)} results for performance. Use filters to narrow down.")

    for idx in range(display_count):
        row = filtered_df.iloc[idx]
        security_analysis = filtered_security_results[idx]
        risk_level = security_analysis.get('risk_level', 'LOW')
        issues = security_analysis.get('issues', [])

        risk_color = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[risk_level]

        with st.expander(f"{risk_color} {row.get('method', 'GET')} {row.get('path', '/')} - {risk_level} Risk ({len(issues)} issues)"):
            st.markdown(f"**URL:** {row.get('url', '')}")
            st.markdown(f"**Status:** {row.get('status', 'Unknown')}")
            st.markdown(f"**Risk Level:** {risk_level}")
            st.markdown(f"**Total Issues:** {len(issues)}")

            if issues:
                st.markdown("**Security Issues:**")
                for i, issue in enumerate(issues):
                    severity_icon = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[issue.get('severity', 'LOW')]
                    st.markdown(f"{i+1}. {severity_icon} **{issue.get('type', 'Unknown')}** ({issue.get('severity', 'LOW')})")
                    st.markdown(f"   - {issue.get('description', 'No description')}")
                    st.markdown(f"   - *Recommendation:* {issue.get('recommendation', 'No recommendation')}")

            if show_details:
                st.markdown("**Response Headers:**")
                headers = row.get('response_headers', [])
                if headers:
                    for header in headers[:5]:  # Show first 5 headers
                        if isinstance(header, dict):
                            st.markdown(f"- {header.get('name', '')}: {header.get('value', '')}")
                else:
                    st.markdown("No headers available")

                if st.checkbox(f"Show Response Body", key=f"security_body_{idx}"):
                    response_body = row.get('response_body', '')
                    if response_body:
                        if len(response_body) > 500:
                            st.text_area("Response Body (truncated)", response_body[:500] + "...", height=150, disabled=True)
                        else:
                            st.code(response_body)
                    else:
                        st.markdown("No response body")

    # Security Recommendations
    st.markdown('<h3>🛡️ Security Recommendations</h3>', unsafe_allow_html=True)

    if high_risk_requests > 0:
        st.error(f"**Critical:** {high_risk_requests} high-risk requests found. Immediate attention required!")
        st.markdown("""
        **Immediate Actions:**
        1. Review all high-risk security issues
        2. Implement proper input validation
        3. Add missing security headers
        4. Remove sensitive data from responses
        """)

    if medium_risk_requests > 0:
        st.warning(f"**Warning:** {medium_risk_requests} medium-risk requests found.")
        st.markdown("""
        **Recommended Actions:**
        1. Review medium-risk security issues
        2. Implement security best practices
        3. Add comprehensive security headers
        4. Review CORS configuration
        """)

    if low_risk_requests == total_requests:
        st.success("✅ All requests have low security risk. Good security posture!")

    # Export Security Report
    st.markdown('<h3>📊 Export Security Report</h3>', unsafe_allow_html=True)

    if st.button("Generate Security Report"):
        # Create security report from cached results
        report_data = []
        for i, security_analysis in enumerate(security_results):
            issues = security_analysis.get('issues', [])
            row = df.iloc[i] if i < len(df) else {}

            for issue in issues:
                report_data.append({
                    'URL': row.get('url', ''),
                    'Method': row.get('method', ''),
                    'Status': row.get('status', ''),
                    'Risk Level': security_analysis.get('risk_level', 'LOW'),
                    'Issue Type': issue.get('type', ''),
                    'Severity': issue.get('severity', ''),
                    'Description': issue.get('description', ''),
                    'Recommendation': issue.get('recommendation', '')
                })

        if report_data:
            report_df = pd.DataFrame(report_data)
            csv = report_df.to_csv(index=False)
            st.download_button(
                label="Download Security Report (CSV)",
                data=csv,
                file_name=f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
            st.success("Security report generated successfully!")
        else:
            st.info("No security issues found to report.")

def main():
    # Add professional CSS styling
    st.markdown("""
    <style>
    /* Professional Enterprise-Grade Styling */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Professional Tab Styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 4px;
    }

    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding: 0 24px;
        background-color: transparent;
        border-radius: 6px;
        color: #495057;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
    }

    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Professional Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    /* Professional Success/Error Styling */
    .stSuccess {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
        color: #155724;
    }

    .stError {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
        color: #721c24;
    }

    .stWarning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        color: #856404;
    }

    .stInfo {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 6px;
        color: #0c5460;
    }

    /* Professional Metric Styling */
    .metric-container {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    /* Professional Header Styling */
    h1, h2, h3 {
        color: #495057;
        font-weight: 600;
    }

    /* Professional Sidebar Styling */
    .css-1d391kg {
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
    }
    </style>
    """, unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        render_sidebar()

    # Main content
    st.markdown('<h1>QA Automation Log Analysis Dashboard</h1>', unsafe_allow_html=True)

    # Create tabs
    tabs = st.tabs(["Dashboard", "Request Details", "API Comparison", "Security Testing", "AI Analysis", "Knowledge Base", "QA Reports", "Test Generator", "API Documentation", "Design Validation", "RC Testing", "Mobile Testing"])

    # Dashboard tab
    with tabs[0]:
        render_dashboard_tab()

    # Request Details tab
    with tabs[1]:
        render_request_details_tab()

    # API Comparison tab
    with tabs[2]:
        render_api_comparison_tab()

    # Security Testing tab
    with tabs[3]:
        render_security_testing_tab()

    # AI Analysis tab
    with tabs[4]:
        render_ai_analysis_tab()

    # Knowledge Base tab
    with tabs[5]:
        render_knowledge_base_tab()

    # QA Reports tab
    with tabs[6]:
        render_qa_reports_tab()

    # Karate Test Generator tab
    with tabs[7]:
        render_karate_test_generator_tab()

    # API Documentation tab
    with tabs[8]:
        render_api_documentation_tab()

    # Figma Validation tab
    with tabs[9]:
        render_figma_validation_tab()

    # RC Testing tab
    with tabs[10]:
        render_rc_testing_tab()

    # Emulator Testing tab
    with tabs[11]:
        render_emulator_testing_tab()

# Function to render the sidebar
def render_sidebar():
    st.markdown('<h3>QA Automation Tools</h3>', unsafe_allow_html=True)

    # Claude AI Settings
    with st.expander("Claude AI Settings", expanded=False):
        api_key = st.text_input("Enter your Anthropic API Key:", type="password", key="api_key")
        if api_key:
            os.environ["ANTHROPIC_API_KEY"] = api_key
            st.success("API key set! You can now use Claude AI for analysis.")

    # Upload log files
    st.markdown('<h4>Upload Log Files</h4>', unsafe_allow_html=True)

    # Version A (Primary) file upload
    st.markdown('<div style="margin-bottom: 10px;"><b>Version A (Primary)</b></div>', unsafe_allow_html=True)
    uploaded_file = st.file_uploader("Choose a Charles Proxy log file",
                                    type=["json", "chlsj", "har"],
                                    key="file_uploader_a",
                                    help="Upload the primary/baseline log file")

    # Version B (Comparison) file upload
    st.markdown('<div style="margin-top: 15px; margin-bottom: 10px;"><b>Version B (Comparison)</b></div>', unsafe_allow_html=True)
    uploaded_file_b = st.file_uploader("Choose a comparison log file",
                                      type=["json", "chlsj", "har"],
                                      key="file_uploader_b",
                                      help="Upload the secondary log file to compare against Version A")

    # API Comparison Settings
    with st.expander("API Comparison Settings", expanded=False):
        # Domain filtering
        st.markdown('<h5>Domain Filtering</h5>', unsafe_allow_html=True)

        # Paytm Dashboard specific filtering (OPTIONAL)
        st.markdown("**Optional Domain Filtering:**")
        paytm_filter_enabled = st.checkbox("Filter for Paytm Dashboard APIs only",
                                         value=getattr(st.session_state, 'paytm_filter_enabled', False),
                                         help="OPTIONAL: Show only requests from dashboard.paytm domain. Leave unchecked to see ALL APIs.")

        if paytm_filter_enabled:
            st.session_state.domain_filter = "paytm"
            st.session_state.paytm_filter_enabled = True
            st.info("Filtering enabled for Paytm Dashboard APIs (dashboard.paytm, api.paytm.com, etc.)")
        else:
            st.session_state.domain_filter = None
            st.session_state.paytm_filter_enabled = False
            if not paytm_filter_enabled:
                st.success("Showing ALL APIs from all domains (no filtering applied)")

        # General domain filtering
        domain_filter_enabled = st.checkbox("Enable custom domain filtering",
                                          value=st.session_state.comparison_settings["domain_filter_enabled"],
                                          help="Filter requests to include only specific domains")

        if domain_filter_enabled and not paytm_filter_enabled:
            domains_input = st.text_area("Enter domains to include (one per line):",
                                       height=100,
                                       help="e.g., api.example.com")
            domains = [d.strip() for d in domains_input.split('\n') if d.strip()]
        else:
            domains = []

        # Resource type filtering
        st.markdown('<h5>Resource Type Filtering</h5>', unsafe_allow_html=True)
        exclude_static = st.checkbox("Exclude static resources",
                                   value=st.session_state.comparison_settings["exclude_static_resources"],
                                   help="Exclude common static resources like JS, CSS, images, etc.")

        if exclude_static:
            st.markdown('<div style="font-size: 0.8em; color: #aaa;">Excluding: .js, .css, .woff, .png, .jpg, etc.</div>', unsafe_allow_html=True)

        custom_exclusions = st.text_input("Additional extensions to exclude (comma separated):",
                                        help="e.g., .map, .json, .xml", key="custom_exclusions")

        # Advanced filtering
        st.markdown('<h5>Advanced Filtering</h5>', unsafe_allow_html=True)

        http_methods = st.multiselect("HTTP Methods to include:",
                                    options=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
                                    default=st.session_state.comparison_settings["http_methods"])

        url_pattern = st.text_input("URL pattern to match (regex supported):",
                                  help="e.g., /api/v1/.*", key="url_pattern")

        # Fields to ignore during comparison
        st.markdown('<h5>Comparison Settings</h5>', unsafe_allow_html=True)
        ignore_fields_input = st.text_area("Fields to ignore during comparison (one per line):",
                                         height=100,
                                         value="\n".join(st.session_state.comparison_settings["ignore_fields"]),
                                         help="Fields that should be ignored when comparing JSON responses",
                                         key="ignore_fields")

        ignore_fields = [f.strip() for f in ignore_fields_input.split('\n') if f.strip()]

        # Update comparison settings
        st.session_state.comparison_settings.update({
            "domain_filter_enabled": domain_filter_enabled,
            "domains": domains,
            "exclude_static_resources": exclude_static,
            "custom_exclusions": custom_exclusions.split(',') if custom_exclusions else [],
            "http_methods": http_methods,
            "url_patterns": [url_pattern] if url_pattern else [],
            "ignore_fields": ignore_fields
        })

    # Test context
    with st.expander("Test Context", expanded=False):
        test_context = st.text_input("Test description:", key="test_context_input")
        if test_context != st.session_state.test_context:
            st.session_state.test_context = test_context

        # Test tags
        test_tags = st.text_input("Tags (comma separated):", key="test_tags_input")
        if test_tags != st.session_state.test_tags:
            st.session_state.test_tags = test_tags

    # QA Automation Framework Integration
    st.markdown('<h4>QA Automation Integration</h4>', unsafe_allow_html=True)
    framework_options = ["Selenium", "Cypress", "Playwright", "Appium", "RestAssured", "Custom"]
    selected_framework = st.selectbox("Select automation framework:", framework_options)

    if selected_framework:
        if selected_framework == "Selenium":
            st.info("Selenium integration enabled. Upload logs from Selenium test runs.")
        elif selected_framework == "Cypress":
            st.info("Cypress integration enabled. Upload logs from Cypress test runs.")
        elif selected_framework == "Playwright":
            st.info("Playwright integration enabled. Upload logs from Playwright test runs.")
        elif selected_framework == "Appium":
            st.info("Appium integration enabled. Upload logs from mobile test runs.")
        elif selected_framework == "RestAssured":
            st.info("RestAssured integration enabled. Upload logs from API test runs.")
        else:
            st.text_input("Custom framework name:")
            st.info("Custom framework integration. Configure in settings.")

    # Process uploaded file
    # Process Version A file
    if uploaded_file is not None:
        try:
            # Read and parse the file
            log_data = json.load(uploaded_file)

            # No need for manual structure handling - robust parser handles all formats

            # Store in session state
            st.session_state.log_data = log_data

            # Create DataFrame with domain filtering
            domain_filter = getattr(st.session_state, 'domain_filter', None)
            create_log_dataframe(log_data, domain_filter=domain_filter)

            # Get entry count from the created DataFrame
            entry_count = len(st.session_state.log_df) if st.session_state.log_df is not None else 0
            format_info = getattr(st.session_state, 'format_info_a', {})
            detected_format = format_info.get('detected_format', 'Unknown')

            # Show filtering info in success message
            filter_msg = ""
            domain_filter_active = getattr(st.session_state, 'domain_filter', None)
            if domain_filter_active == "paytm":
                filter_msg = " (🎯 Filtered for Paytm Dashboard APIs only)"
            else:
                filter_msg = " (✅ Showing ALL APIs - no filtering)"

            st.success(f"Successfully loaded Version A file with {entry_count} entries (Format: {detected_format}){filter_msg}")
        except Exception as e:
            st.error(f"Error loading Version A file: {str(e)}")

    # Process Version B file
    if uploaded_file_b is not None:
        try:
            # Read and parse the file
            log_data_b = json.load(uploaded_file_b)

            # No need for manual structure handling - robust parser handles all formats

            # Store in session state
            st.session_state.log_data_b = log_data_b

            # Create DataFrame for Version B with domain filtering
            domain_filter = getattr(st.session_state, 'domain_filter', None)
            df_b = create_log_dataframe_b(log_data_b, domain_filter=domain_filter)

            # Get entry count from the created DataFrame
            entry_count_b = len(st.session_state.log_df_b) if st.session_state.log_df_b is not None else 0
            format_info_b = getattr(st.session_state, 'format_info_b', {})
            detected_format_b = format_info_b.get('detected_format', 'Unknown')

            # Show filtering info in success message
            filter_msg_b = ""
            domain_filter_active = getattr(st.session_state, 'domain_filter', None)
            if domain_filter_active == "paytm":
                filter_msg_b = " (🎯 Filtered for Paytm Dashboard APIs only)"
            else:
                filter_msg_b = " (✅ Showing ALL APIs - no filtering)"

            st.success(f"Successfully loaded Version B file with {entry_count_b} entries (Format: {detected_format_b}){filter_msg_b}")

            # If both files are loaded, enable comparison
            if st.session_state.log_data is not None:
                st.session_state.current_tab = "API Comparison"

                # Auto-compare if both files are loaded
                with st.spinner("Comparing API requests..."):
                    compare_api_requests(st.session_state.log_df, df_b)
        except Exception as e:
            st.error(f"Error loading Version B file: {str(e)}")

    # Analyze with Claude if available (for Version A file)
    if uploaded_file is not None and st.session_state.log_data is not None:
        try:
            if CLAUDE_AVAILABLE and "ANTHROPIC_API_KEY" in os.environ:
                with st.spinner("Analyzing logs with Claude AI..."):
                    claude = ClaudeAgent()
                    qa_prompt = f"""
                    Analyze this Charles Proxy log from a QA automation perspective.
                    Test Context: {st.session_state.test_context}
                    Automation Framework: {selected_framework}

                    Focus on:
                    1. Identifying potential bugs or issues
                    2. Performance problems (slow responses, timeouts)
                    3. Error patterns and their root causes
                    4. Authentication or security concerns
                    5. API response validation issues
                    6. Test automation improvement opportunities
                    7. Patterns that could be added to automated test suites

                    Provide specific, actionable insights that would help a QA engineer improve test coverage.
                    Include code snippets or test patterns where appropriate.
                    """
                    analysis = claude.analyze_logs(st.session_state.log_df, specific_question=qa_prompt)
                    st.session_state.ai_analysis = analysis

                    # Save to knowledge base
                    tags = [tag.strip() for tag in st.session_state.test_tags.split(",")] if st.session_state.test_tags else []
                    # Add framework as a tag
                    if selected_framework and selected_framework not in tags:
                        tags.append(selected_framework)

                    session_id = kb.save_analysis_session(
                        log_data=log_data,
                        log_df=st.session_state.log_df,
                        ai_analysis=analysis,
                        file_name=uploaded_file.name,
                        test_context=st.session_state.test_context,
                        tags=tags
                    )
                    st.success(f"Analysis saved to knowledge base with ID: {session_id}")

                    # Generate QA report
                    st.session_state.qa_report = kb.generate_qa_report(session_id)
        except Exception as e:
            st.error(f"Error analyzing file: {str(e)}")

    # Knowledge base navigation
    st.markdown('<h4>Knowledge Base</h4>', unsafe_allow_html=True)
    kb_col1, kb_col2 = st.columns(2)
    with kb_col1:
        if st.button("View All Sessions", key="view_sessions_btn"):
            st.session_state.current_tab = "knowledge_base"
    with kb_col2:
        if st.button("Test Patterns", key="test_patterns_btn"):
            st.session_state.current_tab = "knowledge_base"
            st.session_state.kb_section = "patterns"

    # Search
    st.markdown('<h4>Search</h4>', unsafe_allow_html=True)
    search_query = st.text_input("Search knowledge base:", key="sidebar_search")
    if search_query:
        search_results = kb.search(search_query)
        st.session_state.search_results = search_results
        st.session_state.current_tab = "knowledge_base"

    # Export/Import
    st.markdown('<h4>Export/Import</h4>', unsafe_allow_html=True)
    exp_col1, exp_col2 = st.columns(2)
    with exp_col1:
        if st.button("Export KB", key="export_kb_sidebar"):
            export_path = kb.export_knowledge_base("exports")
            st.success(f"Knowledge base exported to: {export_path}")
    with exp_col2:
        import_file = st.file_uploader("Import KB", type=["json"], key="import_kb_sidebar")
        if import_file:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as tmp:
                tmp.write(import_file.getvalue())
                tmp_path = tmp.name

            success = kb.import_knowledge_base(tmp_path)
            if success:
                st.success("Knowledge base imported successfully!")
            else:
                st.error("Failed to import knowledge base.")

# Call the main function
if __name__ == "__main__":
    main()
