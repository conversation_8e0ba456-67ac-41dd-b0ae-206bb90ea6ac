#!/usr/bin/env python3
import os
import json
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime
import uuid
import time
from typing import Dict, List, Any, Optional, Union
import re
import sqlite3
from qa_knowledge_base import QAKnowledgeBase

# Import Claude AI agent if available
try:
    from claude_agent import Claude<PERSON>gent
    CLAUDE_AVAILABLE = True
except ImportError:
    CLAUDE_AVAILABLE = False
    # Create a simple fallback agent
    class ClaudeAgent:
        def analyze_logs(self, log_data, prompt=None):
            return ["Claude AI agent not available. Please check that claude_agent.py exists."]

# Set page configuration
st.set_page_config(
    page_title="QA Automation Log Analysis Dashboard",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize knowledge base
kb = QAKnowledgeBase()

# Initialize session state variables
if "log_data" not in st.session_state:
    st.session_state.log_data = None
if "log_df" not in st.session_state:
    st.session_state.log_df = None
if "ai_analysis" not in st.session_state:
    st.session_state.ai_analysis = None
if "analysis_history" not in st.session_state:
    st.session_state.analysis_history = []
if "selected_analysis" not in st.session_state:
    st.session_state.selected_analysis = None
if "current_tab" not in st.session_state:
    st.session_state.current_tab = "dashboard"
if "filter_settings" not in st.session_state:
    st.session_state.filter_settings = {
        "status_filter": [],
        "host_filter": [],
        "method_filter": [],
        "time_range": [0, 10000],
        "search_query": ""
    }
if "test_context" not in st.session_state:
    st.session_state.test_context = ""
if "test_tags" not in st.session_state:
    st.session_state.test_tags = ""
if "qa_report" not in st.session_state:
    st.session_state.qa_report = None

# Apply custom styling
def apply_custom_styling():
    """Apply custom styling for the dashboard"""
    st.markdown("""
    <style>
    /* Main theme colors */
    :root {
        --primary-color: #4361ee;
        --secondary-color: #3a0ca3;
        --background-color: #f8f9fa;
        --text-color: #212529;
        --light-accent: #e9ecef;
        --dark-accent: #495057;
        --success-color: #38b000;
        --warning-color: #ff9e00;
        --error-color: #d90429;
    }

    /* Base styling */
    .main {
        background-color: var(--background-color);
        color: var(--text-color);
    }

    /* Headers */
    h1, h2, h3 {
        color: var(--primary-color);
    }

    /* Cards */
    .card {
        background-color: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    /* Metrics */
    .metric-card {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .metric-label {
        font-size: 0.9rem;
        color: var(--dark-accent);
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
    }
    
    .status-2xx {
        background-color: var(--success-color);
        color: white;
    }
    
    .status-3xx {
        background-color: var(--primary-color);
        color: white;
    }
    
    .status-4xx {
        background-color: var(--warning-color);
        color: black;
    }
    
    .status-5xx {
        background-color: var(--error-color);
        color: white;
    }

    /* Insights */
    .insight-card {
        background-color: #f1f8fe;
        border-left: 4px solid var(--primary-color);
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }
    
    .insight-title {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    /* Severity indicators */
    .severity-high {
        color: var(--error-color);
        font-weight: bold;
    }
    
    .severity-medium {
        color: var(--warning-color);
        font-weight: bold;
    }
    
    .severity-low {
        color: var(--success-color);
        font-weight: bold;
    }

    /* Knowledge base */
    .kb-card {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .kb-title {
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .kb-meta {
        font-size: 0.8rem;
        color: var(--dark-accent);
    }
    
    /* Tags */
    .tag {
        display: inline-block;
        background-color: var(--light-accent);
        color: var(--dark-accent);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    /* Recommendations */
    .recommendation {
        background-color: #e6f3ff;
        border-left: 4px solid #0077cc;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }
    </style>
    """, unsafe_allow_html=True)

# Apply styling
apply_custom_styling()

# Main function to run the app
def main():
    # Sidebar
    with st.sidebar:
        render_sidebar()
    
    # Main content
    st.markdown('<h1>QA Automation Log Analysis Dashboard</h1>', unsafe_allow_html=True)
    
    # Create all 12 tabs
    tabs = st.tabs([
        "📊 Dashboard",
        "🔍 Request Details",
        "📋 HAR Comparison",
        "🤖 Karate Test Generator",
        "📋 API Documentation",
        "🎨 P4B Figma Validation",
        "📱 RC Testing",
        "📱 Emulator Testing",
        "🔗 Chain Testing",
        "📚 Knowledge Base",
        "🤖 AI Analysis",
        "📋 QA Reports"
    ])
    
    # All 12 tabs
    with tabs[0]:
        render_dashboard_tab()

    with tabs[1]:
        render_request_details_tab()

    with tabs[2]:
        render_har_comparison_tab()

    with tabs[3]:
        render_karate_test_generator_tab()

    with tabs[4]:
        render_api_documentation_tab()

    with tabs[5]:
        render_figma_validation_tab()

    with tabs[6]:
        render_rc_testing_tab()

    with tabs[7]:
        render_emulator_testing_tab()

    with tabs[8]:
        render_chain_testing_tab()

    with tabs[9]:
        render_knowledge_base_tab()

    with tabs[10]:
        render_ai_analysis_tab()

    with tabs[11]:
        render_qa_reports_tab()

# Function to render the sidebar
def render_sidebar():
    st.markdown('<h3>QA Automation Tools</h3>', unsafe_allow_html=True)
    
    # Claude AI Settings
    st.markdown('<h4>Claude AI Settings</h4>', unsafe_allow_html=True)
    api_key = st.text_input("Enter your Anthropic API Key:", type="password", key="api_key")
    if api_key:
        os.environ["ANTHROPIC_API_KEY"] = api_key
        st.success("API key set! You can now use Claude AI for analysis.")
    
    # Upload log file
    st.markdown('<h4>Upload Log File</h4>', unsafe_allow_html=True)
    uploaded_file = st.file_uploader("Choose a Charles Proxy log file", type=["json", "chlsj"])
    
    # Test context
    st.markdown('<h4>Test Context</h4>', unsafe_allow_html=True)
    test_context = st.text_input("Test description:", key="test_context_input")
    if test_context != st.session_state.test_context:
        st.session_state.test_context = test_context
    
    # Test tags
    test_tags = st.text_input("Tags (comma separated):", key="test_tags_input")
    if test_tags != st.session_state.test_tags:
        st.session_state.test_tags = test_tags
    
    # Process uploaded file
    if uploaded_file is not None:
        try:
            # Read and parse the file
            log_data = json.load(uploaded_file)
            
            # Store in session state
            st.session_state.log_data = log_data
            
            # Create DataFrame
            create_log_dataframe(log_data)
            
            st.success(f"Successfully loaded log file with {len(log_data.get('entries', []))} entries")
            
            # Analyze with Claude if available
            if CLAUDE_AVAILABLE and "ANTHROPIC_API_KEY" in os.environ:
                with st.spinner("Analyzing logs with Claude AI..."):
                    claude = ClaudeAgent()
                    qa_prompt = f"""
                    Analyze this Charles Proxy log from a QA automation perspective.
                    Test Context: {st.session_state.test_context}
                    
                    Focus on:
                    1. Identifying potential bugs or issues
                    2. Performance problems (slow responses, timeouts)
                    3. Error patterns and their root causes
                    4. Authentication or security concerns
                    5. API response validation issues
                    
                    Provide specific, actionable insights that would help a QA engineer improve test coverage.
                    """
                    analysis = claude.analyze_logs(log_data, prompt=qa_prompt)
                    st.session_state.ai_analysis = analysis
                    
                    # Save to knowledge base
                    tags = [tag.strip() for tag in st.session_state.test_tags.split(",")] if st.session_state.test_tags else []
                    session_id = kb.save_analysis_session(
                        log_data=log_data,
                        log_df=st.session_state.log_df,
                        ai_analysis=analysis,
                        file_name=uploaded_file.name,
                        test_context=st.session_state.test_context,
                        tags=tags
                    )
                    st.success(f"Analysis saved to knowledge base with ID: {session_id}")
        except Exception as e:
            st.error(f"Error loading file: {str(e)}")
    
    # Knowledge base navigation
    st.markdown('<h4>Knowledge Base</h4>', unsafe_allow_html=True)
    if st.button("View All Sessions"):
        st.session_state.current_tab = "knowledge_base"
    
    # Search
    st.markdown('<h4>Search</h4>', unsafe_allow_html=True)
    search_query = st.text_input("Search knowledge base:")
    if search_query:
        search_results = kb.search(search_query)
        st.session_state.search_results = search_results
        st.session_state.current_tab = "knowledge_base"

# Import the missing functions
from qa_integrated_dashboard_functions import (
    create_log_dataframe,
    render_dashboard_tab,
    render_request_details_tab
)

# Function to render AI analysis tab
def render_ai_analysis_tab():
    """Render the AI analysis tab"""
    st.markdown('<h2>AI Analysis</h2>', unsafe_allow_html=True)

    if st.session_state.ai_analysis:
        st.markdown('<h3>Analysis Results</h3>', unsafe_allow_html=True)

        # Display analysis
        for insight in st.session_state.ai_analysis:
            st.markdown(f"""
            <div class="insight-card">
                <div class="insight-title">AI Insight</div>
                <div>{insight}</div>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No AI analysis available. Upload a log file to get started.")

# Function to render knowledge base tab
def render_knowledge_base_tab():
    """Render the knowledge base tab"""
    st.markdown('<h2>Knowledge Base</h2>', unsafe_allow_html=True)

    # Display recent sessions
    sessions = kb.get_all_sessions()[:10]  # Get first 10 sessions

    if sessions:
        st.markdown('<h3>Recent Analysis Sessions</h3>', unsafe_allow_html=True)

        for session in sessions:
            st.markdown(f"""
            <div class="kb-card">
                <div class="kb-title">{session.get('file_name', 'Unknown')}</div>
                <div class="kb-meta">
                    Date: {session.get('timestamp', 'Unknown')} |
                    Context: {session.get('test_context', 'None')}
                </div>
                <div>{session.get('summary', 'No summary available')}</div>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No analysis sessions found. Upload and analyze log files to build your knowledge base.")

# Function to render QA reports tab
def render_qa_reports_tab():
    """Render the QA reports tab"""
    st.markdown('<h2>QA Reports</h2>', unsafe_allow_html=True)

    if st.session_state.log_df is not None and not st.session_state.log_df.empty:
        df = st.session_state.log_df

        # Generate report
        st.markdown('<h3>Test Execution Report</h3>', unsafe_allow_html=True)

        # Summary metrics
        total_requests = len(df)
        success_count = len(df[df["status"].between(200, 299)])
        error_count = len(df[df["status"] >= 400])
        success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total Requests", total_requests)
        with col2:
            st.metric("Success Rate", f"{success_rate:.1f}%")
        with col3:
            st.metric("Error Count", error_count)

        # Detailed findings
        st.markdown('<h4>Key Findings</h4>', unsafe_allow_html=True)

        if error_count > 0:
            st.markdown(f"""
            <div class="recommendation">
                <strong>⚠️ Errors Detected:</strong> Found {error_count} failed requests that require investigation.
            </div>
            """, unsafe_allow_html=True)

        if success_rate < 95:
            st.markdown(f"""
            <div class="recommendation">
                <strong>📊 Success Rate:</strong> Current success rate is {success_rate:.1f}%. Consider investigating failed requests.
            </div>
            """, unsafe_allow_html=True)

        # Performance insights
        avg_response_time = df["time"].mean()
        slow_requests = len(df[df["time"] > 5000])  # > 5 seconds

        if slow_requests > 0:
            st.markdown(f"""
            <div class="recommendation">
                <strong>⏱️ Performance:</strong> Found {slow_requests} slow requests (>5s). Average response time: {avg_response_time:.0f}ms.
            </div>
            """, unsafe_allow_html=True)
    else:
        st.info("No data available for report generation. Please upload a log file.")

def render_har_comparison_tab():
    """HAR comparison tab"""
    st.markdown("## 📋 HAR File Comparison")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### Upload First HAR File")
        file1 = st.file_uploader("Choose first HAR file", type=['har', 'json'], key="har1")
        if file1:
            st.success(f"✅ {file1.name} loaded")

    with col2:
        st.markdown("### Upload Second HAR File")
        file2 = st.file_uploader("Choose second HAR file", type=['har', 'json'], key="har2")
        if file2:
            st.success(f"✅ {file2.name} loaded")

    if file1 and file2:
        if st.button("🔍 Compare Files", type="primary"):
            with st.spinner("Comparing HAR files..."):
                time.sleep(2)
                st.success("✅ Comparison completed!")

                # Comparison results
                st.markdown("### Comparison Results")

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Common Endpoints", "15", "3")
                with col2:
                    st.metric("Response Time Diff", "+250ms", "12%")
                with col3:
                    st.metric("Status Code Changes", "2", "-1")

def render_karate_test_generator_tab():
    """Karate test generator tab"""
    try:
        from karate_test_generator import KarateTestGenerator

        st.markdown("## 🤖 Karate Test Generator")

        generator = KarateTestGenerator()
        uploaded_file = st.file_uploader("Upload API Specification", type=['json', 'yaml'], key="karate")

        if uploaded_file:
            st.success("✅ API specification uploaded!")

            col1, col2 = st.columns(2)
            with col1:
                test_types = st.multiselect("Test Types", ["Functional", "Performance", "Security"], default=["Functional"])
            with col2:
                environment = st.selectbox("Environment", ["Development", "Staging", "Production"])

            if st.button("🚀 Generate Tests", type="primary"):
                with st.spinner("Generating tests..."):
                    try:
                        test_content = generator.generate_from_file(uploaded_file, test_types, environment)
                        st.success("✅ Tests generated!")
                        st.code(test_content, language="gherkin")
                        st.download_button("📥 Download", data=test_content, file_name="tests.feature")
                    except Exception as e:
                        st.error(f"Generation failed: {str(e)}")
    except ImportError:
        st.error("❌ Karate Test Generator not found")

def render_api_documentation_tab():
    """API documentation tab"""
    try:
        from api_documentation_tab import render_api_documentation_tab
        render_api_documentation_tab()
    except ImportError:
        st.error("❌ API Documentation module not found")

def render_figma_validation_tab():
    """Figma validation tab"""
    try:
        from figma_validation_tab import render_figma_validation_tab
        render_figma_validation_tab()
    except ImportError:
        st.error("❌ Figma Validation module not found")

def render_rc_testing_tab():
    """RC testing tab"""
    try:
        from rc_testing_tab import render_rc_testing_tab
        render_rc_testing_tab()
    except ImportError:
        st.error("❌ RC Testing module not found")

def render_emulator_testing_tab():
    """Emulator testing tab"""
    try:
        from emulator_testing_tab import render_emulator_testing_tab
        render_emulator_testing_tab()
    except ImportError:
        st.error("❌ Emulator Testing module not found")

def render_chain_testing_tab():
    """Chain testing tab"""
    try:
        from chain_testing_interface import render_chain_testing_interface
        class MockDriver:
            def is_connected(self): return False
            def get_connection_status(self): return {"connected": False}
        render_chain_testing_interface(MockDriver())
    except ImportError:
        st.error("❌ Chain Testing module not found")

# Run the app
if __name__ == "__main__":
    main()
