#!/usr/bin/env python3
import os
import json
import requests
from typing import Dict, List, Any, Optional
import pandas as pd

class ClaudeAgent:
    """Claude AI agent for analyzing Charles Proxy logs"""

    def __init__(self, api_key=None):
        """Initialize the Claude agent"""
        # Use provided API key or get from environment
        self.api_key = api_key or os.environ.get("ANTHROPIC_API_KEY")
        self.api_url = "https://api.anthropic.com/v1/messages"
        self.model = "claude-3-opus-20240229"  # Can be changed to other Claude models

    def analyze_logs(self, df: pd.DataFrame, specific_question: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze log data using Claude and return insights

        Args:
            df: DataFrame containing log data
            specific_question: Optional specific question to ask <PERSON> about the logs

        Returns:
            Dictionary with analysis results
        """
        if df.empty:
            return {"error": "No data available for analysis."}

        # Convert DataFrame to JSON for <PERSON>
        log_data = df.head(50).to_json(orient="records", indent=2)

        # Create prompt for <PERSON>
        if specific_question:
            prompt = self._create_specific_prompt(log_data, specific_question)
        else:
            prompt = self._create_general_prompt(log_data)

        # Call Claude API
        try:
            response = self._call_claude_api(prompt)
            return {
                "success": True,
                "analysis": response,
                "data_sample_size": min(50, len(df))
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _create_general_prompt(self, log_data: str) -> str:
        """Create a general analysis prompt for Claude"""
        return f"""You are an expert at analyzing HTTP logs and network traffic.

I'm going to provide you with Charles Proxy log data in JSON format. Please analyze this data and provide insights about:

1. Performance issues (slow requests, patterns in response times)
2. Error patterns (HTTP error codes, failed requests)
3. Security concerns (suspicious patterns, potential vulnerabilities)
4. Optimization opportunities (redundant requests, caching issues)
5. General statistics and patterns

Here's the log data (limited to 50 entries for brevity):

```json
{log_data}
```

Please provide a detailed analysis with specific insights. Format your response in markdown with clear sections and bullet points where appropriate. Include specific examples from the data to support your findings."""

    def _create_specific_prompt(self, log_data: str, question: str) -> str:
        """Create a prompt for a specific question about the logs"""
        return f"""You are an expert at analyzing HTTP logs and network traffic.

I'm going to provide you with Charles Proxy log data in JSON format, and I have a specific question about this data:

"{question}"

Here's the log data (limited to 50 entries for brevity):

```json
{log_data}
```

Please answer my question with detailed analysis and specific examples from the data. Format your response in markdown with clear sections and bullet points where appropriate."""

    def _call_claude_api(self, prompt: str) -> str:
        """Call the Claude API with the given prompt"""
        if not self.api_key:
            return "Error: No API key provided. Please set the ANTHROPIC_API_KEY environment variable or provide an API key when initializing the agent."

        # Check if using a placeholder API key
        if self.api_key == "your-api-key":
            # Return a fallback response using the local AI agent
            return self._generate_fallback_response(prompt)

        try:
            headers = {
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01",
                "content-type": "application/json"
            }

            data = {
                "model": self.model,
                "max_tokens": 4000,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }

            response = requests.post(self.api_url, headers=headers, json=data)

            if response.status_code != 200:
                error_message = f"Claude API returned status code {response.status_code}"
                try:
                    error_details = response.json()
                    error_message += f": {error_details.get('error', {}).get('message', 'Unknown error')}"
                except:
                    pass
                print(error_message)
                return self._generate_fallback_response(prompt)

            try:
                result = response.json()
                return result["content"][0]["text"]
            except (KeyError, IndexError) as e:
                print(f"Unexpected response format from Claude API: {str(e)}")
                print(f"Response: {response.text[:500]}...")
                return self._generate_fallback_response(prompt)
        except Exception as e:
            print(f"Error calling Claude API: {str(e)}")
            return self._generate_fallback_response(prompt)

    def _generate_fallback_response(self, prompt: str) -> str:
        """Generate a fallback response when Claude API is not available"""
        # Extract log data from the prompt
        import re
        log_data_match = re.search(r'```json\n(.*?)\n```', prompt, re.DOTALL)
        log_data_str = log_data_match.group(1) if log_data_match else "{}"

        try:
            # Parse the log data
            import json
            log_data = json.loads(log_data_str)

            # Convert to DataFrame
            import pandas as pd
            if isinstance(log_data, list):
                df = pd.DataFrame(log_data)
            else:
                df = pd.DataFrame([log_data])

            # Generate basic insights
            insights = []

            # Summary
            insights.append("## Log Analysis Summary\n\nThis analysis was generated by the local fallback agent because Claude AI is not available.")

            # Status code analysis
            if "status" in df.columns:
                status_counts = df["status"].astype(str).value_counts()
                insights.append("\n## Status Code Analysis\n")
                insights.append("### Status Code Distribution\n")
                for status, count in status_counts.items():
                    insights.append(f"- Status {status}: {count} requests")

            # Performance analysis
            if "duration" in df.columns or "actualTime" in df.columns:
                duration_col = "duration" if "duration" in df.columns else "actualTime"
                avg_duration = df[duration_col].mean()
                max_duration = df[duration_col].max()
                insights.append("\n## Performance Analysis\n")
                insights.append(f"- Average response time: {avg_duration:.1f} ms")
                insights.append(f"- Maximum response time: {max_duration:.1f} ms")

            # Host analysis
            if "host" in df.columns:
                host_counts = df["host"].value_counts()
                insights.append("\n## Host Analysis\n")
                for host, count in host_counts.items():
                    insights.append(f"- {host}: {count} requests")

            # Method analysis
            if "method" in df.columns:
                method_counts = df["method"].value_counts()
                insights.append("\n## Method Analysis\n")
                for method, count in method_counts.items():
                    insights.append(f"- {method}: {count} requests")

            return "\n".join(insights)
        except Exception as e:
            return f"Error generating fallback response: {str(e)}\n\nPlease set a valid Anthropic API key to use Claude AI for analysis."

# Create a singleton instance (will use API key from environment variable)
claude_agent = ClaudeAgent()

# Example usage:
# Set your API key as an environment variable: export ANTHROPIC_API_KEY="your-api-key"
# Or initialize with a key: claude_agent = ClaudeAgent(api_key="your-api-key")
# Then call: results = claude_agent.analyze_logs(dataframe)
