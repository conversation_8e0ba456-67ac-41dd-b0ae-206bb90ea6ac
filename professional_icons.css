/* Professional Enterprise-Grade Icons for QA Analytics Dashboard */

/* Base icon styles */
.icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 8px;
}

.icon-lg {
    width: 24px;
    height: 24px;
}

.icon-xl {
    width: 32px;
    height: 32px;
}

/* Status Icons */
.icon-success::before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
}

.icon-error::before {
    content: "✗";
    color: #dc3545;
    font-weight: bold;
}

.icon-warning::before {
    content: "⚠";
    color: #ffc107;
    font-weight: bold;
}

.icon-info::before {
    content: "ⓘ";
    color: #17a2b8;
    font-weight: bold;
}

/* Navigation Icons */
.icon-dashboard::before {
    content: "⊞";
    color: #495057;
}

.icon-mobile::before {
    content: "📱";
    color: #495057;
}

.icon-chain::before {
    content: "⛓";
    color: #495057;
}

.icon-settings::before {
    content: "⚙";
    color: #495057;
}

.icon-analytics::before {
    content: "📊";
    color: #495057;
}

/* Action Icons */
.icon-play::before {
    content: "▶";
    color: #28a745;
}

.icon-stop::before {
    content: "⏹";
    color: #dc3545;
}

.icon-pause::before {
    content: "⏸";
    color: #ffc107;
}

.icon-refresh::before {
    content: "↻";
    color: #17a2b8;
}

.icon-download::before {
    content: "⬇";
    color: #6c757d;
}

.icon-upload::before {
    content: "⬆";
    color: #6c757d;
}

/* Tool Icons */
.icon-build::before {
    content: "🔨";
    color: #495057;
}

.icon-code::before {
    content: "⟨⟩";
    color: #495057;
}

.icon-test::before {
    content: "🧪";
    color: #495057;
}

.icon-security::before {
    content: "🛡";
    color: #495057;
}

/* Connection Icons */
.icon-connected::before {
    content: "●";
    color: #28a745;
}

.icon-disconnected::before {
    content: "●";
    color: #dc3545;
}

.icon-connecting::before {
    content: "●";
    color: #ffc107;
    animation: pulse 1s infinite;
}

/* File Icons */
.icon-file::before {
    content: "📄";
    color: #495057;
}

.icon-folder::before {
    content: "📁";
    color: #495057;
}

.icon-image::before {
    content: "🖼";
    color: #495057;
}

/* Professional Button Styles */
.btn-professional {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-professional:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Professional Card Styles */
.card-professional {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e9ecef;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.card-professional:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Progress Indicators */
.progress-professional {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-professional {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Professional Typography */
.text-professional {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
}

.heading-professional {
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
}

.subheading-professional {
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

/* Professional Table Styles */
.table-professional {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-professional th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.table-professional td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

.table-professional tr:hover {
    background-color: #f8f9fa;
}

/* Professional Form Elements */
.form-professional {
    margin-bottom: 1rem;
}

.form-label-professional {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-input-professional {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input-professional:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
