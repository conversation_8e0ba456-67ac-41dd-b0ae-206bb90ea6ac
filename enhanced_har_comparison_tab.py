"""
Enhanced HAR File Comparison Tab
Comprehensive UI for HAR file comparison with advanced filtering and analysis
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime
from har_comparison_engine import <PERSON><PERSON><PERSON><PERSON>parisonEngine, Comparison<PERSON><PERSON>ult
from typing import Dict, List, Any, Optional

def render_enhanced_har_comparison_tab():
    """Render the enhanced HAR comparison tab with full functionality"""
    st.markdown("## 🔄 Advanced HAR File Comparison")
    st.markdown("Compare Charles Proxy logs and HAR files with advanced filtering and analysis")
    
    # Initialize comparison engine
    if 'har_engine' not in st.session_state:
        st.session_state['har_engine'] = HARComparisonEngine()
    
    engine = st.session_state['har_engine']
    
    # Sidebar controls
    with st.sidebar:
        st.markdown("### 🎛️ Comparison Controls")
        
        # Domain filtering
        st.markdown("#### 🌐 Domain Filtering")
        domain_filter_enabled = st.checkbox("Enable Domain Filtering", key="domain_filter_enabled")
        if domain_filter_enabled:
            domain_input = st.text_area(
                "Domains to include (one per line)",
                placeholder="dashboard.paytm\napi.example.com\ncdn.domain.com",
                key="domain_filter_input"
            )
            domains = [d.strip() for d in domain_input.split('\n') if d.strip()]
            engine.set_domain_filters(domains)
            if domains:
                st.success(f"✅ Filtering for {len(domains)} domains")
        else:
            engine.set_domain_filters([])
        
        # Resource type filtering
        st.markdown("#### 📁 Resource Type Filtering")
        resource_types = st.multiselect(
            "Select resource types to include",
            options=['document', 'script', 'stylesheet', 'image', 'xhr', 'font', 'other'],
            default=[],
            key="resource_type_filter"
        )
        engine.set_resource_type_filters(resource_types)
        
        # Comparison options
        st.markdown("#### ⚙️ Comparison Options")
        compare_headers = st.checkbox("Compare Headers", value=True, key="compare_headers")
        compare_content = st.checkbox("Compare Content", value=True, key="compare_content")
        compare_timing = st.checkbox("Compare Timing", value=True, key="compare_timing")
        ignore_dynamic = st.checkbox("Ignore Dynamic Parameters", value=True, key="ignore_dynamic")
        
        engine.set_comparison_options({
            'compare_headers': compare_headers,
            'compare_content': compare_content,
            'compare_timing': compare_timing,
            'ignore_dynamic_params': ignore_dynamic
        })
    
    # Main content area
    col1, col2 = st.columns(2)
    
    # File upload section
    with col1:
        st.markdown("### 📁 Upload First File")
        file1 = st.file_uploader(
            "Choose first HAR/Charles log file",
            type=['har', 'json', 'chlsj'],
            key="har_file1",
            help="Supports HAR, Charles Session JSON (.chlsj), and generic JSON formats"
        )
        
        if file1:
            st.success(f"✅ {file1.name} loaded ({file1.size:,} bytes)")
            
            # Parse and store file1 data
            if 'file1_data' not in st.session_state or st.session_state.get('file1_name') != file1.name:
                with st.spinner("Parsing first file..."):
                    try:
                        file1_content = file1.read()
                        st.session_state['file1_data'] = engine.parse_har_file(file1_content, file1.name)
                        st.session_state['file1_name'] = file1.name
                        st.success(f"📊 Parsed {st.session_state['file1_data']['total_entries']} entries")
                    except Exception as e:
                        st.error(f"❌ Error parsing file: {str(e)}")
                        st.session_state['file1_data'] = None
    
    with col2:
        st.markdown("### 📁 Upload Second File")
        file2 = st.file_uploader(
            "Choose second HAR/Charles log file",
            type=['har', 'json', 'chlsj'],
            key="har_file2",
            help="Supports HAR, Charles Session JSON (.chlsj), and generic JSON formats"
        )
        
        if file2:
            st.success(f"✅ {file2.name} loaded ({file2.size:,} bytes)")
            
            # Parse and store file2 data
            if 'file2_data' not in st.session_state or st.session_state.get('file2_name') != file2.name:
                with st.spinner("Parsing second file..."):
                    try:
                        file2_content = file2.read()
                        st.session_state['file2_data'] = engine.parse_har_file(file2_content, file2.name)
                        st.session_state['file2_name'] = file2.name
                        st.success(f"📊 Parsed {st.session_state['file2_data']['total_entries']} entries")
                    except Exception as e:
                        st.error(f"❌ Error parsing file: {str(e)}")
                        st.session_state['file2_data'] = None
    
    # File preview section
    if st.session_state.get('file1_data') or st.session_state.get('file2_data'):
        st.markdown("---")
        st.markdown("### 👀 File Preview")
        
        preview_col1, preview_col2 = st.columns(2)
        
        with preview_col1:
            if st.session_state.get('file1_data'):
                data1 = st.session_state['file1_data']
                st.markdown(f"**{data1['filename']}**")
                st.info(f"Format: {data1['format']} | Entries: {data1['total_entries']}")
                
                # Show sample entries
                if data1['entries']:
                    sample_df = pd.DataFrame(data1['entries'][:5])
                    st.dataframe(sample_df[['method', 'url', 'status_code', 'response_time']], use_container_width=True)
        
        with preview_col2:
            if st.session_state.get('file2_data'):
                data2 = st.session_state['file2_data']
                st.markdown(f"**{data2['filename']}**")
                st.info(f"Format: {data2['format']} | Entries: {data2['total_entries']}")
                
                # Show sample entries
                if data2['entries']:
                    sample_df = pd.DataFrame(data2['entries'][:5])
                    st.dataframe(sample_df[['method', 'url', 'status_code', 'response_time']], use_container_width=True)
    
    # Comparison section
    if st.session_state.get('file1_data') and st.session_state.get('file2_data'):
        st.markdown("---")
        
        # Comparison button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔄 Compare Files", type="primary", use_container_width=True):
                with st.spinner("Performing comprehensive comparison..."):
                    try:
                        # Perform comparison
                        comparison_result = engine.compare_files(
                            st.session_state['file1_data'],
                            st.session_state['file2_data']
                        )
                        
                        # Generate detailed report
                        comparison_report = engine.generate_comparison_report(
                            comparison_result,
                            st.session_state['file1_name'],
                            st.session_state['file2_name']
                        )
                        
                        # Store results
                        st.session_state['comparison_result'] = comparison_result
                        st.session_state['comparison_report'] = comparison_report
                        
                        st.success("✅ Comparison completed successfully!")
                        
                    except Exception as e:
                        st.error(f"❌ Comparison failed: {str(e)}")
    
    # Display comparison results
    if st.session_state.get('comparison_result') and st.session_state.get('comparison_report'):
        render_comparison_results()

def render_comparison_results():
    """Render comprehensive comparison results"""
    result = st.session_state['comparison_result']
    report = st.session_state['comparison_report']
    
    st.markdown("---")
    st.markdown("## 📊 Comparison Results")
    
    # Executive Summary
    st.markdown("### 📋 Executive Summary")
    
    summary_col1, summary_col2, summary_col3, summary_col4 = st.columns(4)
    
    with summary_col1:
        st.metric(
            "Common Endpoints",
            result.common_endpoints,
            delta=None,
            help="Endpoints present in both files"
        )
    
    with summary_col2:
        delta_color = "inverse" if result.response_time_diff > 0 else "normal"
        st.metric(
            "Avg Response Time Diff",
            f"{result.response_time_diff:.0f}ms",
            delta=f"{result.response_time_diff:+.0f}ms",
            delta_color=delta_color,
            help="Average response time difference (File2 - File1)"
        )
    
    with summary_col3:
        st.metric(
            "Status Code Changes",
            result.status_code_changes,
            delta=result.status_code_changes if result.status_code_changes > 0 else None,
            delta_color="inverse" if result.status_code_changes > 0 else "normal",
            help="Number of endpoints with different status codes"
        )
    
    with summary_col4:
        st.metric(
            "Content Changes",
            result.content_changes,
            delta=result.content_changes if result.content_changes > 0 else None,
            help="Number of endpoints with different response content"
        )
    
    # Detailed Analysis Tabs
    analysis_tabs = st.tabs([
        "📈 Performance Analysis",
        "🔒 Security Analysis", 
        "🔍 Detailed Differences",
        "📊 Visual Comparison",
        "📄 Export Report"
    ])
    
    with analysis_tabs[0]:
        render_performance_analysis(result.performance_analysis)
    
    with analysis_tabs[1]:
        render_security_analysis(result.security_analysis)
    
    with analysis_tabs[2]:
        render_detailed_differences(result.detailed_differences)
    
    with analysis_tabs[3]:
        render_visual_comparison(result)
    
    with analysis_tabs[4]:
        render_export_options(report)

def render_performance_analysis(performance_data: Dict[str, Any]):
    """Render performance analysis section"""
    st.markdown("#### ⚡ Performance Analysis")

    if 'error' in performance_data:
        st.error(f"Performance analysis failed: {performance_data['error']}")
        return

    # Performance metrics comparison
    perf_col1, perf_col2 = st.columns(2)

    with perf_col1:
        st.markdown("**File 1 Performance**")
        st.metric("Average Response Time", f"{performance_data.get('file1_avg_response_time', 0):.0f}ms")
        st.metric("Median Response Time", f"{performance_data.get('file1_median_response_time', 0):.0f}ms")
        st.metric("95th Percentile", f"{performance_data.get('file1_95th_percentile', 0):.0f}ms")

    with perf_col2:
        st.markdown("**File 2 Performance**")
        st.metric("Average Response Time", f"{performance_data.get('file2_avg_response_time', 0):.0f}ms")
        st.metric("Median Response Time", f"{performance_data.get('file2_median_response_time', 0):.0f}ms")
        st.metric("95th Percentile", f"{performance_data.get('file2_95th_percentile', 0):.0f}ms")

    # Performance regression alert
    if performance_data.get('performance_regression', False):
        st.error("⚠️ Performance regression detected! File 2 shows significantly slower response times.")

    # Slow endpoints
    slow_endpoints = performance_data.get('slow_endpoints', [])
    if slow_endpoints:
        st.markdown("#### 🐌 Slow Endpoints (>2s)")
        slow_df = pd.DataFrame(slow_endpoints)
        st.dataframe(slow_df, use_container_width=True)

def render_security_analysis(security_data: Dict[str, Any]):
    """Render security analysis section"""
    st.markdown("#### 🔒 Security Analysis")

    if 'error' in security_data:
        st.error(f"Security analysis failed: {security_data['error']}")
        return

    # Security issues summary
    insecure_count = len(security_data.get('insecure_requests', []))
    missing_headers_count = len(security_data.get('security_headers_missing', []))
    status_issues_count = len(security_data.get('status_code_security_issues', []))

    sec_col1, sec_col2, sec_col3 = st.columns(3)

    with sec_col1:
        st.metric("Insecure HTTP Requests", insecure_count,
                 delta=insecure_count if insecure_count > 0 else None,
                 delta_color="inverse" if insecure_count > 0 else "normal")

    with sec_col2:
        st.metric("Missing Security Headers", missing_headers_count,
                 delta=missing_headers_count if missing_headers_count > 0 else None,
                 delta_color="inverse" if missing_headers_count > 0 else "normal")

    with sec_col3:
        st.metric("Security Status Codes", status_issues_count,
                 delta=status_issues_count if status_issues_count > 0 else None,
                 delta_color="inverse" if status_issues_count > 0 else "normal")

    # Detailed security issues
    if insecure_count > 0:
        with st.expander(f"🔓 Insecure HTTP Requests ({insecure_count})"):
            for url in security_data['insecure_requests'][:20]:  # Limit display
                st.text(url)

    if missing_headers_count > 0:
        with st.expander(f"🛡️ Missing Security Headers ({missing_headers_count})"):
            for item in security_data['security_headers_missing'][:20]:
                st.text(f"{item['url']} - Missing: {', '.join(item['missing_headers'])}")

def render_detailed_differences(differences: List[Dict[str, Any]]):
    """Render detailed differences section"""
    st.markdown("#### 🔍 Detailed Differences")

    if not differences:
        st.info("No differences found between the files.")
        return

    # Filter options
    filter_col1, filter_col2, filter_col3 = st.columns(3)

    with filter_col1:
        show_only_changes = st.checkbox("Show only changed endpoints", value=True)

    with filter_col2:
        method_filter = st.selectbox("Filter by method",
                                   options=['All'] + list(set(d['method'] for d in differences)),
                                   index=0)

    with filter_col3:
        max_results = st.slider("Max results to show", 10, 500, 100)

    # Filter differences
    filtered_diffs = differences

    if show_only_changes:
        filtered_diffs = [d for d in filtered_diffs
                         if d.get('status_code_diff') or d.get('content_diff') or d.get('headers_diff')]

    if method_filter != 'All':
        filtered_diffs = [d for d in filtered_diffs if d['method'] == method_filter]

    filtered_diffs = filtered_diffs[:max_results]

    # Display differences
    for i, diff in enumerate(filtered_diffs):
        with st.expander(f"{diff['method']} {diff['url'][:80]}{'...' if len(diff['url']) > 80 else ''}"):
            diff_col1, diff_col2 = st.columns(2)

            with diff_col1:
                st.markdown("**Presence**")
                st.text(f"File 1: {'✅' if diff['file1_present'] else '❌'}")
                st.text(f"File 2: {'✅' if diff['file2_present'] else '❌'}")

                if diff.get('response_time_diff') is not None:
                    st.markdown("**Response Time**")
                    st.text(f"Difference: {diff['response_time_diff']:+.0f}ms")

            with diff_col2:
                if diff.get('status_code_diff'):
                    st.markdown("**Status Code Change**")
                    old_status, new_status = diff['status_code_diff']
                    st.text(f"{old_status} → {new_status}")

                if diff.get('size_diff') is not None:
                    st.markdown("**Size Difference**")
                    st.text(f"{diff['size_diff']:+,} bytes")

            # Content differences
            if diff.get('content_diff'):
                st.markdown("**Content Differences**")
                st.code(diff['content_diff'][:1000] + ('...' if len(diff['content_diff']) > 1000 else ''),
                        language='diff')

            # Header differences
            if diff.get('headers_diff'):
                st.markdown("**Header Differences**")
                headers_diff = diff['headers_diff']
                if 'added' in headers_diff and headers_diff['added']:
                    st.success(f"Added headers: {', '.join(headers_diff['added'].keys())}")
                if 'removed' in headers_diff and headers_diff['removed']:
                    st.error(f"Removed headers: {', '.join(headers_diff['removed'].keys())}")
                if 'changed' in headers_diff and headers_diff['changed']:
                    st.warning(f"Changed headers: {', '.join(headers_diff['changed'].keys())}")

def render_visual_comparison(result: ComparisonResult):
    """Render visual comparison charts"""
    st.markdown("#### 📊 Visual Comparison")

    # Summary pie chart
    fig_summary = go.Figure(data=[go.Pie(
        labels=['Common Endpoints', 'Unique to File 1', 'Unique to File 2'],
        values=[result.common_endpoints, result.unique_file1, result.unique_file2],
        hole=0.3,
        marker_colors=['#2E8B57', '#FF6B6B', '#4ECDC4']
    )])

    fig_summary.update_layout(
        title="Endpoint Distribution",
        showlegend=True,
        height=400
    )

    st.plotly_chart(fig_summary, use_container_width=True)

    # Changes breakdown
    changes_data = {
        'Change Type': ['Status Code Changes', 'Content Changes', 'Header Changes'],
        'Count': [result.status_code_changes, result.content_changes, result.header_changes]
    }

    fig_changes = px.bar(
        changes_data,
        x='Change Type',
        y='Count',
        title='Types of Changes Detected',
        color='Count',
        color_continuous_scale='Reds'
    )

    st.plotly_chart(fig_changes, use_container_width=True)

def render_export_options(report: Dict[str, Any]):
    """Render export options"""
    st.markdown("#### 📄 Export Options")

    export_col1, export_col2 = st.columns(2)

    with export_col1:
        # JSON export
        if st.button("📄 Export as JSON", use_container_width=True):
            json_str = json.dumps(report, indent=2, default=str)
            st.download_button(
                label="Download JSON Report",
                data=json_str,
                file_name=f"har_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

    with export_col2:
        # CSV export
        if st.button("📊 Export as CSV", use_container_width=True):
            # Convert detailed differences to DataFrame
            if report.get('detailed_differences'):
                df = pd.DataFrame(report['detailed_differences'])
                csv_str = df.to_csv(index=False)
                st.download_button(
                    label="Download CSV Report",
                    data=csv_str,
                    file_name=f"har_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

    # Report preview
    with st.expander("📋 Report Preview"):
        st.json(report)
