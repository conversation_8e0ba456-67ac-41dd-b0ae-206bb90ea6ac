#!/usr/bin/env python3
"""
Demo script showing the Chain-like Command System in action
Creates and executes example test chains
"""

from test_chain_system import <PERSON><PERSON>hainExecutor, ChainStepType
from appium_driver_enhanced import AppiumDriverEnhanced

def demo_basic_chain():
    """Demo a basic test chain"""
    print("🔗 DEMO: Basic Test Chain")
    print("=" * 30)
    
    # Initialize
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    # Create chain
    chain = executor.create_chain(
        name="Basic App Test",
        description="Launch app, take screenshot, verify text",
        app_package="com.android.settings"
    )
    
    # Add steps
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch Settings app", {"package_name": "com.android.settings"}),
        (ChainStepType.WAIT, "Wait for app to load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Capture initial screen", {}),
        (ChainStepType.VERIFY_TEXT, "Verify Settings header", {"text": "Settings", "case_sensitive": False}),
        (ChainStepType.PRESS_BUTTON, "Go to home", {"button": "home"}),
        (ChainStepType.TAKE_SCREENSHOT, "Capture final screen", {})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)
    
    print(f"✅ Created chain with {len(chain.steps)} steps")
    
    # Generate code
    generated_code = executor._generate_appium_code(chain)
    print(f"✅ Generated {len(generated_code)} characters of Appium code")
    
    return chain, generated_code

def demo_login_flow_chain():
    """Demo a login flow test chain"""
    print("\n🔐 DEMO: Login Flow Chain")
    print("=" * 30)
    
    # Initialize
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    # Create chain
    chain = executor.create_chain(
        name="Login Flow Test",
        description="Complete user login flow with validation",
        app_package="com.example.app"
    )
    
    # Add comprehensive login steps
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch target app", {"package_name": "com.example.app"}),
        (ChainStepType.WAIT, "Wait for app initialization", {"duration": 3}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: App launched", {}),
        (ChainStepType.VERIFY_TEXT, "Verify welcome screen", {"text": "Welcome", "case_sensitive": False}),
        (ChainStepType.TAP_ELEMENT, "Tap username field", {"locator_type": "id", "locator_value": "username_field"}),
        (ChainStepType.TYPE_TEXT, "Enter username", {"text": "<EMAIL>"}),
        (ChainStepType.TAP_ELEMENT, "Tap password field", {"locator_type": "id", "locator_value": "password_field"}),
        (ChainStepType.TYPE_TEXT, "Enter password", {"text": "securepassword123"}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: Credentials entered", {}),
        (ChainStepType.TAP_ELEMENT, "Tap login button", {"locator_type": "id", "locator_value": "login_button"}),
        (ChainStepType.WAIT, "Wait for login processing", {"duration": 5}),
        (ChainStepType.TAKE_SCREENSHOT, "Screenshot: After login", {}),
        (ChainStepType.VERIFY_TEXT, "Verify login success", {"text": "Dashboard", "case_sensitive": False}),
        (ChainStepType.VERIFY_ELEMENT, "Verify user profile", {"locator_type": "id", "locator_value": "user_profile"})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)
    
    print(f"✅ Created login chain with {len(chain.steps)} steps")
    
    # Generate code
    generated_code = executor._generate_appium_code(chain)
    print(f"✅ Generated {len(generated_code)} characters of Appium code")
    
    return chain, generated_code

def demo_ui_interaction_chain():
    """Demo a UI interaction test chain"""
    print("\n🎯 DEMO: UI Interaction Chain")
    print("=" * 30)
    
    # Initialize
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    # Create chain
    chain = executor.create_chain(
        name="UI Interaction Test",
        description="Test various UI interactions and gestures",
        app_package="com.android.settings"
    )
    
    # Add UI interaction steps
    steps = [
        (ChainStepType.LAUNCH_APP, "Launch Settings", {"package_name": "com.android.settings"}),
        (ChainStepType.WAIT, "Wait for load", {"duration": 2}),
        (ChainStepType.TAKE_SCREENSHOT, "Initial screenshot", {}),
        (ChainStepType.SCROLL, "Scroll down", {"direction": "down"}),
        (ChainStepType.TAKE_SCREENSHOT, "After scroll down", {}),
        (ChainStepType.SCROLL, "Scroll up", {"direction": "up"}),
        (ChainStepType.TAP_COORDINATES, "Tap center screen", {"x": 540, "y": 1200}),
        (ChainStepType.WAIT, "Wait after tap", {"duration": 1}),
        (ChainStepType.PRESS_BUTTON, "Press back", {"button": "back"}),
        (ChainStepType.SWIPE, "Swipe gesture", {"start_x": 500, "start_y": 1500, "end_x": 500, "end_y": 500, "duration": 1000}),
        (ChainStepType.TAKE_SCREENSHOT, "Final screenshot", {}),
        (ChainStepType.PRESS_BUTTON, "Return home", {"button": "home"})
    ]
    
    for step_type, description, parameters in steps:
        executor.add_step(step_type, description, parameters)
    
    print(f"✅ Created UI interaction chain with {len(chain.steps)} steps")
    
    # Generate code
    generated_code = executor._generate_appium_code(chain)
    print(f"✅ Generated {len(generated_code)} characters of Appium code")
    
    return chain, generated_code

def demo_export_import():
    """Demo chain export/import functionality"""
    print("\n💾 DEMO: Export/Import Functionality")
    print("=" * 35)
    
    # Create a chain
    driver = AppiumDriverEnhanced()
    executor = TestChainExecutor(driver)
    
    chain = executor.create_chain(
        name="Export Test Chain",
        description="Chain for testing export/import"
    )
    
    # Add some steps
    executor.add_step(ChainStepType.TAKE_SCREENSHOT, "Test screenshot", {})
    executor.add_step(ChainStepType.WAIT, "Test wait", {"duration": 1})
    
    # Export
    exported_data = executor.export_chain(chain)
    print(f"✅ Exported chain: {exported_data['name']}")
    print(f"   Steps: {len(exported_data['steps'])}")
    
    # Import
    new_executor = TestChainExecutor(driver)
    imported_chain = new_executor.import_chain(exported_data)
    print(f"✅ Imported chain: {imported_chain.name}")
    print(f"   Steps: {len(imported_chain.steps)}")
    
    return exported_data

def show_generated_code_sample(code):
    """Show a sample of generated code"""
    print("\n🔧 GENERATED APPIUM CODE SAMPLE:")
    print("=" * 40)
    
    lines = code.split('\n')
    sample_lines = lines[:20]  # Show first 20 lines
    
    for i, line in enumerate(sample_lines, 1):
        print(f"{i:2d}: {line}")
    
    if len(lines) > 20:
        print(f"... ({len(lines) - 20} more lines)")
    
    print(f"\nTotal lines: {len(lines)}")
    print(f"Total characters: {len(code)}")

def main():
    """Run all demos"""
    print("🎬 CHAIN-LIKE COMMAND SYSTEM DEMO")
    print("=" * 50)
    print("Demonstrating automated mobile testing capabilities")
    print("=" * 50)
    
    # Demo 1: Basic chain
    basic_chain, basic_code = demo_basic_chain()
    
    # Demo 2: Login flow
    login_chain, login_code = demo_login_flow_chain()
    
    # Demo 3: UI interactions
    ui_chain, ui_code = demo_ui_interaction_chain()
    
    # Demo 4: Export/Import
    exported_data = demo_export_import()
    
    # Show code sample
    show_generated_code_sample(login_code)
    
    # Summary
    print("\n" + "=" * 50)
    print("🎉 DEMO COMPLETE!")
    print("=" * 50)
    
    print("\n📊 DEMO SUMMARY:")
    print(f"   • Basic Chain: {len(basic_chain.steps)} steps")
    print(f"   • Login Chain: {len(login_chain.steps)} steps")
    print(f"   • UI Chain: {len(ui_chain.steps)} steps")
    print(f"   • Export/Import: ✅ Working")
    
    print("\n🌟 FEATURES DEMONSTRATED:")
    print("   ✅ Chain creation and step addition")
    print("   ✅ Multiple step types (11 different types)")
    print("   ✅ Automatic Appium code generation")
    print("   ✅ Chain export/import functionality")
    print("   ✅ Comprehensive test scenarios")
    
    print("\n🚀 READY FOR DASHBOARD USE:")
    print("   1. Go to: http://localhost:8501")
    print("   2. Navigate to: 📱 Emulator Testing")
    print("   3. Connect to: Pixel_6_API_34 (emulator-5554)")
    print("   4. Select: 🔗 Chain Testing")
    print("   5. Build your own test chains!")
    
    print("\n✨ Your chain-like command system is fully operational!")

if __name__ == "__main__":
    main()
