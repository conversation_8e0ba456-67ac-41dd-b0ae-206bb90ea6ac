"""
Emulator Testing Dashboard Tab
Android Studio emulator integration with Appium automation
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import json
import base64
import time
from typing import Dict, List, Any
from emulator_manager import EmulatorManager, EmulatorInstance
from appium_driver_enhanced import AppiumDriverEnhanced, ConnectionDiagnostics
from ai_test_agent import AITestAgent, TestScenario

def render_emulator_testing_tab():
    """Render the Emulator Testing dashboard tab"""
    
    # Professional header
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        text-align: center;
    ">
        <h1 style="color: white; margin: 0; font-size: 2.5rem; font-weight: 800;">
            📱 Emulator Testing
        </h1>
        <p style="color: rgba(255, 255, 255, 0.9); margin: 0.5rem 0 0 0; font-size: 1.2rem;">
            Android Studio emulator automation with AI-powered testing
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize components
    if 'emulator_manager' not in st.session_state:
        st.session_state['emulator_manager'] = EmulatorManager()
    
    if 'appium_driver' not in st.session_state:
        st.session_state['appium_driver'] = AppiumDriverEnhanced()

    if 'ai_agent' not in st.session_state:
        st.session_state['ai_agent'] = AITestAgent(st.session_state['appium_driver'])
    
    emulator_manager = st.session_state['emulator_manager']
    appium_driver = st.session_state['appium_driver']
    ai_agent = st.session_state['ai_agent']
    
    # Create tabs
    emulator_tabs = st.tabs([
        "🔌 Emulator Connection",
        "🤖 AI Test Agent",
        "📱 Live Control",
        "📊 Test Results",
        "📚 Test Library"
    ])
    
    # Tab 1: Emulator Connection
    with emulator_tabs[0]:
        st.markdown("## 🔌 Emulator Connection & Management")
        
        # Enhanced prerequisites check
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("### 🔧 Prerequisites")
            adb_available = emulator_manager.is_adb_available()
            emulator_available = emulator_manager.is_emulator_available()
            appium_server_running = appium_driver.check_appium_server()

            st.write(f"{'✅' if adb_available else '❌'} **ADB Available:** {adb_available}")
            st.write(f"{'✅' if emulator_available else '❌'} **Android Emulator:** {emulator_available}")
            st.write(f"{'✅' if appium_server_running else '❌'} **Appium Server:** {appium_server_running}")

            if not adb_available or not emulator_available:
                st.warning("⚠️ Please install Android SDK and ensure ADB/Emulator are in PATH")

            if not appium_server_running:
                st.error("❌ Appium server is not running on localhost:4723")
                st.info("💡 Start Appium server: `appium --port 4723`")

        with col2:
            st.markdown("### 🔄 Actions")
            if st.button("🔍 Discover Emulators", use_container_width=True):
                with st.spinner("Discovering emulators..."):
                    emulators = emulator_manager.discover_emulators()
                    st.session_state['discovered_emulators'] = emulators
                    st.success(f"Found {len(emulators)} emulator(s)")
                    st.rerun()

            if st.button("🔄 Refresh List", use_container_width=True):
                with st.spinner("Refreshing emulator status..."):
                    # Force refresh by clearing cache and rediscovering
                    emulator_manager.emulators.clear()
                    emulators = emulator_manager.discover_emulators()
                    st.session_state['discovered_emulators'] = emulators
                    st.success("Emulator list refreshed")
                    st.rerun()

            if st.button("🔧 Check Appium Server", use_container_width=True):
                if appium_driver.check_appium_server():
                    st.success("✅ Appium server is running")
                else:
                    st.error("❌ Appium server is not running")

        with col3:
            st.markdown("### 📋 Quick Setup")
            st.info("""
            **Setup Steps:**
            1. Install Android Studio
            2. Create an AVD (emulator)
            3. Install Appium: `npm install -g appium`
            4. Install driver: `appium driver install uiautomator2`
            5. Start server: `appium --port 4723`
            """)

            if st.button("📖 Setup Guide", use_container_width=True):
                st.session_state['show_setup_guide'] = True
        
        # Display discovered emulators
        if 'discovered_emulators' in st.session_state:
            st.markdown("### 📱 Available Emulators")
            
            emulators = st.session_state['discovered_emulators']
            
            if emulators:
                for emulator in emulators:
                    with st.expander(f"📱 {emulator.name} - {emulator.status.upper()}", expanded=emulator.running):
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.write(f"**Device:** {emulator.device_name}")
                            st.write(f"**API Level:** {emulator.api_level}")
                            st.write(f"**Resolution:** {emulator.resolution}")
                        
                        with col2:
                            st.write(f"**Architecture:** {emulator.arch}")
                            st.write(f"**Port:** {emulator.port if emulator.running else 'N/A'}")
                            st.write(f"**Status:** {'🟢 Running' if emulator.running else '🔴 Stopped'}")
                        
                        with col3:
                            if emulator.running:
                                if st.button(f"🛑 Stop {emulator.name}", key=f"stop_{emulator.name}"):
                                    if emulator_manager.stop_emulator(emulator.name):
                                        st.success(f"Stopped {emulator.name}")
                                        st.rerun()
                                
                                if st.button(f"🔗 Connect to {emulator.name}", key=f"connect_{emulator.name}"):
                                    with st.spinner(f"Connecting to {emulator.name}..."):
                                        success, diagnostics = appium_driver.connect_to_device(emulator.adb_id)

                                        if success:
                                            st.session_state['connected_emulator'] = emulator
                                            st.session_state['connection_diagnostics'] = diagnostics
                                            st.success(f"✅ Successfully connected to {emulator.name}")

                                            # Test basic functionality
                                            with st.spinner("Testing device functionality..."):
                                                functionality_tests = appium_driver.test_basic_functionality()
                                                st.session_state['functionality_tests'] = functionality_tests

                                            st.rerun()
                                        else:
                                            st.error(f"❌ Connection failed: {diagnostics.error_message}")

                                            # Show detailed diagnostics
                                            with st.expander("🔍 Connection Diagnostics", expanded=True):
                                                st.write("**Diagnostic Results:**")
                                                st.write(f"• Appium Server Running: {'✅' if diagnostics.appium_server_running else '❌'}")
                                                st.write(f"• ADB Available: {'✅' if diagnostics.adb_available else '❌'}")
                                                st.write(f"• Device Connected: {'✅' if diagnostics.device_connected else '❌'}")

                                                if diagnostics.suggestions:
                                                    st.write("**Suggested Solutions:**")
                                                    for suggestion in diagnostics.suggestions:
                                                        st.write(f"• {suggestion}")

                                            st.session_state['connection_diagnostics'] = diagnostics
                            else:
                                if st.button(f"▶️ Start {emulator.name}", key=f"start_{emulator.name}"):
                                    with st.spinner(f"Starting {emulator.name}..."):
                                        success = emulator_manager.start_emulator(emulator.name)
                                        if success:
                                            st.success(f"Started {emulator.name}")
                                            # Refresh the emulator list
                                            st.session_state['discovered_emulators'] = emulator_manager.discover_emulators()
                                            time.sleep(2)
                                            st.rerun()
                                        else:
                                            st.error(f"Failed to start {emulator.name}")
                                            st.info("💡 The emulator might already be running. Try clicking 'Discover Emulators' to refresh the list.")

                                            # Show ADB devices for troubleshooting
                                            adb_devices = emulator_manager.get_adb_devices()
                                            if adb_devices:
                                                st.write("**Connected ADB devices:**")
                                                for device_id, status in adb_devices.items():
                                                    st.write(f"• {device_id}: {status}")
                                            else:
                                                st.write("No ADB devices found. Try running: `adb devices`")
            else:
                st.info("No emulators found. Please create an AVD in Android Studio first.")

        # Connection Status Section
        if 'connected_emulator' in st.session_state:
            st.markdown("---")
            st.markdown("### 🔗 Connection Status")

            connected_emulator = st.session_state['connected_emulator']
            connection_status = appium_driver.get_connection_status()

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Connected Device", connected_emulator.name)
                st.metric("Connection Status", "🟢 Connected" if connection_status['connected'] else "🔴 Disconnected")

            with col2:
                if 'functionality_tests' in st.session_state:
                    tests = st.session_state['functionality_tests']
                    working_tests = sum(1 for test in tests.values() if test)
                    total_tests = len(tests)
                    st.metric("Functionality Tests", f"{working_tests}/{total_tests} Passed")

                if st.button("🔄 Test Connection", use_container_width=True):
                    with st.spinner("Testing connection..."):
                        if appium_driver.is_connected():
                            functionality_tests = appium_driver.test_basic_functionality()
                            st.session_state['functionality_tests'] = functionality_tests
                            st.success("Connection test completed")
                        else:
                            st.error("Device is not connected")
                        st.rerun()

            with col3:
                if st.button("📱 Take Screenshot", use_container_width=True):
                    screenshot = appium_driver.take_screenshot()
                    if screenshot:
                        st.session_state['test_screenshot'] = screenshot
                        st.success("Screenshot captured")
                    else:
                        st.error("Failed to capture screenshot")

                if st.button("🔌 Disconnect", use_container_width=True):
                    appium_driver.disconnect()
                    if 'connected_emulator' in st.session_state:
                        del st.session_state['connected_emulator']
                    if 'functionality_tests' in st.session_state:
                        del st.session_state['functionality_tests']
                    st.success("Disconnected from device")
                    st.rerun()

            # Show functionality test results
            if 'functionality_tests' in st.session_state:
                st.markdown("#### 🧪 Functionality Test Results")
                tests = st.session_state['functionality_tests']

                test_cols = st.columns(len(tests))
                for i, (test_name, result) in enumerate(tests.items()):
                    with test_cols[i]:
                        status = "✅" if result else "❌"
                        st.write(f"{status} **{test_name.replace('_', ' ').title()}**")

            # Show test screenshot if available
            if 'test_screenshot' in st.session_state:
                st.markdown("#### 📸 Device Screenshot")
                try:
                    screenshot_data = base64.b64decode(st.session_state['test_screenshot'])
                    st.image(screenshot_data, caption=f"Screenshot from {connected_emulator.name}", width=300)
                except Exception as e:
                    st.error(f"Failed to display screenshot: {str(e)}")

        # Troubleshooting Section
        if 'connection_diagnostics' in st.session_state:
            diagnostics = st.session_state['connection_diagnostics']
            if diagnostics.error_message:
                st.markdown("---")
                st.markdown("### 🔧 Troubleshooting")

                st.error(f"**Error:** {diagnostics.error_message}")

                if diagnostics.suggestions:
                    st.markdown("**Suggested Solutions:**")
                    for i, suggestion in enumerate(diagnostics.suggestions, 1):
                        st.write(f"{i}. {suggestion}")

        # Setup Guide
        if st.session_state.get('show_setup_guide', False):
            st.markdown("---")
            st.markdown("### 📖 Complete Setup Guide")

            with st.expander("🔧 Android SDK Setup", expanded=True):
                st.markdown("""
                **1. Install Android Studio:**
                - Download from: https://developer.android.com/studio
                - Install Android SDK and platform-tools

                **2. Set Environment Variables:**
                ```bash
                export ANDROID_HOME=$HOME/Library/Android/sdk  # macOS
                export PATH=$PATH:$ANDROID_HOME/platform-tools
                export PATH=$PATH:$ANDROID_HOME/emulator
                ```

                **3. Create Android Virtual Device (AVD):**
                - Open Android Studio
                - Go to Tools > AVD Manager
                - Create Virtual Device
                - Choose device and system image
                - Start the emulator
                """)

            with st.expander("🚀 Appium Setup", expanded=True):
                st.markdown("""
                **1. Install Node.js:**
                - Download from: https://nodejs.org/

                **2. Install Appium:**
                ```bash
                npm install -g appium
                ```

                **3. Install UiAutomator2 Driver:**
                ```bash
                appium driver install uiautomator2
                ```

                **4. Start Appium Server:**
                ```bash
                appium --port 4723
                ```

                **5. Verify Installation:**
                ```bash
                appium driver list
                ```
                """)

            with st.expander("🐛 Common Issues", expanded=True):
                st.markdown("""
                **Connection Refused:**
                - Ensure Appium server is running: `appium --port 4723`
                - Check if port 4723 is available

                **Device Not Found:**
                - Verify emulator is running: `adb devices`
                - Restart ADB: `adb kill-server && adb start-server`

                **Session Creation Failed:**
                - Install UiAutomator2 driver: `appium driver install uiautomator2`
                - Update Appium to latest version

                **Timeout Errors:**
                - Ensure emulator is fully booted
                - Increase connection timeout
                - Check emulator performance
                """)

            if st.button("❌ Close Setup Guide"):
                st.session_state['show_setup_guide'] = False
                st.rerun()
    
    # Tab 2: AI Test Agent
    with emulator_tabs[1]:
        st.markdown("## 🤖 AI-Powered Test Agent")
        
        if 'connected_emulator' not in st.session_state:
            st.warning("⚠️ Please connect to an emulator first")
            return
        
        connected_emulator = st.session_state['connected_emulator']
        st.success(f"🔗 Connected to: {connected_emulator.name}")
        
        # Test scenario creation
        st.markdown("### 📝 Create Test Scenario")
        
        col1, col2 = st.columns(2)
        
        with col1:
            scenario_name = st.text_input("Test Scenario Name", placeholder="Login Test")
            app_package = st.text_input("App Package", placeholder="com.example.app")
            app_activity = st.text_input("App Activity (Optional)", placeholder="MainActivity")
        
        with col2:
            scenario_description = st.text_area("Description", placeholder="Test user login functionality")
        
        # Natural language instructions
        st.markdown("### 💬 Natural Language Test Instructions")
        st.info("💡 **Examples:** 'Tap on login button', 'Type \"<EMAIL>\" in email field', 'Verify welcome message appears'")
        
        instructions = st.text_area(
            "Test Instructions (one per line)",
            placeholder="""Launch the app
Tap on email field
Type "<EMAIL>"
Tap on password field
Type "password123"
Tap on login button
Verify welcome message appears""",
            height=200
        )
        
        # Execute test scenario
        if st.button("🚀 Execute Test Scenario", type="primary", use_container_width=True):
            if scenario_name and app_package and instructions:
                instruction_list = [line.strip() for line in instructions.split('\n') if line.strip()]
                
                # Create scenario
                scenario = ai_agent.create_test_scenario(
                    name=scenario_name,
                    description=scenario_description,
                    instructions=instruction_list,
                    app_package=app_package,
                    app_activity=app_activity
                )
                
                # Execute scenario
                with st.spinner("🤖 AI Agent executing test scenario..."):
                    results = ai_agent.execute_scenario(scenario, connected_emulator.adb_id)
                    st.session_state['last_test_results'] = results
                
                # Display results
                if results['overall_success']:
                    st.success(f"✅ Test scenario completed successfully!")
                else:
                    st.error(f"❌ Test scenario failed")
                
                # Results summary
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Steps", results['total_steps'])
                
                with col2:
                    st.metric("Passed", results['passed_steps'])
                
                with col3:
                    st.metric("Failed", results['failed_steps'])
                
                with col4:
                    st.metric("Execution Time", f"{results['execution_time']:.1f}s")
            else:
                st.error("Please fill in all required fields")
    
    # Tab 3: Live Control
    with emulator_tabs[2]:
        st.markdown("## 📱 Live Emulator Control")
        
        if 'connected_emulator' not in st.session_state:
            st.warning("⚠️ Please connect to an emulator first")
            return
        
        connected_emulator = st.session_state['connected_emulator']
        
        # Live screenshot
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown("### 📸 Live Screen")
            
            if st.button("📷 Take Screenshot", use_container_width=True):
                screenshot = appium_driver.take_screenshot()
                if screenshot:
                    st.session_state['current_screenshot'] = screenshot
            
            if 'current_screenshot' in st.session_state:
                try:
                    screenshot_data = base64.b64decode(st.session_state['current_screenshot'])
                    st.image(screenshot_data, caption=f"Screenshot from {connected_emulator.name}", use_column_width=True)
                except Exception as e:
                    st.error(f"Failed to display screenshot: {str(e)}")
        
        with col2:
            st.markdown("### 🎮 Manual Controls")
            
            # Device controls
            col_a, col_b = st.columns(2)
            
            with col_a:
                if st.button("🏠 Home", use_container_width=True):
                    appium_driver.press_home()
                
                if st.button("⬅️ Back", use_container_width=True):
                    appium_driver.press_back()
            
            with col_b:
                if st.button("📋 Menu", use_container_width=True):
                    appium_driver.press_menu()
                
                if st.button("🔄 Rotate", use_container_width=True):
                    current_orientation = appium_driver.driver.orientation if appium_driver.driver else "PORTRAIT"
                    new_orientation = "LANDSCAPE" if current_orientation == "PORTRAIT" else "PORTRAIT"
                    appium_driver.rotate_device(new_orientation.lower())
            
            # Quick actions
            st.markdown("#### ⚡ Quick Actions")
            
            text_input = st.text_input("Type Text", placeholder="Hello World")
            if st.button("⌨️ Type", use_container_width=True) and text_input:
                appium_driver.type_text(text_input)
            
            # Coordinates input
            st.markdown("#### 📍 Tap Coordinates")
            coord_col1, coord_col2 = st.columns(2)
            
            with coord_col1:
                tap_x = st.number_input("X", min_value=0, max_value=2000, value=500)
            
            with coord_col2:
                tap_y = st.number_input("Y", min_value=0, max_value=2000, value=1000)
            
            if st.button("👆 Tap", use_container_width=True):
                appium_driver.tap(tap_x, tap_y)
            
            # Swipe controls
            st.markdown("#### 👆 Swipe Gestures")
            
            swipe_col1, swipe_col2 = st.columns(2)
            
            with swipe_col1:
                if st.button("⬆️ Swipe Up", use_container_width=True):
                    appium_driver.scroll_up()
                
                if st.button("⬇️ Swipe Down", use_container_width=True):
                    appium_driver.scroll_down()
            
            with swipe_col2:
                if st.button("⬅️ Swipe Left", use_container_width=True):
                    size = appium_driver.driver.get_window_size() if appium_driver.driver else {'width': 1080, 'height': 1920}
                    appium_driver.swipe(size['width'] * 0.8, size['height'] // 2, size['width'] * 0.2, size['height'] // 2)
                
                if st.button("➡️ Swipe Right", use_container_width=True):
                    size = appium_driver.driver.get_window_size() if appium_driver.driver else {'width': 1080, 'height': 1920}
                    appium_driver.swipe(size['width'] * 0.2, size['height'] // 2, size['width'] * 0.8, size['height'] // 2)
    
    # Tab 4: Test Results
    with emulator_tabs[3]:
        st.markdown("## 📊 Test Results & Analytics")
        
        if 'last_test_results' in st.session_state:
            results = st.session_state['last_test_results']
            
            # Overall results
            st.markdown("### 📈 Test Execution Summary")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Scenario", results['scenario_name'])
            
            with col2:
                success_rate = (results['passed_steps'] / results['total_steps'] * 100) if results['total_steps'] > 0 else 0
                st.metric("Success Rate", f"{success_rate:.1f}%")
            
            with col3:
                st.metric("Total Time", f"{results['execution_time']:.1f}s")
            
            with col4:
                status = "✅ PASSED" if results['overall_success'] else "❌ FAILED"
                st.metric("Status", status)
            
            # Step-by-step results
            st.markdown("### 📋 Step-by-Step Results")
            
            if results['steps']:
                step_data = []
                for i, step in enumerate(results['steps'], 1):
                    step_data.append({
                        'Step': i,
                        'Instruction': step['instruction'],
                        'Action': step['action_type'],
                        'Status': '✅ Pass' if step['success'] else '❌ Fail',
                        'Time (s)': f"{step['execution_time']:.2f}",
                        'Error': step.get('error_message', '')
                    })
                
                df_steps = pd.DataFrame(step_data)
                st.dataframe(df_steps, use_container_width=True)
                
                # Execution timeline
                st.markdown("### ⏱️ Execution Timeline")
                
                fig_timeline = go.Figure()
                
                step_numbers = list(range(1, len(results['steps']) + 1))
                execution_times = [step['execution_time'] for step in results['steps']]
                colors = ['green' if step['success'] else 'red' for step in results['steps']]
                
                fig_timeline.add_trace(go.Bar(
                    x=step_numbers,
                    y=execution_times,
                    marker_color=colors,
                    text=[f"{time:.2f}s" for time in execution_times],
                    textposition='auto'
                ))
                
                fig_timeline.update_layout(
                    title="Step Execution Times",
                    xaxis_title="Step Number",
                    yaxis_title="Execution Time (seconds)",
                    showlegend=False
                )
                
                st.plotly_chart(fig_timeline, use_container_width=True)
            
            # Export results
            st.markdown("### 💾 Export Results")
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📄 Download JSON Report", use_container_width=True):
                    report_json = json.dumps(results, indent=2, default=str)
                    st.download_button(
                        label="💾 Download Report",
                        data=report_json,
                        file_name=f"test_report_{results['scenario_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )
            
            with col2:
                if st.button("📊 Generate Summary Report", use_container_width=True):
                    summary = {
                        'test_summary': {
                            'scenario_name': results['scenario_name'],
                            'execution_date': results['start_time'],
                            'total_steps': results['total_steps'],
                            'passed_steps': results['passed_steps'],
                            'failed_steps': results['failed_steps'],
                            'success_rate': f"{(results['passed_steps'] / results['total_steps'] * 100):.1f}%",
                            'execution_time': f"{results['execution_time']:.1f}s",
                            'overall_result': 'PASSED' if results['overall_success'] else 'FAILED'
                        }
                    }
                    
                    summary_json = json.dumps(summary, indent=2)
                    st.download_button(
                        label="💾 Download Summary",
                        data=summary_json,
                        file_name=f"test_summary_{results['scenario_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )
        
        else:
            st.info("No test results available. Run a test scenario first.")
    
    # Tab 5: Test Library
    with emulator_tabs[4]:
        st.markdown("## 📚 Test Case Library")
        
        # Predefined test templates
        st.markdown("### 📋 Test Templates")
        
        templates = {
            "Login Test": [
                "Launch the app",
                "Tap on email field",
                "Type \"<EMAIL>\"",
                "Tap on password field", 
                "Type \"password123\"",
                "Tap on login button",
                "Verify welcome message appears"
            ],
            "Navigation Test": [
                "Launch the app",
                "Tap on menu button",
                "Tap on settings",
                "Verify settings page opens",
                "Press back button",
                "Verify main page appears"
            ],
            "Form Test": [
                "Launch the app",
                "Tap on create account",
                "Type \"John Doe\" in name field",
                "Type \"<EMAIL>\" in email field",
                "Type \"password123\" in password field",
                "Tap on submit button",
                "Verify success message"
            ]
        }
        
        selected_template = st.selectbox("Choose a template", list(templates.keys()))
        
        if selected_template:
            st.markdown(f"#### Template: {selected_template}")
            template_instructions = templates[selected_template]
            
            for i, instruction in enumerate(template_instructions, 1):
                st.write(f"{i}. {instruction}")
            
            if st.button(f"📋 Use {selected_template} Template", use_container_width=True):
                st.session_state['template_instructions'] = '\n'.join(template_instructions)
                st.success(f"Template loaded! Go to AI Test Agent tab to execute.")
        
        # Custom test case management
        st.markdown("### 💾 Save Custom Test Case")
        
        col1, col2 = st.columns(2)
        
        with col1:
            custom_name = st.text_input("Test Case Name", placeholder="My Custom Test")
        
        with col2:
            if st.button("💾 Save Current Test", use_container_width=True):
                if 'last_test_results' in st.session_state and custom_name:
                    # Save test case logic would go here
                    st.success(f"Test case '{custom_name}' saved!")
                else:
                    st.error("Please provide a name and run a test first")
        
        # Test execution history
        st.markdown("### 📊 Execution History")
        
        if 'test_history' not in st.session_state:
            st.session_state['test_history'] = []
        
        if st.session_state['test_history']:
            history_data = []
            for test in st.session_state['test_history']:
                history_data.append({
                    'Test Name': test['name'],
                    'Date': test['date'],
                    'Status': test['status'],
                    'Duration': test['duration'],
                    'Success Rate': test['success_rate']
                })
            
            df_history = pd.DataFrame(history_data)
            st.dataframe(df_history, use_container_width=True)
        else:
            st.info("No test execution history available.")
