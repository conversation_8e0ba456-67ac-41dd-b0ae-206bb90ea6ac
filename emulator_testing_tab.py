"""
Simplified Emulator Testing Tab - Core Appium Functionality
Focuses on essential Appium WebDriver operations through a clean dropdown interface
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import json
import base64
import time
from typing import Dict, List, Any
from emulator_manager import Emulator<PERSON>anager, EmulatorInstance
from appium_driver_enhanced import AppiumDriverEnhanced, ConnectionDiagnostics

def render_emulator_testing_tab():
    """Render the simplified Emulator Testing dashboard tab"""

    try:
        # Add professional styling
        st.markdown("""
        <style>
        .mobile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .prerequisite-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .prerequisite-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .status-check {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .operation-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .status-success {
            color: #28a745;
        }

        .status-error {
            color: #dc3545;
        }

        .status-warning {
            color: #ffc107;
        }
        </style>
        """, unsafe_allow_html=True)

        # Professional header
        st.markdown("""
        <div class="mobile-header">
            <h2 style="margin: 0; font-size: 2rem;">Mobile Testing Platform</h2>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">Core Appium WebDriver functionality for Android emulator automation</p>
        </div>
        """, unsafe_allow_html=True)

        # Initialize components with error handling
        try:
            if 'emulator_manager' not in st.session_state:
                st.session_state['emulator_manager'] = EmulatorManager()

            if 'appium_driver' not in st.session_state:
                st.session_state['appium_driver'] = AppiumDriverEnhanced()

            emulator_manager = st.session_state['emulator_manager']
            appium_driver = st.session_state['appium_driver']

            # Render simplified interface
            render_simplified_appium_interface(emulator_manager, appium_driver)

        except Exception as e:
            st.error(f"Component initialization failed: {str(e)}")
            st.info("Try refreshing the page or restarting the dashboard")

            # Show debug info
            with st.expander("Debug Information", expanded=False):
                st.code(f"Error: {str(e)}")
                import traceback
                st.code(traceback.format_exc())

    except Exception as e:
        st.error(f"Dashboard error: {str(e)}")
        st.info("Please refresh the page or restart the dashboard")


def render_simplified_appium_interface(emulator_manager, appium_driver):
    """Render simplified Appium interface with dropdown menu"""

    try:
        # Step 1: Prerequisites Check
        st.markdown("### System Prerequisites")
        col1, col2, col3 = st.columns(3)

        with col1:
            try:
                adb_available = emulator_manager.is_adb_available()
                status_class = "status-success" if adb_available else "status-error"
                status_icon = "✓" if adb_available else "✗"
                st.markdown(f"""
                <div class="prerequisite-card">
                    <div class="status-check {status_class}">{status_icon}</div>
                    <div><strong>ADB Available</strong></div>
                </div>
                """, unsafe_allow_html=True)
            except Exception as e:
                st.markdown("""
                <div class="prerequisite-card">
                    <div class="status-check status-error">✗</div>
                    <div><strong>ADB Available</strong></div>
                    <div style="font-size: 0.8rem; color: #6c757d;">Error checking</div>
                </div>
                """, unsafe_allow_html=True)
                adb_available = False

        with col2:
            try:
                emulator_available = emulator_manager.is_emulator_available()
                status_class = "status-success" if emulator_available else "status-error"
                status_icon = "✓" if emulator_available else "✗"
                st.markdown(f"""
                <div class="prerequisite-card">
                    <div class="status-check {status_class}">{status_icon}</div>
                    <div><strong>Android Emulator</strong></div>
                </div>
                """, unsafe_allow_html=True)
            except Exception as e:
                st.markdown("""
                <div class="prerequisite-card">
                    <div class="status-check status-error">✗</div>
                    <div><strong>Android Emulator</strong></div>
                    <div style="font-size: 0.8rem; color: #6c757d;">Error checking</div>
                </div>
                """, unsafe_allow_html=True)
                emulator_available = False

        with col3:
            try:
                appium_server_running = appium_driver.check_appium_server()
                status_class = "status-success" if appium_server_running else "status-error"
                status_icon = "✓" if appium_server_running else "✗"
                st.markdown(f"""
                <div class="prerequisite-card">
                    <div class="status-check {status_class}">{status_icon}</div>
                    <div><strong>Appium Server</strong></div>
                </div>
                """, unsafe_allow_html=True)
            except Exception as e:
                st.markdown("""
                <div class="prerequisite-card">
                    <div class="status-check status-error">✗</div>
                    <div><strong>Appium Server</strong></div>
                    <div style="font-size: 0.8rem; color: #6c757d;">Error checking</div>
                </div>
                """, unsafe_allow_html=True)
                appium_server_running = False

        if not all([adb_available, emulator_available, appium_server_running]):
            st.warning("Please ensure all prerequisites are met before proceeding")
            with st.expander("Setup Instructions", expanded=False):
                st.markdown("""
                **Quick Setup:**
                1. Install Android Studio and create an AVD
                2. Install Appium: `npm install -g appium`
                3. Install driver: `appium driver install uiautomator2`
                4. Start server: `appium --port 4723`
                5. Start your emulator from Android Studio
                """)
            return

        st.markdown("---")

        # Step 2: Emulator Discovery and Selection
        st.markdown("### Device Selection")

        col1, col2 = st.columns([2, 1])

        with col1:
            if st.button("Discover Devices", use_container_width=True):
                with st.spinner("Discovering emulators..."):
                    try:
                        emulators = emulator_manager.discover_emulators()
                        st.session_state['discovered_emulators'] = emulators
                        st.success(f"Found {len(emulators)} emulator(s)")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Discovery failed: {str(e)}")

        with col2:
            if st.button("Refresh", use_container_width=True):
                with st.spinner("Refreshing..."):
                    try:
                        emulator_manager.emulators.clear()
                        emulators = emulator_manager.discover_emulators()
                        st.session_state['discovered_emulators'] = emulators
                        st.rerun()
                    except Exception as e:
                        st.error(f"Refresh failed: {str(e)}")

        # Display available emulators
        if 'discovered_emulators' in st.session_state:
            emulators = st.session_state['discovered_emulators']
            running_emulators = [e for e in emulators if e.running]

            if running_emulators:
                emulator_names = [f"{e.name} ({e.adb_id})" for e in running_emulators]
                selected_emulator_name = st.selectbox(
                    "Select Running Emulator:",
                    emulator_names,
                    key="emulator_selector"
                )

                # Find selected emulator
                selected_emulator = None
                for e in running_emulators:
                    if f"{e.name} ({e.adb_id})" == selected_emulator_name:
                        selected_emulator = e
                        break

                if selected_emulator:
                    st.session_state['selected_emulator'] = selected_emulator
                    st.success(f"Selected: {selected_emulator.name}")
            else:
                st.warning("No running emulators found. Please start an emulator first.")
                return
        else:
            st.info("Click 'Discover Emulators' to find available emulators")
            return

        st.markdown("---")

        # Step 3: Choose Testing Mode
        if 'selected_emulator' in st.session_state:
            selected_emulator = st.session_state['selected_emulator']

            st.markdown("### Testing Mode")

            # Mode selection
            testing_mode = st.radio(
                "Choose testing approach:",
                ["Individual Operations", "Chain Testing"],
                horizontal=True
            )

            if testing_mode == "Individual Operations":
                render_individual_operations(selected_emulator, appium_driver)
            else:
                render_chain_testing_mode(appium_driver)

    except Exception as e:
        st.error(f"Interface error: {str(e)}")
        st.info("Try refreshing the page")
        with st.expander("Debug Information", expanded=False):
            import traceback
            st.code(traceback.format_exc())

def render_individual_operations(selected_emulator, appium_driver):
    """Render individual operations interface"""

    st.markdown("#### Core Appium Operations")

    # Dropdown menu for operations
    operation = st.selectbox(
        "Select Appium Operation:",
        [
            "Select an operation...",
            "Connect to Device",
            "Disconnect from Device",
            "Take Screenshot",
            "Get Device Info",
            "Get Page Source",
            "Press Home Button",
            "Press Back Button",
            "Press Menu Button",
            "Type Text",
            "Tap at Coordinates"
        ],
        key="operation_selector"
    )

    # Execute selected operation
    if operation != "Select an operation...":
        try:
            execute_appium_operation(operation, selected_emulator, appium_driver)
        except Exception as e:
            st.error(f"Operation failed: {str(e)}")
            with st.expander("Error Details", expanded=False):
                import traceback
                st.code(traceback.format_exc())

def render_chain_testing_mode(appium_driver):
    """Render chain testing interface"""

    # Import and render chain testing interface
    try:
        from chain_testing_interface import render_chain_testing_interface
        render_chain_testing_interface(appium_driver)
    except ImportError as e:
        st.error(f"Chain testing interface not available: {str(e)}")
        st.info("Make sure chain_testing_interface.py is available")
    except Exception as e:
        st.error(f"Interface error: {str(e)}")
        st.info("Try refreshing the page")
        with st.expander("Debug Information", expanded=False):
            import traceback
            st.code(traceback.format_exc())


def execute_appium_operation(operation, emulator, appium_driver):
    """Execute the selected Appium operation"""

    st.markdown(f"#### Executing: {operation}")

    if operation == "Connect to Device":
        if st.button("Connect", type="primary"):
            with st.spinner(f"Connecting to {emulator.name}..."):
                success, diagnostics = appium_driver.connect_to_device(emulator.adb_id)

                if success:
                    st.session_state['connected_emulator'] = emulator
                    st.success(f"Connected to {emulator.name}")

                    # Test basic functionality
                    tests = appium_driver.test_basic_functionality()
                    st.session_state['functionality_tests'] = tests

                    # Show test results
                    st.markdown("**Connection Tests:**")
                    for test_name, result in tests.items():
                        status = "✓" if result else "✗"
                        st.write(f"{status} {test_name.replace('_', ' ').title()}")
                else:
                    st.error(f"Connection failed: {diagnostics.error_message}")
                    if diagnostics.suggestions:
                        st.markdown("**Suggestions:**")
                        for suggestion in diagnostics.suggestions:
                            st.write(f"• {suggestion}")

    elif operation == "Disconnect from Device":
        if 'connected_emulator' in st.session_state:
            if st.button("Disconnect", type="secondary"):
                appium_driver.disconnect()
                if 'connected_emulator' in st.session_state:
                    del st.session_state['connected_emulator']
                if 'functionality_tests' in st.session_state:
                    del st.session_state['functionality_tests']
                st.success("Disconnected from device")
                st.rerun()
        else:
            st.warning("No device connected")

    # Add other operations here...
    # (The rest of the operations would be implemented similarly)

    # Show connection status if connected
    if 'connected_emulator' in st.session_state:
        st.markdown("---")
        st.markdown("### Connection Status")

        connected_emulator = st.session_state['connected_emulator']
        connection_status = appium_driver.get_connection_status()

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Connected Device", connected_emulator.name)

        with col2:
            status_text = "Connected" if connection_status['connected'] else "Disconnected"
            st.metric("Status", status_text)