"""
Simplified Emulator Testing Tab - Core Appium Functionality
Focuses on essential Appium WebDriver operations through a clean dropdown interface
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import json
import base64
import time
from typing import Dict, List, Any
from emulator_manager import EmulatorManager, EmulatorInstance
from appium_driver_enhanced import AppiumDriverEnhanced, ConnectionDiagnostics

def render_emulator_testing_tab():
    """Render the simplified Emulator Testing dashboard tab"""

    try:
        # Simple header
        st.markdown("## Mobile Testing")
        st.markdown("Core Appium WebDriver functionality for Android emulator automation")
        st.markdown("---")

        # Initialize components with error handling
        try:
            if 'emulator_manager' not in st.session_state:
                st.session_state['emulator_manager'] = EmulatorManager()

            if 'appium_driver' not in st.session_state:
                st.session_state['appium_driver'] = AppiumDriverEnhanced()

            emulator_manager = st.session_state['emulator_manager']
            appium_driver = st.session_state['appium_driver']

            # Render simplified interface
            render_simplified_appium_interface(emulator_manager, appium_driver)

        except Exception as e:
            st.error(f"❌ Component initialization failed: {str(e)}")
            st.info("💡 Try refreshing the page or restarting the dashboard")

            # Show debug info
            with st.expander("🐛 Debug Information", expanded=False):
                st.code(f"Error: {str(e)}")
                import traceback
                st.code(traceback.format_exc())

    except Exception as e:
        st.error(f"❌ Dashboard error: {str(e)}")
        st.info("💡 Please refresh the page or restart the dashboard")


def render_simplified_appium_interface(emulator_manager, appium_driver):
    """Render simplified Appium interface with dropdown menu"""

    try:
        # Step 1: Prerequisites Check
        st.markdown("### Prerequisites")
        col1, col2, col3 = st.columns(3)

        with col1:
            try:
                adb_available = emulator_manager.is_adb_available()
                status_icon = "✓" if adb_available else "✗"
                st.write(f"**{status_icon} ADB Available**")
            except Exception as e:
                st.write("**✗ ADB Available** (Error checking)")
                adb_available = False

        with col2:
            try:
                emulator_available = emulator_manager.is_emulator_available()
                status_icon = "✓" if emulator_available else "✗"
                st.write(f"**{status_icon} Android Emulator**")
            except Exception as e:
                st.write("**✗ Android Emulator** (Error checking)")
                emulator_available = False

        with col3:
            try:
                appium_server_running = appium_driver.check_appium_server()
                status_icon = "✓" if appium_server_running else "✗"
                st.write(f"**{status_icon} Appium Server**")
            except Exception as e:
                st.write("**✗ Appium Server** (Error checking)")
                appium_server_running = False

        if not all([adb_available, emulator_available, appium_server_running]):
            st.warning("Please ensure all prerequisites are met before proceeding")
            with st.expander("Setup Instructions", expanded=False):
                st.markdown("""
                **Quick Setup:**
                1. Install Android Studio and create an AVD
                2. Install Appium: `npm install -g appium`
                3. Install driver: `appium driver install uiautomator2`
                4. Start server: `appium --port 4723`
                5. Start your emulator from Android Studio
                """)
            return

        st.markdown("---")

        # Step 2: Emulator Discovery and Selection
        st.markdown("### Device Selection")

        col1, col2 = st.columns([2, 1])

        with col1:
            if st.button("Discover Devices", use_container_width=True):
                with st.spinner("Discovering emulators..."):
                    try:
                        emulators = emulator_manager.discover_emulators()
                        st.session_state['discovered_emulators'] = emulators
                        st.success(f"Found {len(emulators)} emulator(s)")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Discovery failed: {str(e)}")

        with col2:
            if st.button("Refresh", use_container_width=True):
                with st.spinner("Refreshing..."):
                    try:
                        emulator_manager.emulators.clear()
                        emulators = emulator_manager.discover_emulators()
                        st.session_state['discovered_emulators'] = emulators
                        st.rerun()
                    except Exception as e:
                        st.error(f"Refresh failed: {str(e)}")

        # Display available emulators
        if 'discovered_emulators' in st.session_state:
            emulators = st.session_state['discovered_emulators']
            running_emulators = [e for e in emulators if e.running]

            if running_emulators:
                emulator_names = [f"{e.name} ({e.adb_id})" for e in running_emulators]
                selected_emulator_name = st.selectbox(
                    "Select Running Emulator:",
                    emulator_names,
                    key="emulator_selector"
                )

                # Find selected emulator
                selected_emulator = None
                for e in running_emulators:
                    if f"{e.name} ({e.adb_id})" == selected_emulator_name:
                        selected_emulator = e
                        break

                if selected_emulator:
                    st.session_state['selected_emulator'] = selected_emulator
                    st.success(f"Selected: {selected_emulator.name}")
            else:
                st.warning("No running emulators found. Please start an emulator first.")
                return
        else:
            st.info("Click 'Discover Emulators' to find available emulators")
            return

        st.markdown("---")

        # Step 3: Choose Testing Mode
        if 'selected_emulator' in st.session_state:
            selected_emulator = st.session_state['selected_emulator']

            st.markdown("### Testing Mode")

            # Mode selection
            testing_mode = st.radio(
                "Choose testing approach:",
                ["Individual Operations", "Chain Testing"],
                horizontal=True
            )

            if testing_mode == "Individual Operations":
                render_individual_operations(selected_emulator, appium_driver)
            else:
                render_chain_testing_mode(appium_driver)

    except Exception as e:
        st.error(f"❌ Interface error: {str(e)}")
        st.info("💡 Try refreshing the page")
        with st.expander("🐛 Debug Information", expanded=False):
            import traceback
            st.code(traceback.format_exc())

def render_individual_operations(selected_emulator, appium_driver):
    """Render individual operations interface"""

    st.markdown("#### Core Appium Operations")

    # Dropdown menu for operations
    operation = st.selectbox(
        "Select Appium Operation:",
        [
            "Select an operation...",
            "Connect to Device",
            "Disconnect from Device",
            "Take Screenshot",
            "Get Device Info",
            "Get Page Source",
            "Press Home Button",
            "Press Back Button",
            "Press Menu Button",
            "Type Text",
            "Tap at Coordinates"
        ],
        key="operation_selector"
    )

    # Execute selected operation
    if operation != "Select an operation...":
        try:
            execute_appium_operation(operation, selected_emulator, appium_driver)
        except Exception as e:
            st.error(f"❌ Operation failed: {str(e)}")
            with st.expander("🐛 Error Details", expanded=False):
                import traceback
                st.code(traceback.format_exc())

def render_chain_testing_mode(appium_driver):
    """Render chain testing interface"""

    # Import and render chain testing interface
    try:
        from chain_testing_interface import render_chain_testing_interface
        render_chain_testing_interface(appium_driver)
    except ImportError as e:
        st.error(f"❌ Chain testing interface not available: {str(e)}")
        st.info("💡 Make sure chain_testing_interface.py is available")
    except Exception as e:
        st.error(f"❌ Interface error: {str(e)}")
        st.info("💡 Try refreshing the page")
        with st.expander("🐛 Debug Information", expanded=False):
            import traceback
            st.code(traceback.format_exc())


def execute_appium_operation(operation, emulator, appium_driver):
    """Execute the selected Appium operation"""
    
    st.markdown(f"#### 🔧 Executing: {operation}")
    
    if operation == "Connect to Device":
        if st.button("Connect", type="primary"):
            with st.spinner(f"Connecting to {emulator.name}..."):
                success, diagnostics = appium_driver.connect_to_device(emulator.adb_id)

                if success:
                    st.session_state['connected_emulator'] = emulator
                    st.success(f"Connected to {emulator.name}")

                    # Test basic functionality
                    tests = appium_driver.test_basic_functionality()
                    st.session_state['functionality_tests'] = tests

                    # Show test results
                    st.markdown("**Connection Tests:**")
                    for test_name, result in tests.items():
                        status = "✓" if result else "✗"
                        st.write(f"{status} {test_name.replace('_', ' ').title()}")
                else:
                    st.error(f"Connection failed: {diagnostics.error_message}")
                    if diagnostics.suggestions:
                        st.markdown("**Suggestions:**")
                        for suggestion in diagnostics.suggestions:
                            st.write(f"• {suggestion}")
    
    elif operation == "Disconnect from Device":
        if 'connected_emulator' in st.session_state:
            if st.button("Disconnect", type="secondary"):
                appium_driver.disconnect()
                if 'connected_emulator' in st.session_state:
                    del st.session_state['connected_emulator']
                if 'functionality_tests' in st.session_state:
                    del st.session_state['functionality_tests']
                st.success("Disconnected from device")
                st.rerun()
        else:
            st.warning("No device connected")
    
    elif operation == "Take Screenshot":
        if 'connected_emulator' in st.session_state:
            if st.button("Take Screenshot"):
                with st.spinner("Capturing screenshot..."):
                    screenshot = appium_driver.take_screenshot()
                    if screenshot:
                        st.session_state['current_screenshot'] = screenshot
                        st.success("Screenshot captured")

                        # Display screenshot
                        try:
                            screenshot_data = base64.b64decode(screenshot)
                            st.image(screenshot_data, caption=f"Screenshot from {emulator.name}", width=400)
                        except Exception as e:
                            st.error(f"Failed to display screenshot: {str(e)}")
                    else:
                        st.error("Failed to capture screenshot")
        else:
            st.warning("Please connect to device first")
    
    elif operation == "Get Device Info":
        if 'connected_emulator' in st.session_state:
            if st.button("Get Device Info"):
                with st.spinner("Getting device information..."):
                    if appium_driver.is_connected():
                        try:
                            # Get device information
                            device_info = {
                                "Device Name": emulator.device_name,
                                "API Level": emulator.api_level,
                                "Resolution": emulator.resolution,
                                "Architecture": emulator.arch,
                                "ADB ID": emulator.adb_id,
                                "Port": emulator.port
                            }

                            # Try to get additional info from driver
                            if appium_driver.driver:
                                try:
                                    window_size = appium_driver.driver.get_window_size()
                                    device_info["Window Size"] = f"{window_size['width']}x{window_size['height']}"

                                    orientation = appium_driver.driver.orientation
                                    device_info["Orientation"] = orientation
                                except:
                                    pass

                            st.success("Device information retrieved")

                            # Display device info
                            for key, value in device_info.items():
                                st.write(f"**{key}:** {value}")

                        except Exception as e:
                            st.error(f"Failed to get device info: {str(e)}")
                    else:
                        st.error("Device not connected")
        else:
            st.warning("Please connect to device first")
    
    elif operation == "Get Page Source":
        if 'connected_emulator' in st.session_state:
            if st.button("Get Page Source"):
                with st.spinner("Getting page source..."):
                    if appium_driver.is_connected():
                        try:
                            page_source = appium_driver.driver.page_source
                            st.success("Page source retrieved")

                            # Display page source in expandable section
                            with st.expander("Page Source XML", expanded=False):
                                st.code(page_source, language="xml")

                            # Show summary
                            lines = len(page_source.split('\n'))
                            chars = len(page_source)
                            st.write(f"**Summary:** {lines} lines, {chars} characters")

                        except Exception as e:
                            st.error(f"Failed to get page source: {str(e)}")
                    else:
                        st.error("Device not connected")
        else:
            st.warning("Please connect to device first")
    
    elif operation in ["Press Home Button", "Press Back Button", "Press Menu Button"]:
        if 'connected_emulator' in st.session_state:
            button_map = {
                "Press Home Button": ("Press Home", appium_driver.press_home),
                "Press Back Button": ("Press Back", appium_driver.press_back),
                "Press Menu Button": ("Press Menu", appium_driver.press_menu)
            }

            button_text, button_func = button_map[operation]

            if st.button(button_text):
                with st.spinner(f"Executing {operation.lower()}..."):
                    try:
                        button_func()
                        st.success(f"{operation} executed")
                    except Exception as e:
                        st.error(f"Failed to execute {operation.lower()}: {str(e)}")
        else:
            st.warning("Please connect to device first")
    
    elif operation == "Type Text":
        if 'connected_emulator' in st.session_state:
            text_input = st.text_input("Enter text to type:", placeholder="Hello World")

            if st.button("Type Text") and text_input:
                with st.spinner("Typing text..."):
                    try:
                        appium_driver.type_text(text_input)
                        st.success(f"Typed: '{text_input}'")
                    except Exception as e:
                        st.error(f"Failed to type text: {str(e)}")
        else:
            st.warning("Please connect to device first")
    
    elif operation == "Tap at Coordinates":
        if 'connected_emulator' in st.session_state:
            col1, col2 = st.columns(2)

            with col1:
                tap_x = st.number_input("X Coordinate", min_value=0, max_value=2000, value=500)

            with col2:
                tap_y = st.number_input("Y Coordinate", min_value=0, max_value=2000, value=1000)

            if st.button("Tap"):
                with st.spinner(f"Tapping at ({tap_x}, {tap_y})..."):
                    try:
                        appium_driver.tap(tap_x, tap_y)
                        st.success(f"Tapped at coordinates ({tap_x}, {tap_y})")
                    except Exception as e:
                        st.error(f"Failed to tap: {str(e)}")
        else:
            st.warning("Please connect to device first")
    
    # Show connection status if connected
    if 'connected_emulator' in st.session_state:
        st.markdown("---")
        st.markdown("### Connection Status")

        connected_emulator = st.session_state['connected_emulator']
        connection_status = appium_driver.get_connection_status()

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Connected Device", connected_emulator.name)

        with col2:
            status_text = "Connected" if connection_status['connected'] else "Disconnected"
            st.metric("Status", status_text)
