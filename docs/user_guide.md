# User Guide

This guide provides detailed instructions for using the Integrated Claude Log Analysis Dashboard.

## Table of Contents

- [Getting Started](#getting-started)
- [Dashboard Interface](#dashboard-interface)
- [Analyzing Log Files](#analyzing-log-files)
- [Using Templates](#using-templates)
- [View Modes](#view-modes)
- [Saving and Sharing Results](#saving-and-sharing-results)
- [Advanced Features](#advanced-features)
- [Example Workflows](#example-workflows)

## Getting Started

### First-Time Setup

1. Ensure you have your Anthropic API key ready
2. Launch the dashboard using the run script:
   ```bash
   ./run_integrated_dashboard.sh your-api-key-here
   ```
3. Open your browser to `http://localhost:8501`
4. Enter your API key in the sidebar if not provided via command line

### Dashboard Overview

When you first open the dashboard, you'll see:

- **Sidebar**: Contains settings, view mode options, and history management
- **Main Area**: Includes prompt input, file upload, and results display
- **Templates**: Quick-start templates for common analysis tasks

## Dashboard Interface

### Sidebar

The sidebar contains several sections:

#### API Key
- Enter your Anthropic API key here if not provided at startup
- The key is masked for security

#### Model Settings
- **Model Selection**: Choose which Claude model to use
  - claude-3-opus: Most powerful, best for detailed analysis
  - claude-3-sonnet: Good balance of performance and speed
  - claude-3-haiku: Fastest, good for simple analyses
- **Maximum Response Length**: Adjust the token limit for Claude's response

#### View Mode
- **Claude Only**: Shows only Claude's text analysis
- **Dashboard Only**: Shows only data visualizations
- **Split View**: Shows both Claude's analysis and visualizations

#### Display Settings
- **Dark Mode**: Toggle between light and dark themes

#### History
- **Clear History**: Reset all responses and uploaded data

### Main Area

The main area is divided into several sections:

#### Prompt Input
- **Templates**: Quick-start templates for common analysis tasks
- **Prompt Editor**: Text area for entering your prompt
- **File Upload**: Upload Charles Proxy log files (JSON format)
- **Submit Button**: Send your prompt to Claude for analysis

#### Results Display
- **Claude's Analysis**: Text response from Claude
- **Visualizations**: Interactive charts and graphs
- **Data Explorer**: Table view of the log data

## Analyzing Log Files

### Uploading Log Files

1. Click the "Upload a log file" button in the main area
2. Select a Charles Proxy log file in JSON format
3. Once uploaded, you'll see a summary of the file contents
4. Click "Append Log Data to Prompt" to include the data in your prompt

### Creating a Custom Prompt

You can create a custom prompt for log analysis:

1. Enter your prompt in the text area
2. Include specific questions or analysis requests
3. Upload a log file and append it to your prompt
4. Click "Submit to Claude" to get the analysis

### Understanding Results

The analysis results include:

- **Claude's Text Analysis**: Insights, patterns, and recommendations
- **Status Code Distribution**: Breakdown of HTTP status codes
- **Duration Analysis**: Request duration statistics and distribution
- **Host Analysis**: Top hosts and their performance metrics
- **Data Explorer**: Detailed table view of all log entries

## Using Templates

The dashboard includes several templates for common analysis tasks:

### Log Analysis Template
- Comprehensive analysis of log data
- Covers performance, errors, security, and optimization
- Good starting point for general log review

### Performance Analysis Template
- Focused on performance metrics
- Identifies slow requests and bottlenecks
- Suggests optimization strategies

### Error Analysis Template
- Concentrates on error patterns
- Identifies common error codes and their causes
- Suggests troubleshooting steps

### Security Analysis Template
- Examines security aspects of API usage
- Identifies potential vulnerabilities
- Suggests security improvements

To use a template:
1. Click the template button
2. Upload your log file
3. Append the log data to the prompt
4. Submit to Claude

## View Modes

The dashboard offers three view modes:

### Claude Only
- Shows only Claude's text analysis
- Good for focusing on AI insights
- Includes history of previous analyses

### Dashboard Only
- Shows only data visualizations
- Good for exploring the data visually
- Includes interactive filters and charts

### Split View
- Shows both Claude's analysis and visualizations
- Three tabs available:
  - **Claude's Analysis**: Text insights from Claude
  - **Log Visualization**: Interactive charts and graphs
  - **Combined View**: Key insights and metrics together

To switch between views:
1. Open the sidebar
2. Select your preferred view mode under "View Mode"

## Saving and Sharing Results

### Saving Claude's Response
1. In Claude's analysis view, click "Save Response"
2. The response will be saved as a Markdown file
3. The file includes both the prompt and Claude's analysis

### Copying to Clipboard
1. In Claude's analysis view, click "Copy to Clipboard"
2. Paste the content wherever needed

### Exporting Visualizations
Plotly charts can be exported:
1. Hover over a chart
2. Click the camera icon in the chart toolbar
3. The chart will be downloaded as a PNG image

## Advanced Features

### Filtering Data
In the dashboard view:
1. Use the filter controls above the visualizations
2. Filter by host, status code, or duration category
3. The visualizations will update automatically

### Viewing Raw Data
In the dashboard view:
1. Scroll down to the "Data Explorer" section
2. View the complete table of log entries
3. Sort columns by clicking column headers

### History Management
1. Previous analyses are stored in the session
2. Access them in the "Response History" section
3. Clear history using the "Clear History" button in the sidebar

## Example Workflows

### Troubleshooting API Errors

1. Upload your Charles Proxy log file
2. Select the "Error Analysis Template"
3. Submit to Claude
4. Switch to Split View
5. Examine the status code distribution chart
6. Filter for specific error codes (4xx or 5xx)
7. Review Claude's analysis for error patterns
8. Check the Data Explorer for specific error instances

### Performance Optimization

1. Upload your Charles Proxy log file
2. Select the "Performance Analysis Template"
3. Submit to Claude
4. Switch to Split View
5. Examine the duration distribution chart
6. Look for the 95th percentile line to identify outliers
7. Filter for slow requests (>1s)
8. Review Claude's suggestions for optimization
9. Check the "Average Duration by Host" chart to identify problematic hosts
